"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  FolderPlus, 
  Upload, 
  FileImage, 
  Trash2, 
  Edit3, 
  Eye,
  AlertCircle,
  CheckCircle,
  Clock,
  Archive
} from 'lucide-react';
import { useDropzone } from 'react-dropzone';
import { 
  ProjectResponse, 
  ProjectCreate, 
  ProjectPageResponse, 
  ProjectStatus,
  OCRStatus,
  SUPPORTED_LANGUAGES,
  SupportedLanguage
} from '@/lib/types';
import { apiClient } from '@/lib/api';

interface ProjectManagerProps {
  onProjectSelect?: (project: ProjectResponse) => void;
  onPageSelect?: (page: ProjectPageResponse) => void;
  selectedProject?: ProjectResponse;
  selectedPage?: ProjectPageResponse;
}

interface CreateProjectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onProjectCreated: (project: ProjectResponse) => void;
}

function CreateProjectDialog({ open, onOpenChange, onProjectCreated }: CreateProjectDialogProps) {
  const [formData, setFormData] = useState<ProjectCreate>({
    name: '',
    description: '',
    source_language: 'japanese',
    target_language: 'english',
  });
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsCreating(true);
    setError(null);

    try {
      const project = await apiClient.createProject(formData);
      onProjectCreated(project);
      onOpenChange(false);
      setFormData({
        name: '',
        description: '',
        source_language: 'japanese',
        target_language: 'english',
      });
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to create project');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Project</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Project Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter project name..."
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Input
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Enter project description..."
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Source Language</Label>
              <Select
                value={formData.source_language}
                onValueChange={(value) => setFormData(prev => ({ ...prev, source_language: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(SUPPORTED_LANGUAGES).map(([key, value]) => (
                    <SelectItem key={key} value={key}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Target Language</Label>
              <Select
                value={formData.target_language}
                onValueChange={(value) => setFormData(prev => ({ ...prev, target_language: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(SUPPORTED_LANGUAGES).map(([key, value]) => (
                    <SelectItem key={key} value={key}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex gap-2 pt-4">
            <Button type="submit" disabled={isCreating || !formData.name.trim()}>
              {isCreating ? 'Creating...' : 'Create Project'}
            </Button>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

function getStatusColor(status: ProjectStatus | OCRStatus): string {
  switch (status) {
    case ProjectStatus.COMPLETED:
    case OCRStatus.COMPLETED:
      return 'bg-green-100 text-green-800';
    case ProjectStatus.IN_PROGRESS:
    case OCRStatus.PROCESSING:
      return 'bg-blue-100 text-blue-800';
    case ProjectStatus.DRAFT:
    case OCRStatus.PENDING:
      return 'bg-gray-100 text-gray-800';
    case ProjectStatus.ARCHIVED:
    case OCRStatus.FAILED:
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

function getStatusIcon(status: ProjectStatus | OCRStatus) {
  switch (status) {
    case ProjectStatus.COMPLETED:
    case OCRStatus.COMPLETED:
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    case ProjectStatus.IN_PROGRESS:
    case OCRStatus.PROCESSING:
      return <Clock className="w-4 h-4 text-blue-500" />;
    case ProjectStatus.ARCHIVED:
      return <Archive className="w-4 h-4 text-red-500" />;
    case OCRStatus.FAILED:
      return <AlertCircle className="w-4 h-4 text-red-500" />;
    default:
      return <Clock className="w-4 h-4 text-gray-500" />;
  }
}

export default function ProjectManager({
  onProjectSelect,
  onPageSelect,
  selectedProject,
  selectedPage,
}: ProjectManagerProps) {
  const [projects, setProjects] = useState<ProjectResponse[]>([]);
  const [pages, setPages] = useState<ProjectPageResponse[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUploadingPage, setIsUploadingPage] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Load projects
  const loadProjects = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await apiClient.getProjects();
      setProjects(response.items);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to load projects');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load pages for selected project
  const loadPages = useCallback(async (projectId: string) => {
    try {
      const projectPages = await apiClient.getProjectPages(projectId);
      setPages(projectPages);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to load pages');
    }
  }, []);

  useEffect(() => {
    loadProjects();
  }, [loadProjects]);

  useEffect(() => {
    if (selectedProject) {
      loadPages(selectedProject.id);
    } else {
      setPages([]);
    }
  }, [selectedProject, loadPages]);

  // File upload handling
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (!selectedProject || acceptedFiles.length === 0) return;

    setIsUploadingPage(true);
    setUploadProgress(0);

    try {
      for (let i = 0; i < acceptedFiles.length; i++) {
        const file = acceptedFiles[i];
        const pageNumber = pages.length + i + 1;

        await apiClient.uploadPage(selectedProject.id, file, pageNumber);
        setUploadProgress(((i + 1) / acceptedFiles.length) * 100);
      }

      // Reload pages after upload
      await loadPages(selectedProject.id);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to upload pages');
    } finally {
      setIsUploadingPage(false);
      setUploadProgress(0);
    }
  }, [selectedProject, pages.length, loadPages]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp']
    },
    disabled: !selectedProject || isUploadingPage,
  });

  const handleProjectCreated = useCallback((project: ProjectResponse) => {
    setProjects(prev => [project, ...prev]);
    onProjectSelect?.(project);
  }, [onProjectSelect]);

  return (
    <div className="w-80 border-r bg-white flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-2">
          <h2 className="font-semibold text-lg">Projects</h2>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCreateDialogOpen(true)}
          >
            <FolderPlus className="w-4 h-4 mr-2" />
            New
          </Button>
        </div>
        {selectedProject && (
          <div className="text-sm text-gray-600">
            {selectedProject.name} • {pages.length} pages
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      )}

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {!selectedProject ? (
          /* Project List */
          <ScrollArea className="h-full">
            <div className="p-4 space-y-2">
              {isLoading ? (
                <div className="text-center py-8 text-gray-500">
                  Loading projects...
                </div>
              ) : projects.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No projects yet</p>
                  <p className="text-sm">Create your first project to get started</p>
                </div>
              ) : (
                projects.map((project) => (
                  <div
                    key={project.id}
                    className="p-3 border rounded-lg cursor-pointer hover:border-gray-300 transition-colors"
                    onClick={() => onProjectSelect?.(project)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-medium truncate">{project.name}</h3>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(project.status)}
                        <Badge className={getStatusColor(project.status)}>
                          {project.status}
                        </Badge>
                      </div>
                    </div>
                    {project.description && (
                      <p className="text-sm text-gray-600 mb-2 truncate">
                        {project.description}
                      </p>
                    )}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{project.page_count} pages</span>
                      <span>
                        {SUPPORTED_LANGUAGES[project.source_language as SupportedLanguage]} → {SUPPORTED_LANGUAGES[project.target_language as SupportedLanguage]}
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        ) : (
          /* Page List */
          <div className="flex flex-col h-full">
            {/* Back Button */}
            <div className="p-4 border-b">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onProjectSelect?.(undefined as any)}
                className="mb-2"
              >
                ← Back to Projects
              </Button>
              <h3 className="font-medium">{selectedProject.name}</h3>
            </div>

            {/* Upload Area */}
            <div className="p-4 border-b">
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors ${
                  isDragActive
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-300 hover:border-gray-400'
                } ${isUploadingPage ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                <input {...getInputProps()} />
                <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                {isUploadingPage ? (
                  <div className="space-y-2">
                    <p className="text-sm">Uploading pages...</p>
                    <Progress value={uploadProgress} className="w-full" />
                  </div>
                ) : isDragActive ? (
                  <p className="text-sm">Drop images here...</p>
                ) : (
                  <div>
                    <p className="text-sm">Drop manga pages here or click to browse</p>
                    <p className="text-xs text-gray-500 mt-1">
                      Supports PNG, JPG, GIF, WebP
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Pages List */}
            <ScrollArea className="flex-1">
              <div className="p-4 space-y-2">
                {pages.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <FileImage className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                    <p>No pages uploaded</p>
                    <p className="text-sm">Upload manga pages to get started</p>
                  </div>
                ) : (
                  pages.map((page) => (
                    <div
                      key={page.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedPage?.id === page.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => onPageSelect?.(page)}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <FileImage className="w-4 h-4 text-gray-400" />
                          <span className="font-medium">Page {page.page_number}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(page.ocr_status)}
                          <Badge className={getStatusColor(page.ocr_status)}>
                            {page.ocr_status}
                          </Badge>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 truncate mb-1">
                        {page.original_filename}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>
                          {page.image_width && page.image_height
                            ? `${page.image_width}×${page.image_height}`
                            : 'Unknown size'}
                        </span>
                        <span>{page.text_region_count} regions</span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </div>
        )}
      </div>

      {/* Create Project Dialog */}
      <CreateProjectDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onProjectCreated={handleProjectCreated}
      />
    </div>
  );
}
