"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Languages, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertCircle,
  ThumbsUp,
  ThumbsDown,
  Copy,
  Wand2
} from 'lucide-react';
import { 
  LLMProvider, 
  TranslationStatus, 
  TranslationJobResponse, 
  TranslationAlternativeResponse,
  TextRegionResponse,
  SUPPORTED_LANGUAGES,
  SupportedLanguage
} from '@/lib/types';
import { apiClient } from '@/lib/api';

interface TranslationWorkflowProps {
  selectedRegion?: TextRegionResponse;
  sourceLanguage?: string;
  targetLanguage?: string;
  onTranslationComplete?: (regionId: string, translation: string) => void;
  onJobUpdate?: (job: TranslationJobResponse) => void;
}

interface TranslationJobWithAlternatives extends TranslationJobResponse {
  alternatives?: TranslationAlternativeResponse[];
}

export default function TranslationWorkflow({
  selectedRegion,
  sourceLanguage = 'japanese',
  targetLanguage = 'english',
  onTranslationComplete,
  onJobUpdate,
}: TranslationWorkflowProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentJob, setCurrentJob] = useState<TranslationJobWithAlternatives | null>(null);
  const [provider, setProvider] = useState<LLMProvider>(LLMProvider.CLAUDE);
  const [customPrompt, setCustomPrompt] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [selectedAlternative, setSelectedAlternative] = useState<string | null>(null);

  // Reset state when region changes
  useEffect(() => {
    setCurrentJob(null);
    setError(null);
    setProgress(0);
    setSelectedAlternative(null);
  }, [selectedRegion?.id]);

  const startTranslation = useCallback(async () => {
    if (!selectedRegion?.original_text) {
      setError('No text to translate');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setProgress(0);

    try {
      // Start translation processing
      const job = await apiClient.processTranslation({
        text_region_id: selectedRegion.id,
        provider,
        source_language: sourceLanguage,
        target_language: targetLanguage,
        original_text: selectedRegion.original_text,
        custom_prompt: customPrompt || undefined,
      });

      setCurrentJob(job);
      onJobUpdate?.(job);

      // Poll for job completion
      const pollInterval = setInterval(async () => {
        try {
          const updatedJob = await apiClient.getTranslationJobDetail(job.id);
          setCurrentJob(updatedJob);
          onJobUpdate?.(updatedJob);

          // Update progress based on status
          switch (updatedJob.status) {
            case TranslationStatus.IN_PROGRESS:
              setProgress(50);
              break;
            case TranslationStatus.COMPLETED:
              setProgress(100);
              clearInterval(pollInterval);
              setIsProcessing(false);
              
              // Auto-select the main translation
              if (updatedJob.translated_text) {
                onTranslationComplete?.(selectedRegion.id, updatedJob.translated_text);
              }
              break;
            case TranslationStatus.FAILED:
              setProgress(0);
              clearInterval(pollInterval);
              setIsProcessing(false);
              setError(updatedJob.error_message || 'Translation failed');
              break;
          }
        } catch (err) {
          console.error('Error polling translation job:', err);
          clearInterval(pollInterval);
          setIsProcessing(false);
          setError('Failed to check translation status');
        }
      }, 2000); // Poll every 2 seconds

      // Set timeout to stop polling after 5 minutes
      setTimeout(() => {
        clearInterval(pollInterval);
        if (isProcessing) {
          setIsProcessing(false);
          setError('Translation processing timed out');
        }
      }, 300000);

    } catch (err: any) {
      setIsProcessing(false);
      setError(err.response?.data?.detail || 'Failed to start translation');
    }
  }, [selectedRegion, provider, sourceLanguage, targetLanguage, customPrompt, onTranslationComplete, onJobUpdate, isProcessing]);

  const selectAlternative = useCallback(async (alternativeId: string) => {
    if (!currentJob) return;

    try {
      await apiClient.selectTranslationAlternative(currentJob.id, alternativeId);
      setSelectedAlternative(alternativeId);
      
      // Find the selected alternative and apply it
      const alternative = currentJob.alternatives?.find(alt => alt.id === alternativeId);
      if (alternative && selectedRegion) {
        onTranslationComplete?.(selectedRegion.id, alternative.alternative_text);
      }
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to select alternative');
    }
  }, [currentJob, selectedRegion, onTranslationComplete]);

  const copyToClipboard = useCallback((text: string) => {
    navigator.clipboard.writeText(text);
  }, []);

  const getStatusIcon = (status: TranslationStatus) => {
    switch (status) {
      case TranslationStatus.COMPLETED:
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case TranslationStatus.FAILED:
        return <XCircle className="w-4 h-4 text-red-500" />;
      case TranslationStatus.IN_PROGRESS:
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: TranslationStatus) => {
    switch (status) {
      case TranslationStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case TranslationStatus.FAILED:
        return 'bg-red-100 text-red-800';
      case TranslationStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="w-80 border-l bg-white flex flex-col h-full">
      <div className="p-4 border-b">
        <h3 className="font-semibold text-lg flex items-center gap-2">
          <Languages className="w-5 h-5" />
          Translation
        </h3>
        <p className="text-sm text-gray-600">AI-powered text translation</p>
      </div>

      <Tabs defaultValue="translate" className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-2 mx-4 mt-2">
          <TabsTrigger value="translate">Translate</TabsTrigger>
          <TabsTrigger value="alternatives" disabled={!currentJob?.alternatives?.length}>
            Alternatives ({currentJob?.alternatives?.length || 0})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="translate" className="flex-1 mt-2">
          <div className="px-4 space-y-6">
            {/* Selected Region Info */}
            {selectedRegion ? (
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="font-medium mb-2">Selected Text Region</div>
                <div className="text-sm space-y-1">
                  <div>
                    <span className="text-gray-600">Original:</span>
                    <p className="font-mono bg-white p-2 rounded border mt-1">
                      {selectedRegion.original_text || 'No text detected'}
                    </p>
                  </div>
                  {selectedRegion.translated_text && (
                    <div>
                      <span className="text-gray-600">Current Translation:</span>
                      <p className="font-mono bg-white p-2 rounded border mt-1">
                        {selectedRegion.translated_text}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Select a text region to start translation
                </AlertDescription>
              </Alert>
            )}

            {/* Provider Selection */}
            <div className="space-y-2">
              <Label>LLM Provider</Label>
              <Select
                value={provider}
                onValueChange={(value) => setProvider(value as LLMProvider)}
                disabled={isProcessing}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={LLMProvider.CLAUDE}>Claude (Anthropic)</SelectItem>
                  <SelectItem value={LLMProvider.OPENAI}>OpenAI GPT</SelectItem>
                  <SelectItem value={LLMProvider.GEMINI}>Google Gemini</SelectItem>
                  <SelectItem value={LLMProvider.DEEPSEEK}>DeepSeek</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Language Settings */}
            <div className="grid grid-cols-2 gap-2">
              <div className="space-y-2">
                <Label>From</Label>
                <Select value={sourceLanguage} disabled>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(SUPPORTED_LANGUAGES).map(([key, value]) => (
                      <SelectItem key={key} value={key}>
                        {value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>To</Label>
                <Select value={targetLanguage} disabled>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(SUPPORTED_LANGUAGES).map(([key, value]) => (
                      <SelectItem key={key} value={key}>
                        {value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Custom Prompt */}
            <div className="space-y-2">
              <Label>Custom Prompt (Optional)</Label>
              <Textarea
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                placeholder="Enter custom instructions for translation..."
                disabled={isProcessing}
                rows={3}
              />
            </div>

            {/* Translate Button */}
            <Button
              onClick={startTranslation}
              disabled={!selectedRegion?.original_text || isProcessing}
              className="w-full"
            >
              {isProcessing ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Translating...
                </>
              ) : (
                <>
                  <Wand2 className="w-4 h-4 mr-2" />
                  Translate Text
                </>
              )}
            </Button>

            {/* Progress */}
            {isProcessing && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{progress}%</span>
                </div>
                <Progress value={progress} className="w-full" />
              </div>
            )}

            {/* Error Display */}
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Current Job Status */}
            {currentJob && (
              <div className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">Translation Job</h4>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(currentJob.status)}
                    <Badge className={getStatusColor(currentJob.status)}>
                      {currentJob.status}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Provider:</span>
                    <span>{currentJob.provider}</span>
                  </div>
                  {currentJob.confidence_score && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Confidence:</span>
                      <span>{Math.round(currentJob.confidence_score * 100)}%</span>
                    </div>
                  )}
                  {currentJob.processing_time_seconds && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Processing Time:</span>
                      <span>{currentJob.processing_time_seconds.toFixed(1)}s</span>
                    </div>
                  )}
                </div>

                {currentJob.translated_text && (
                  <div className="mt-3">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium">Translation:</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(currentJob.translated_text!)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                    <p className="font-mono bg-white p-2 rounded border text-sm">
                      {currentJob.translated_text}
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="alternatives" className="flex-1 mt-2">
          <ScrollArea className="h-full px-4">
            {currentJob?.alternatives && currentJob.alternatives.length > 0 ? (
              <div className="space-y-3">
                {currentJob.alternatives.map((alternative) => (
                  <div
                    key={alternative.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedAlternative === alternative.id || alternative.is_selected
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => selectAlternative(alternative.id)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          {alternative.provider}
                        </Badge>
                        {alternative.is_selected && (
                          <Badge className="bg-green-100 text-green-800">
                            Selected
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-1">
                        <span className="text-xs text-gray-500">
                          {Math.round(alternative.confidence_score * 100)}%
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            copyToClipboard(alternative.alternative_text);
                          }}
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm font-mono bg-white p-2 rounded border">
                      {alternative.alternative_text}
                    </p>
                    {alternative.quality_score && (
                      <div className="mt-2 text-xs text-gray-500">
                        Quality Score: {Math.round(alternative.quality_score * 100)}%
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>No alternative translations available</p>
                <p className="text-sm">Complete a translation to see alternatives</p>
              </div>
            )}
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  );
}
