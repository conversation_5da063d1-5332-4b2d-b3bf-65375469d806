"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Trash2, Edit3, Eye, EyeOff } from 'lucide-react';
import { TextRegionResponse, TextRegionType, TranslationStatus } from '@/lib/types';

interface TextRegionManagerProps {
  textRegions: TextRegionResponse[];
  selectedRegionId?: string;
  onRegionSelect?: (regionId: string) => void;
  onRegionUpdate?: (regionId: string, updates: Partial<TextRegionResponse>) => void;
  onRegionDelete?: (regionId: string) => void;
  onRegionToggleVisibility?: (regionId: string) => void;
}

interface RegionEditFormProps {
  region: TextRegionResponse;
  onUpdate: (updates: Partial<TextRegionResponse>) => void;
  onCancel: () => void;
}

function RegionEditForm({ region, onUpdate, onCancel }: RegionEditFormProps) {
  const [formData, setFormData] = useState({
    region_type: region.region_type,
    original_text: region.original_text || '',
    translated_text: region.translated_text || '',
    font_family: region.font_family || 'Arial',
    font_size: region.font_size || 16,
    font_color: region.font_color || '#000000',
    background_color: region.background_color || 'transparent',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdate(formData);
  };

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="region_type">Region Type</Label>
        <Select
          value={formData.region_type}
          onValueChange={(value) => handleChange('region_type', value as TextRegionType)}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={TextRegionType.SPEECH_BUBBLE}>Speech Bubble</SelectItem>
            <SelectItem value={TextRegionType.THOUGHT_BUBBLE}>Thought Bubble</SelectItem>
            <SelectItem value={TextRegionType.NARRATION}>Narration</SelectItem>
            <SelectItem value={TextRegionType.SOUND_EFFECT}>Sound Effect</SelectItem>
            <SelectItem value={TextRegionType.SIGN}>Sign</SelectItem>
            <SelectItem value={TextRegionType.OTHER}>Other</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="original_text">Original Text</Label>
        <Input
          id="original_text"
          value={formData.original_text}
          onChange={(e) => handleChange('original_text', e.target.value)}
          placeholder="Enter original text..."
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="translated_text">Translated Text</Label>
        <Input
          id="translated_text"
          value={formData.translated_text}
          onChange={(e) => handleChange('translated_text', e.target.value)}
          placeholder="Enter translated text..."
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="font_family">Font Family</Label>
          <Select
            value={formData.font_family}
            onValueChange={(value) => handleChange('font_family', value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Arial">Arial</SelectItem>
              <SelectItem value="Helvetica">Helvetica</SelectItem>
              <SelectItem value="Times New Roman">Times New Roman</SelectItem>
              <SelectItem value="Georgia">Georgia</SelectItem>
              <SelectItem value="Verdana">Verdana</SelectItem>
              <SelectItem value="Comic Sans MS">Comic Sans MS</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="font_size">Font Size</Label>
          <Input
            id="font_size"
            type="number"
            min="8"
            max="72"
            value={formData.font_size}
            onChange={(e) => handleChange('font_size', parseInt(e.target.value))}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="font_color">Font Color</Label>
          <div className="flex gap-2">
            <Input
              id="font_color"
              type="color"
              value={formData.font_color}
              onChange={(e) => handleChange('font_color', e.target.value)}
              className="w-16 h-10 p-1"
            />
            <Input
              value={formData.font_color}
              onChange={(e) => handleChange('font_color', e.target.value)}
              placeholder="#000000"
              className="flex-1"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="background_color">Background Color</Label>
          <div className="flex gap-2">
            <Input
              id="background_color"
              type="color"
              value={formData.background_color === 'transparent' ? '#ffffff' : formData.background_color}
              onChange={(e) => handleChange('background_color', e.target.value)}
              className="w-16 h-10 p-1"
            />
            <Input
              value={formData.background_color}
              onChange={(e) => handleChange('background_color', e.target.value)}
              placeholder="transparent"
              className="flex-1"
            />
          </div>
        </div>
      </div>

      <div className="flex gap-2 pt-4">
        <Button type="submit" size="sm">
          Save Changes
        </Button>
        <Button type="button" variant="outline" size="sm" onClick={onCancel}>
          Cancel
        </Button>
      </div>
    </form>
  );
}

function getRegionTypeColor(type: TextRegionType): string {
  switch (type) {
    case TextRegionType.SPEECH_BUBBLE:
      return 'bg-blue-100 text-blue-800';
    case TextRegionType.THOUGHT_BUBBLE:
      return 'bg-purple-100 text-purple-800';
    case TextRegionType.NARRATION:
      return 'bg-green-100 text-green-800';
    case TextRegionType.SOUND_EFFECT:
      return 'bg-orange-100 text-orange-800';
    case TextRegionType.SIGN:
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

function getStatusColor(status: TranslationStatus): string {
  switch (status) {
    case TranslationStatus.COMPLETED:
      return 'bg-green-100 text-green-800';
    case TranslationStatus.IN_PROGRESS:
      return 'bg-blue-100 text-blue-800';
    case TranslationStatus.FAILED:
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

export default function TextRegionManager({
  textRegions,
  selectedRegionId,
  onRegionSelect,
  onRegionUpdate,
  onRegionDelete,
  onRegionToggleVisibility,
}: TextRegionManagerProps) {
  const [editingRegionId, setEditingRegionId] = useState<string | null>(null);
  const [hiddenRegions, setHiddenRegions] = useState<Set<string>>(new Set());

  const handleEdit = (regionId: string) => {
    setEditingRegionId(regionId);
  };

  const handleUpdate = (regionId: string, updates: Partial<TextRegionResponse>) => {
    onRegionUpdate?.(regionId, updates);
    setEditingRegionId(null);
  };

  const handleToggleVisibility = (regionId: string) => {
    setHiddenRegions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(regionId)) {
        newSet.delete(regionId);
      } else {
        newSet.add(regionId);
      }
      return newSet;
    });
    onRegionToggleVisibility?.(regionId);
  };

  const editingRegion = editingRegionId ? textRegions.find(r => r.id === editingRegionId) : null;

  return (
    <div className="w-80 border-l bg-white flex flex-col h-full">
      <div className="p-4 border-b">
        <h3 className="font-semibold text-lg">Text Regions</h3>
        <p className="text-sm text-gray-600">{textRegions.length} regions</p>
      </div>

      <Tabs defaultValue="list" className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-2 mx-4 mt-2">
          <TabsTrigger value="list">List</TabsTrigger>
          <TabsTrigger value="edit" disabled={!editingRegionId}>Edit</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="flex-1 mt-2">
          <ScrollArea className="h-full px-4">
            <div className="space-y-2">
              {textRegions.map((region) => (
                <div
                  key={region.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedRegionId === region.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => onRegionSelect?.(region.id)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge className={getRegionTypeColor(region.region_type)}>
                        {region.region_type.replace('_', ' ')}
                      </Badge>
                      <Badge className={getStatusColor(region.translation_status)}>
                        {region.translation_status}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleVisibility(region.id);
                        }}
                      >
                        {hiddenRegions.has(region.id) ? (
                          <EyeOff className="w-4 h-4" />
                        ) : (
                          <Eye className="w-4 h-4" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEdit(region.id);
                        }}
                      >
                        <Edit3 className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onRegionDelete?.(region.id);
                        }}
                      >
                        <Trash2 className="w-4 h-4 text-red-500" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-1 text-sm">
                    {region.original_text && (
                      <div>
                        <span className="font-medium text-gray-600">Original:</span>
                        <p className="text-gray-800 truncate">{region.original_text}</p>
                      </div>
                    )}
                    {region.translated_text && (
                      <div>
                        <span className="font-medium text-gray-600">Translation:</span>
                        <p className="text-gray-800 truncate">{region.translated_text}</p>
                      </div>
                    )}
                    {region.confidence_score && (
                      <div className="text-xs text-gray-500">
                        Confidence: {Math.round(region.confidence_score * 100)}%
                      </div>
                    )}
                  </div>

                  <div className="mt-2 text-xs text-gray-500">
                    Position: ({Math.round(region.x * 100)}%, {Math.round(region.y * 100)}%)
                    Size: {Math.round(region.width * 100)}% × {Math.round(region.height * 100)}%
                  </div>
                </div>
              ))}

              {textRegions.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p>No text regions yet</p>
                  <p className="text-sm">Use the Text Region tool to create one</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="edit" className="flex-1 mt-2">
          <div className="px-4">
            {editingRegion ? (
              <RegionEditForm
                region={editingRegion}
                onUpdate={(updates) => handleUpdate(editingRegion.id, updates)}
                onCancel={() => setEditingRegionId(null)}
              />
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>Select a region to edit</p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
