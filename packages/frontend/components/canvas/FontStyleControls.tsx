"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Bold, 
  Italic, 
  Underline, 
  AlignLeft, 
  AlignCenter, 
  AlignRight, 
  Palette,
  Type,
  RotateCcw
} from 'lucide-react';

interface FontStyle {
  fontFamily: string;
  fontSize: number;
  fontWeight: 'normal' | 'bold';
  fontStyle: 'normal' | 'italic';
  textDecoration: 'none' | 'underline';
  textAlign: 'left' | 'center' | 'right';
  color: string;
  backgroundColor: string;
  lineHeight: number;
  letterSpacing: number;
}

interface FontStyleControlsProps {
  selectedStyle?: Partial<FontStyle>;
  onStyleChange?: (style: Partial<FontStyle>) => void;
  onApplyToSelected?: () => void;
  onResetStyle?: () => void;
}

const FONT_FAMILIES = [
  'Arial',
  'Helvetica',
  'Times New Roman',
  'Georgia',
  'Verdana',
  'Comic Sans MS',
  'Impact',
  'Trebuchet MS',
  'Courier New',
  'Lucida Console',
];

const FONT_SIZES = [8, 10, 12, 14, 16, 18, 20, 24, 28, 32, 36, 48, 60, 72];

const PRESET_COLORS = [
  '#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF',
  '#FFFF00', '#FF00FF', '#00FFFF', '#FFA500', '#800080',
  '#FFC0CB', '#A52A2A', '#808080', '#000080', '#008000',
];

const DEFAULT_STYLE: FontStyle = {
  fontFamily: 'Arial',
  fontSize: 16,
  fontWeight: 'normal',
  fontStyle: 'normal',
  textDecoration: 'none',
  textAlign: 'left',
  color: '#000000',
  backgroundColor: 'transparent',
  lineHeight: 1.2,
  letterSpacing: 0,
};

export default function FontStyleControls({
  selectedStyle = {},
  onStyleChange,
  onApplyToSelected,
  onResetStyle,
}: FontStyleControlsProps) {
  const [currentStyle, setCurrentStyle] = useState<FontStyle>({
    ...DEFAULT_STYLE,
    ...selectedStyle,
  });

  useEffect(() => {
    setCurrentStyle({
      ...DEFAULT_STYLE,
      ...selectedStyle,
    });
  }, [selectedStyle]);

  const handleStyleChange = (updates: Partial<FontStyle>) => {
    const newStyle = { ...currentStyle, ...updates };
    setCurrentStyle(newStyle);
    onStyleChange?.(updates);
  };

  const handleReset = () => {
    setCurrentStyle(DEFAULT_STYLE);
    onStyleChange?.(DEFAULT_STYLE);
    onResetStyle?.();
  };

  return (
    <div className="w-80 border-l bg-white flex flex-col h-full">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-lg flex items-center gap-2">
            <Type className="w-5 h-5" />
            Font Styles
          </h3>
          <Button variant="outline" size="sm" onClick={handleReset}>
            <RotateCcw className="w-4 h-4" />
            Reset
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Font Family */}
        <div className="space-y-2">
          <Label>Font Family</Label>
          <Select
            value={currentStyle.fontFamily}
            onValueChange={(value) => handleStyleChange({ fontFamily: value })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {FONT_FAMILIES.map((font) => (
                <SelectItem key={font} value={font} style={{ fontFamily: font }}>
                  {font}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Font Size */}
        <div className="space-y-2">
          <Label>Font Size</Label>
          <div className="flex gap-2">
            <Select
              value={currentStyle.fontSize.toString()}
              onValueChange={(value) => handleStyleChange({ fontSize: parseInt(value) })}
            >
              <SelectTrigger className="flex-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {FONT_SIZES.map((size) => (
                  <SelectItem key={size} value={size.toString()}>
                    {size}px
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input
              type="number"
              min="8"
              max="200"
              value={currentStyle.fontSize}
              onChange={(e) => handleStyleChange({ fontSize: parseInt(e.target.value) || 16 })}
              className="w-20"
            />
          </div>
          <Slider
            value={[currentStyle.fontSize]}
            onValueChange={([value]) => handleStyleChange({ fontSize: value })}
            min={8}
            max={72}
            step={1}
            className="mt-2"
          />
        </div>

        {/* Font Style Buttons */}
        <div className="space-y-2">
          <Label>Style</Label>
          <div className="flex gap-1">
            <Button
              variant={currentStyle.fontWeight === 'bold' ? 'default' : 'outline'}
              size="sm"
              onClick={() => 
                handleStyleChange({ 
                  fontWeight: currentStyle.fontWeight === 'bold' ? 'normal' : 'bold' 
                })
              }
            >
              <Bold className="w-4 h-4" />
            </Button>
            <Button
              variant={currentStyle.fontStyle === 'italic' ? 'default' : 'outline'}
              size="sm"
              onClick={() => 
                handleStyleChange({ 
                  fontStyle: currentStyle.fontStyle === 'italic' ? 'normal' : 'italic' 
                })
              }
            >
              <Italic className="w-4 h-4" />
            </Button>
            <Button
              variant={currentStyle.textDecoration === 'underline' ? 'default' : 'outline'}
              size="sm"
              onClick={() => 
                handleStyleChange({ 
                  textDecoration: currentStyle.textDecoration === 'underline' ? 'none' : 'underline' 
                })
              }
            >
              <Underline className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Text Alignment */}
        <div className="space-y-2">
          <Label>Alignment</Label>
          <div className="flex gap-1">
            <Button
              variant={currentStyle.textAlign === 'left' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleStyleChange({ textAlign: 'left' })}
            >
              <AlignLeft className="w-4 h-4" />
            </Button>
            <Button
              variant={currentStyle.textAlign === 'center' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleStyleChange({ textAlign: 'center' })}
            >
              <AlignCenter className="w-4 h-4" />
            </Button>
            <Button
              variant={currentStyle.textAlign === 'right' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleStyleChange({ textAlign: 'right' })}
            >
              <AlignRight className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <Separator />

        {/* Text Color */}
        <div className="space-y-2">
          <Label>Text Color</Label>
          <div className="flex gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-12 h-10 p-1"
                  style={{ backgroundColor: currentStyle.color }}
                >
                  <Palette className="w-4 h-4" style={{ color: currentStyle.color === '#000000' ? '#ffffff' : '#000000' }} />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-64">
                <div className="space-y-3">
                  <div className="flex gap-1 flex-wrap">
                    {PRESET_COLORS.map((color) => (
                      <button
                        key={color}
                        className="w-8 h-8 rounded border-2 border-gray-200 hover:border-gray-400"
                        style={{ backgroundColor: color }}
                        onClick={() => handleStyleChange({ color })}
                      />
                    ))}
                  </div>
                  <Input
                    type="color"
                    value={currentStyle.color}
                    onChange={(e) => handleStyleChange({ color: e.target.value })}
                    className="w-full h-10"
                  />
                </div>
              </PopoverContent>
            </Popover>
            <Input
              value={currentStyle.color}
              onChange={(e) => handleStyleChange({ color: e.target.value })}
              placeholder="#000000"
              className="flex-1"
            />
          </div>
        </div>

        {/* Background Color */}
        <div className="space-y-2">
          <Label>Background Color</Label>
          <div className="flex gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-12 h-10 p-1"
                  style={{ 
                    backgroundColor: currentStyle.backgroundColor === 'transparent' ? '#ffffff' : currentStyle.backgroundColor,
                    backgroundImage: currentStyle.backgroundColor === 'transparent' ? 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)' : 'none',
                    backgroundSize: currentStyle.backgroundColor === 'transparent' ? '8px 8px' : 'auto',
                    backgroundPosition: currentStyle.backgroundColor === 'transparent' ? '0 0, 0 4px, 4px -4px, -4px 0px' : 'auto',
                  }}
                >
                  <Palette className="w-4 h-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-64">
                <div className="space-y-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleStyleChange({ backgroundColor: 'transparent' })}
                    className="w-full"
                  >
                    Transparent
                  </Button>
                  <div className="flex gap-1 flex-wrap">
                    {PRESET_COLORS.map((color) => (
                      <button
                        key={color}
                        className="w-8 h-8 rounded border-2 border-gray-200 hover:border-gray-400"
                        style={{ backgroundColor: color }}
                        onClick={() => handleStyleChange({ backgroundColor: color })}
                      />
                    ))}
                  </div>
                  <Input
                    type="color"
                    value={currentStyle.backgroundColor === 'transparent' ? '#ffffff' : currentStyle.backgroundColor}
                    onChange={(e) => handleStyleChange({ backgroundColor: e.target.value })}
                    className="w-full h-10"
                  />
                </div>
              </PopoverContent>
            </Popover>
            <Input
              value={currentStyle.backgroundColor}
              onChange={(e) => handleStyleChange({ backgroundColor: e.target.value })}
              placeholder="transparent"
              className="flex-1"
            />
          </div>
        </div>

        <Separator />

        {/* Advanced Settings */}
        <div className="space-y-4">
          <Label>Advanced</Label>
          
          {/* Line Height */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm">Line Height</span>
              <Badge variant="outline">{currentStyle.lineHeight}</Badge>
            </div>
            <Slider
              value={[currentStyle.lineHeight]}
              onValueChange={([value]) => handleStyleChange({ lineHeight: value })}
              min={0.8}
              max={3}
              step={0.1}
            />
          </div>

          {/* Letter Spacing */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm">Letter Spacing</span>
              <Badge variant="outline">{currentStyle.letterSpacing}px</Badge>
            </div>
            <Slider
              value={[currentStyle.letterSpacing]}
              onValueChange={([value]) => handleStyleChange({ letterSpacing: value })}
              min={-5}
              max={10}
              step={0.5}
            />
          </div>
        </div>

        {/* Preview */}
        <div className="space-y-2">
          <Label>Preview</Label>
          <div className="p-4 border rounded-lg bg-gray-50">
            <div
              style={{
                fontFamily: currentStyle.fontFamily,
                fontSize: `${currentStyle.fontSize}px`,
                fontWeight: currentStyle.fontWeight,
                fontStyle: currentStyle.fontStyle,
                textDecoration: currentStyle.textDecoration,
                textAlign: currentStyle.textAlign,
                color: currentStyle.color,
                backgroundColor: currentStyle.backgroundColor === 'transparent' ? 'transparent' : currentStyle.backgroundColor,
                lineHeight: currentStyle.lineHeight,
                letterSpacing: `${currentStyle.letterSpacing}px`,
              }}
            >
              Sample Text
            </div>
          </div>
        </div>
      </div>

      {/* Apply Button */}
      <div className="p-4 border-t">
        <Button onClick={onApplyToSelected} className="w-full">
          Apply to Selected
        </Button>
      </div>
    </div>
  );
}
