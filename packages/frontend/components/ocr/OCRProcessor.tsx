"use client";

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  Scan, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertCircle,
  Settings
} from 'lucide-react';
import { 
  LLMProvider, 
  OCRStatus, 
  OCRJobResponse, 
  OCRResultResponse, 
  ProjectPageResponse,
  TextRegionResponse,
  TextRegionType
} from '@/lib/types';
import { apiClient } from '@/lib/api';

interface OCRProcessorProps {
  page?: ProjectPageResponse;
  onRegionsDetected?: (regions: Partial<TextRegionResponse>[]) => void;
  onJobUpdate?: (job: OCRJobResponse) => void;
}

interface OCRJobWithResults extends OCRJobResponse {
  results?: OCRResultResponse[];
}

export default function OCRProcessor({
  page,
  onRegionsDetected,
  onJobUpdate,
}: OCRProcessorProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentJob, setCurrentJob] = useState<OCRJobWithResults | null>(null);
  const [provider, setProvider] = useState<LLMProvider>(LLMProvider.CLAUDE);
  const [customPrompt, setCustomPrompt] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);

  const startOCRProcessing = useCallback(async () => {
    if (!page) {
      setError('No page selected');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setProgress(0);

    try {
      // Start OCR processing
      const job = await apiClient.processOCR({
        page_id: page.id,
        provider,
        custom_prompt: customPrompt || undefined,
      });

      setCurrentJob(job);
      onJobUpdate?.(job);

      // Poll for job completion
      const pollInterval = setInterval(async () => {
        try {
          const updatedJob = await apiClient.getOCRJobDetail(job.id);
          setCurrentJob(updatedJob);
          onJobUpdate?.(updatedJob);

          // Update progress based on status
          switch (updatedJob.status) {
            case OCRStatus.PROCESSING:
              setProgress(50);
              break;
            case OCRStatus.COMPLETED:
              setProgress(100);
              clearInterval(pollInterval);
              setIsProcessing(false);
              
              // Convert OCR results to text regions
              if (updatedJob.results) {
                const regions = updatedJob.results.map(result => ({
                  page_id: page.id,
                  region_type: result.region_type,
                  x: result.x,
                  y: result.y,
                  width: result.width,
                  height: result.height,
                  original_text: result.text_content,
                  confidence_score: result.confidence_score,
                }));
                onRegionsDetected?.(regions);
              }
              break;
            case OCRStatus.FAILED:
              setProgress(0);
              clearInterval(pollInterval);
              setIsProcessing(false);
              setError(updatedJob.error_message || 'OCR processing failed');
              break;
          }
        } catch (err) {
          console.error('Error polling OCR job:', err);
          clearInterval(pollInterval);
          setIsProcessing(false);
          setError('Failed to check OCR status');
        }
      }, 2000); // Poll every 2 seconds

      // Set timeout to stop polling after 5 minutes
      setTimeout(() => {
        clearInterval(pollInterval);
        if (isProcessing) {
          setIsProcessing(false);
          setError('OCR processing timed out');
        }
      }, 300000);

    } catch (err: any) {
      setIsProcessing(false);
      setError(err.response?.data?.detail || 'Failed to start OCR processing');
    }
  }, [page, provider, customPrompt, onRegionsDetected, onJobUpdate, isProcessing]);

  const retryOCRJob = useCallback(async () => {
    if (!currentJob) return;

    setIsProcessing(true);
    setError(null);

    try {
      const retriedJob = await apiClient.retryOCRJob(currentJob.id);
      setCurrentJob(retriedJob);
      onJobUpdate?.(retriedJob);
      
      // Start polling again
      startOCRProcessing();
    } catch (err: any) {
      setIsProcessing(false);
      setError(err.response?.data?.detail || 'Failed to retry OCR job');
    }
  }, [currentJob, onJobUpdate, startOCRProcessing]);

  const getStatusIcon = (status: OCRStatus) => {
    switch (status) {
      case OCRStatus.COMPLETED:
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case OCRStatus.FAILED:
        return <XCircle className="w-4 h-4 text-red-500" />;
      case OCRStatus.PROCESSING:
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: OCRStatus) => {
    switch (status) {
      case OCRStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case OCRStatus.FAILED:
        return 'bg-red-100 text-red-800';
      case OCRStatus.PROCESSING:
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="w-80 border-l bg-white flex flex-col h-full">
      <div className="p-4 border-b">
        <h3 className="font-semibold text-lg flex items-center gap-2">
          <Scan className="w-5 h-5" />
          OCR Processing
        </h3>
        <p className="text-sm text-gray-600">Automatic text detection</p>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Provider Selection */}
        <div className="space-y-2">
          <Label>LLM Provider</Label>
          <Select
            value={provider}
            onValueChange={(value) => setProvider(value as LLMProvider)}
            disabled={isProcessing}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={LLMProvider.CLAUDE}>Claude (Anthropic)</SelectItem>
              <SelectItem value={LLMProvider.OPENAI}>OpenAI GPT</SelectItem>
              <SelectItem value={LLMProvider.GEMINI}>Google Gemini</SelectItem>
              <SelectItem value={LLMProvider.DEEPSEEK}>DeepSeek</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Custom Prompt */}
        <div className="space-y-2">
          <Label>Custom Prompt (Optional)</Label>
          <Textarea
            value={customPrompt}
            onChange={(e) => setCustomPrompt(e.target.value)}
            placeholder="Enter custom instructions for OCR processing..."
            disabled={isProcessing}
            rows={3}
          />
          <p className="text-xs text-gray-500">
            Leave empty to use default OCR prompt
          </p>
        </div>

        {/* Process Button */}
        <Button
          onClick={startOCRProcessing}
          disabled={!page || isProcessing}
          className="w-full"
        >
          {isProcessing ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <Scan className="w-4 h-4 mr-2" />
              Start OCR Processing
            </>
          )}
        </Button>

        {/* Progress */}
        {isProcessing && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{progress}%</span>
            </div>
            <Progress value={progress} className="w-full" />
          </div>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Current Job Status */}
        {currentJob && (
          <div className="space-y-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium">Current Job</h4>
                <div className="flex items-center gap-2">
                  {getStatusIcon(currentJob.status)}
                  <Badge className={getStatusColor(currentJob.status)}>
                    {currentJob.status}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Provider:</span>
                  <span>{currentJob.provider}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Regions Detected:</span>
                  <span>{currentJob.total_regions_detected}</span>
                </div>
                {currentJob.average_confidence && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Avg Confidence:</span>
                    <span>{Math.round(currentJob.average_confidence * 100)}%</span>
                  </div>
                )}
                {currentJob.processing_time_seconds && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Processing Time:</span>
                    <span>{currentJob.processing_time_seconds.toFixed(1)}s</span>
                  </div>
                )}
                {currentJob.retry_count > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Retries:</span>
                    <span>{currentJob.retry_count}</span>
                  </div>
                )}
              </div>

              {currentJob.status === OCRStatus.FAILED && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={retryOCRJob}
                  disabled={isProcessing}
                  className="w-full mt-3"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Retry OCR
                </Button>
              )}
            </div>

            {/* Results Summary */}
            {currentJob.results && currentJob.results.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Detected Regions</h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {currentJob.results.map((result, index) => (
                    <div key={result.id} className="p-2 border rounded text-sm">
                      <div className="flex items-center justify-between mb-1">
                        <Badge variant="outline" className="text-xs">
                          {result.region_type.replace('_', ' ')}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {Math.round(result.confidence_score * 100)}%
                        </span>
                      </div>
                      <p className="text-gray-800 truncate">
                        {result.text_content}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Page Info */}
        {page && (
          <div className="p-3 bg-gray-50 rounded-lg text-sm">
            <div className="font-medium mb-1">Current Page</div>
            <div className="text-gray-600">
              Page {page.page_number} - {page.original_filename}
            </div>
            <div className="text-gray-600">
              OCR Status: <Badge className={getStatusColor(page.ocr_status)}>
                {page.ocr_status}
              </Badge>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
