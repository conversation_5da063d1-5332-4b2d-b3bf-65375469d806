# Ho-Trans Frontend

A Next.js-based frontend application for the Ho-Trans manga translation tool. This application provides a photo editor-style interface for translating manga pages using AI-powered OCR and translation services.

## Features

- **Project Management**: Create and manage manga translation projects
- **Canvas Editor**: Photo editor-style interface for manga page editing
- **Text Region Management**: Create, edit, and manage text regions on manga pages
- **OCR Integration**: AI-powered text detection using multiple LLM providers
- **Translation Workflow**: Translate text with alternative suggestions
- **Font Styling**: Comprehensive text styling controls
- **Export Functionality**: Export translated manga pages

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **UI Components**: Radix UI with Tailwind CSS
- **Canvas**: HTML5 Canvas (simplified implementation)
- **State Management**: React hooks
- **API Client**: Axios for backend communication
- **TypeScript**: Full type safety

## Getting Started

### Prerequisites

- Node.js 18+ or Bun
- Running Ho-Trans backend (see backend README)

### Installation

```bash
# Install dependencies
bun install

# Set up environment variables
cp .env.local.example .env.local
# Edit .env.local with your backend URL
```

### Development

```bash
# Start development server
bun run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

### Building

```bash
# Build for production
bun run build

# Start production server
bun run start
```

## Project Structure

```
packages/frontend/
├── app/                    # Next.js app directory
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Main application page
├── components/            # React components
│   ├── canvas/           # Canvas editor components
│   ├── ocr/              # OCR processing components
│   ├── project/          # Project management components
│   ├── translation/      # Translation workflow components
│   └── ui/               # Reusable UI components
├── lib/                  # Utilities and configurations
│   ├── api.ts           # API client
│   ├── types.ts         # TypeScript type definitions
│   └── utils.ts         # Utility functions
└── public/              # Static assets
```

## Key Components

### Canvas Editor
- HTML5 canvas-based image editor
- Zoom, pan, and drawing tools
- Text region creation and management
- Image overlay and export functionality

### Project Manager
- Project creation and listing
- Page upload with drag-and-drop
- Project navigation and organization

### OCR Processor
- Integration with backend OCR services
- Multiple LLM provider support
- Real-time processing status
- Automatic text region creation

### Translation Workflow
- Text translation with multiple providers
- Alternative translation suggestions
- Translation selection and management

### Font Style Controls
- Comprehensive font styling options
- Color picker and font selection
- Real-time preview
- Style application to selected regions

## API Integration

The frontend communicates with the Ho-Trans backend through a REST API:

- **Projects**: CRUD operations for manga projects
- **Pages**: Upload and manage manga pages
- **Text Regions**: Create and edit text regions
- **OCR**: Process images for text detection
- **Translation**: Translate text with AI providers

## Environment Variables

```bash
# Backend API URL
NEXT_PUBLIC_API_URL=http://localhost:8000
```

## Browser Support

- Chrome/Chromium 90+
- Firefox 88+
- Safari 14+
- Edge 90+

Minimum screen width: 800px (as specified in requirements)

## Development Notes

- Uses TypeScript for type safety
- Follows React best practices with hooks
- Responsive design with Tailwind CSS
- Error handling and loading states
- Optimized for manga translation workflows

## Contributing

1. Follow the existing code style and patterns
2. Add TypeScript types for new features
3. Test components thoroughly
4. Update documentation as needed

## License

Part of the Ho-Trans project. See main project LICENSE for details.
