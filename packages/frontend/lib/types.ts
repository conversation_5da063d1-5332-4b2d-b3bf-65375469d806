/**
 * TypeScript types for ho-trans frontend application
 * Based on backend Pydantic schemas
 */

// Enums from backend constants
export enum LLMProvider {
  CLAUDE = "claude",
  OPENAI = "openai",
  GEMINI = "gemini",
  DEEPSEEK = "deepseek",
}

export enum TranslationStatus {
  PENDING = "pending",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  FAILED = "failed",
}

export enum ProjectStatus {
  DRAFT = "draft",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  ARCHIVED = "archived",
}

export enum TextRegionType {
  SPEECH_BUBBLE = "speech_bubble",
  THOUGHT_BUBBLE = "thought_bubble",
  NARRATION = "narration",
  SOUND_EFFECT = "sound_effect",
  SIGN = "sign",
  OTHER = "other",
}

export enum OCRStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed",
}

// Base schema interface
export interface BaseSchema {
  id: string;
  created_at: string;
  updated_at: string;
}

// Project types
export interface ProjectCreate {
  name: string;
  description?: string;
  source_language?: string;
  target_language?: string;
}

export interface ProjectUpdate {
  name?: string;
  description?: string;
  status?: ProjectStatus;
  source_language?: string;
  target_language?: string;
}

export interface ProjectResponse extends BaseSchema {
  name: string;
  description?: string;
  status: ProjectStatus;
  source_language: string;
  target_language: string;
  page_count: number;
}

// Project Page types
export interface ProjectPageCreate {
  page_number: number;
  original_filename: string;
}

export interface ProjectPageResponse extends BaseSchema {
  project_id: string;
  page_number: number;
  original_filename: string;
  file_path: string;
  file_size: number;
  image_width?: number;
  image_height?: number;
  ocr_status: OCRStatus;
  text_region_count: number;
}

// Text Region types
export interface TextRegionCreate {
  page_id: string;
  region_type: TextRegionType;
  x: number;
  y: number;
  width: number;
  height: number;
  original_text?: string;
  confidence_score?: number;
}

export interface TextRegionUpdate {
  region_type?: TextRegionType;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  original_text?: string;
  confidence_score?: number;
  translated_text?: string;
  translation_status?: TranslationStatus;
  font_family?: string;
  font_size?: number;
  font_color?: string;
  background_color?: string;
}

export interface TextRegionResponse extends BaseSchema {
  page_id: string;
  region_type: TextRegionType;
  x: number;
  y: number;
  width: number;
  height: number;
  original_text?: string;
  confidence_score?: number;
  translated_text?: string;
  translation_status: TranslationStatus;
  font_family?: string;
  font_size?: number;
  font_color?: string;
  background_color?: string;
}

// OCR types
export interface OCRJobCreate {
  page_id: string;
  provider?: LLMProvider;
  custom_prompt?: string;
}

export interface OCRJobResponse extends BaseSchema {
  page_id: string;
  status: OCRStatus;
  provider: LLMProvider;
  prompt_used?: string;
  processing_time_seconds?: number;
  error_message?: string;
  retry_count: number;
  total_regions_detected: number;
  average_confidence?: number;
}

export interface OCRResultResponse extends BaseSchema {
  job_id: string;
  region_type: TextRegionType;
  x: number;
  y: number;
  width: number;
  height: number;
  text_content: string;
  confidence_score: number;
  processing_metadata?: Record<string, any>;
}

export interface OCRProcessRequest {
  page_id: string;
  provider?: LLMProvider;
  custom_prompt?: string;
}

export interface OCRJobDetailResponse extends OCRJobResponse {
  results: OCRResultResponse[];
}

// Translation types
export interface TranslationJobCreate {
  text_region_id: string;
  provider?: LLMProvider;
  source_language: string;
  target_language: string;
  original_text: string;
  custom_prompt?: string;
}

export interface TranslationJobResponse extends BaseSchema {
  text_region_id: string;
  status: TranslationStatus;
  provider: LLMProvider;
  source_language: string;
  target_language: string;
  original_text: string;
  translated_text?: string;
  prompt_used?: string;
  processing_time_seconds?: number;
  confidence_score?: number;
  error_message?: string;
  retry_count: number;
}

export interface TranslationAlternativeResponse extends BaseSchema {
  job_id: string;
  alternative_text: string;
  confidence_score: number;
  provider: LLMProvider;
  is_selected: boolean;
  quality_score?: number;
}

export interface TranslationProcessRequest {
  text_region_id: string;
  provider?: LLMProvider;
  source_language: string;
  target_language: string;
  original_text: string;
  custom_prompt?: string;
}

export interface TranslationJobDetailResponse extends TranslationJobResponse {
  alternatives: TranslationAlternativeResponse[];
}

// Detailed response types
export interface ProjectDetailResponse extends ProjectResponse {
  pages: ProjectPageResponse[];
}

export interface ProjectPageDetailResponse extends ProjectPageResponse {
  text_regions: TextRegionResponse[];
}

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}

// API Response types
export interface SuccessResponse {
  message: string;
  data?: any;
}

export interface ErrorResponse {
  error_code: string;
  detail: string;
  timestamp: string;
}

// Supported languages
export const SUPPORTED_LANGUAGES = {
  japanese: "Japanese",
  english: "English",
  indonesian: "Indonesian",
  korean: "Korean",
  chinese: "Chinese",
} as const;

export type SupportedLanguage = keyof typeof SUPPORTED_LANGUAGES;
