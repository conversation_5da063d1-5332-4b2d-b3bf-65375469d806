/**
 * API client for ho-trans backend
 */
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  ProjectCreate,
  ProjectUpdate,
  ProjectResponse,
  ProjectDetailResponse,
  ProjectPageCreate,
  ProjectPageResponse,
  ProjectPageDetailResponse,
  TextRegionCreate,
  TextRegionUpdate,
  TextRegionResponse,
  OCRJobCreate,
  OCRJobResponse,
  OCRJobDetailResponse,
  OCRResultResponse,
  OCRProcessRequest,
  TranslationJobCreate,
  TranslationJobResponse,
  TranslationJobDetailResponse,
  TranslationAlternativeResponse,
  TranslationProcessRequest,
  PaginationParams,
  PaginatedResponse,
  SuccessResponse,
} from './types';

// API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
const API_VERSION = 'v1';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: `${API_BASE_URL}/api/${API_VERSION}`,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 seconds
    });

    // Request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Projects API
  async getProjects(params?: PaginationParams): Promise<PaginatedResponse<ProjectResponse>> {
    const response = await this.client.get('/projects', { params });
    return response.data;
  }

  async createProject(data: ProjectCreate): Promise<ProjectResponse> {
    const response = await this.client.post('/projects', data);
    return response.data;
  }

  async getProject(projectId: string): Promise<ProjectResponse> {
    const response = await this.client.get(`/projects/${projectId}`);
    return response.data;
  }

  async updateProject(projectId: string, data: ProjectUpdate): Promise<ProjectResponse> {
    const response = await this.client.put(`/projects/${projectId}`, data);
    return response.data;
  }

  async deleteProject(projectId: string): Promise<SuccessResponse> {
    const response = await this.client.delete(`/projects/${projectId}`);
    return response.data;
  }

  async getProjectDetail(projectId: string): Promise<ProjectDetailResponse> {
    const response = await this.client.get(`/projects/${projectId}/detail`);
    return response.data;
  }

  // Project Pages API
  async uploadPage(projectId: string, file: File, pageNumber: number): Promise<ProjectPageResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('page_number', pageNumber.toString());
    formData.append('original_filename', file.name);

    const response = await this.client.post(`/projects/${projectId}/pages`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async getProjectPages(projectId: string): Promise<ProjectPageResponse[]> {
    const response = await this.client.get(`/projects/${projectId}/pages`);
    return response.data;
  }

  async getProjectPage(projectId: string, pageId: string): Promise<ProjectPageResponse> {
    const response = await this.client.get(`/projects/${projectId}/pages/${pageId}`);
    return response.data;
  }

  async getProjectPageDetail(projectId: string, pageId: string): Promise<ProjectPageDetailResponse> {
    const response = await this.client.get(`/projects/${projectId}/pages/${pageId}/detail`);
    return response.data;
  }

  // Text Regions API
  async createTextRegion(projectId: string, pageId: string, data: TextRegionCreate): Promise<TextRegionResponse> {
    const response = await this.client.post(`/projects/${projectId}/pages/${pageId}/regions`, data);
    return response.data;
  }

  async updateTextRegion(projectId: string, pageId: string, regionId: string, data: TextRegionUpdate): Promise<TextRegionResponse> {
    const response = await this.client.put(`/projects/${projectId}/pages/${pageId}/regions/${regionId}`, data);
    return response.data;
  }

  async deleteTextRegion(projectId: string, pageId: string, regionId: string): Promise<SuccessResponse> {
    const response = await this.client.delete(`/projects/${projectId}/pages/${pageId}/regions/${regionId}`);
    return response.data;
  }

  // OCR API
  async createOCRJob(data: OCRJobCreate): Promise<OCRJobResponse> {
    const response = await this.client.post('/ocr/jobs', data);
    return response.data;
  }

  async getOCRJob(jobId: string): Promise<OCRJobResponse> {
    const response = await this.client.get(`/ocr/jobs/${jobId}`);
    return response.data;
  }

  async getOCRJobDetail(jobId: string): Promise<OCRJobDetailResponse> {
    const response = await this.client.get(`/ocr/jobs/${jobId}/detail`);
    return response.data;
  }

  async getOCRResults(jobId: string): Promise<OCRResultResponse[]> {
    const response = await this.client.get(`/ocr/jobs/${jobId}/results`);
    return response.data;
  }

  async processOCR(data: OCRProcessRequest): Promise<OCRJobResponse> {
    const response = await this.client.post('/ocr/process', data);
    return response.data;
  }

  async getPageOCRJobs(pageId: string): Promise<OCRJobResponse[]> {
    const response = await this.client.get(`/ocr/pages/${pageId}/jobs`);
    return response.data;
  }

  async retryOCRJob(jobId: string): Promise<OCRJobResponse> {
    const response = await this.client.post(`/ocr/jobs/${jobId}/retry`);
    return response.data;
  }

  // Translation API
  async createTranslationJob(data: TranslationJobCreate): Promise<TranslationJobResponse> {
    const response = await this.client.post('/translation/jobs', data);
    return response.data;
  }

  async getTranslationJob(jobId: string): Promise<TranslationJobResponse> {
    const response = await this.client.get(`/translation/jobs/${jobId}`);
    return response.data;
  }

  async getTranslationJobDetail(jobId: string): Promise<TranslationJobDetailResponse> {
    const response = await this.client.get(`/translation/jobs/${jobId}/detail`);
    return response.data;
  }

  async getTranslationAlternatives(jobId: string): Promise<TranslationAlternativeResponse[]> {
    const response = await this.client.get(`/translation/jobs/${jobId}/alternatives`);
    return response.data;
  }

  async selectTranslationAlternative(jobId: string, alternativeId: string): Promise<SuccessResponse> {
    const response = await this.client.post(`/translation/jobs/${jobId}/alternatives/${alternativeId}/select`);
    return response.data;
  }

  async processTranslation(data: TranslationProcessRequest): Promise<TranslationJobResponse> {
    const response = await this.client.post('/translation/process', data);
    return response.data;
  }

  // Utility methods
  async uploadFile(file: File, endpoint: string): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.client.post(endpoint, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // Get image URL for a page
  getPageImageUrl(pageId: string): string {
    return `${API_BASE_URL}/api/${API_VERSION}/projects/pages/${pageId}/image`;
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient();
export default apiClient;
