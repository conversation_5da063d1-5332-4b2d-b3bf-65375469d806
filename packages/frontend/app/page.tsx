"use client";

import React, { useState, useCallback } from 'react';
import ProjectManager from '@/components/project/ProjectManager';
import CanvasEditor from '@/components/canvas/CanvasEditor';
import TextRegionManager from '@/components/canvas/TextRegionManager';
import FontStyleControls from '@/components/canvas/FontStyleControls';
import OCRProcessor from '@/components/ocr/OCRProcessor';
import TranslationWorkflow from '@/components/translation/TranslationWorkflow';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  ProjectResponse,
  ProjectPageResponse,
  TextRegionResponse,
  OCRJobResponse,
  TranslationJobResponse
} from '@/lib/types';
import { apiClient } from '@/lib/api';

export default function Home() {
  const [selectedProject, setSelectedProject] = useState<ProjectResponse | undefined>();
  const [selectedPage, setSelectedPage] = useState<ProjectPageResponse | undefined>();
  const [textRegions, setTextRegions] = useState<TextRegionResponse[]>([]);
  const [selectedRegionId, setSelectedRegionId] = useState<string | undefined>();
  const [rightPanelTab, setRightPanelTab] = useState<string>('regions');

  // Load text regions when page changes
  const loadTextRegions = useCallback(async (page: ProjectPageResponse) => {
    if (!selectedProject) return;

    try {
      const pageDetail = await apiClient.getProjectPageDetail(selectedProject.id, page.id);
      setTextRegions(pageDetail.text_regions);
    } catch (err) {
      console.error('Failed to load text regions:', err);
      setTextRegions([]);
    }
  }, [selectedProject]);

  const handleProjectSelect = useCallback((project: ProjectResponse | undefined) => {
    setSelectedProject(project);
    setSelectedPage(undefined);
    setTextRegions([]);
    setSelectedRegionId(undefined);
  }, []);

  const handlePageSelect = useCallback((page: ProjectPageResponse | undefined) => {
    setSelectedPage(page);
    setSelectedRegionId(undefined);
    if (page) {
      loadTextRegions(page);
    } else {
      setTextRegions([]);
    }
  }, [loadTextRegions]);

  const handleTextRegionCreate = useCallback(async (region: Partial<TextRegionResponse>) => {
    if (!selectedProject || !selectedPage) return;

    try {
      const newRegion = await apiClient.createTextRegion(
        selectedProject.id,
        selectedPage.id,
        region as any
      );
      setTextRegions(prev => [...prev, newRegion]);
    } catch (err) {
      console.error('Failed to create text region:', err);
    }
  }, [selectedProject, selectedPage]);

  const handleTextRegionUpdate = useCallback(async (regionId: string, updates: Partial<TextRegionResponse>) => {
    if (!selectedProject || !selectedPage) return;

    try {
      const updatedRegion = await apiClient.updateTextRegion(
        selectedProject.id,
        selectedPage.id,
        regionId,
        updates as any
      );
      setTextRegions(prev => prev.map(r => r.id === regionId ? updatedRegion : r));
    } catch (err) {
      console.error('Failed to update text region:', err);
    }
  }, [selectedProject, selectedPage]);

  const handleTextRegionDelete = useCallback(async (regionId: string) => {
    if (!selectedProject || !selectedPage) return;

    try {
      await apiClient.deleteTextRegion(selectedProject.id, selectedPage.id, regionId);
      setTextRegions(prev => prev.filter(r => r.id !== regionId));
      if (selectedRegionId === regionId) {
        setSelectedRegionId(undefined);
      }
    } catch (err) {
      console.error('Failed to delete text region:', err);
    }
  }, [selectedProject, selectedPage, selectedRegionId]);

  const handleRegionsDetected = useCallback((regions: Partial<TextRegionResponse>[]) => {
    // Create text regions from OCR results
    regions.forEach(region => {
      handleTextRegionCreate(region);
    });
  }, [handleTextRegionCreate]);

  const handleTranslationComplete = useCallback((regionId: string, translation: string) => {
    handleTextRegionUpdate(regionId, { translated_text: translation });
  }, [handleTextRegionUpdate]);

  const handleSave = useCallback(async () => {
    // Save functionality - could save canvas state, project progress, etc.
    console.log('Saving project state...');
  }, []);

  const selectedRegion = selectedRegionId ? textRegions.find(r => r.id === selectedRegionId) : undefined;

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Left Panel - Project Management */}
      <ProjectManager
        onProjectSelect={handleProjectSelect}
        onPageSelect={handlePageSelect}
        selectedProject={selectedProject}
        selectedPage={selectedPage}
      />

      {/* Main Content - Canvas Editor */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="h-16 bg-white border-b flex items-center justify-between px-6">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-bold">Ho-Trans</h1>
            <div className="text-sm text-gray-600">
              Manga Translation Tool
            </div>
          </div>
          <div className="flex items-center gap-4">
            {selectedProject && (
              <div className="flex items-center gap-2">
                <Badge variant="outline">
                  {selectedProject.source_language} → {selectedProject.target_language}
                </Badge>
                {selectedPage && (
                  <Badge variant="outline">
                    Page {selectedPage.page_number}
                  </Badge>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Canvas Area */}
        <div className="flex-1">
          {selectedPage ? (
            <CanvasEditor
              page={selectedPage}
              textRegions={textRegions}
              onTextRegionCreate={handleTextRegionCreate}
              onTextRegionUpdate={handleTextRegionUpdate}
              onTextRegionDelete={handleTextRegionDelete}
              onSave={handleSave}
            />
          ) : (
            <div className="h-full flex items-center justify-center bg-gray-100">
              <div className="text-center text-gray-500">
                <div className="text-6xl mb-4">📖</div>
                <h2 className="text-xl font-semibold mb-2">Welcome to Ho-Trans</h2>
                <p className="text-gray-600 mb-4">
                  Select a project and page to start translating manga
                </p>
                {!selectedProject && (
                  <p className="text-sm">
                    Create a new project or select an existing one from the sidebar
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right Panel - Tools */}
      {selectedPage && (
        <Tabs value={rightPanelTab} onValueChange={setRightPanelTab} className="w-80 border-l bg-white flex flex-col">
          <TabsList className="grid w-full grid-cols-4 mx-4 mt-2">
            <TabsTrigger value="regions">Regions</TabsTrigger>
            <TabsTrigger value="ocr">OCR</TabsTrigger>
            <TabsTrigger value="translate">Translate</TabsTrigger>
            <TabsTrigger value="style">Style</TabsTrigger>
          </TabsList>

          <TabsContent value="regions" className="flex-1 mt-0">
            <TextRegionManager
              textRegions={textRegions}
              selectedRegionId={selectedRegionId}
              onRegionSelect={setSelectedRegionId}
              onRegionUpdate={handleTextRegionUpdate}
              onRegionDelete={handleTextRegionDelete}
            />
          </TabsContent>

          <TabsContent value="ocr" className="flex-1 mt-0">
            <OCRProcessor
              page={selectedPage}
              onRegionsDetected={handleRegionsDetected}
            />
          </TabsContent>

          <TabsContent value="translate" className="flex-1 mt-0">
            <TranslationWorkflow
              selectedRegion={selectedRegion}
              sourceLanguage={selectedProject?.source_language}
              targetLanguage={selectedProject?.target_language}
              onTranslationComplete={handleTranslationComplete}
            />
          </TabsContent>

          <TabsContent value="style" className="flex-1 mt-0">
            <FontStyleControls
              selectedStyle={selectedRegion ? {
                fontFamily: selectedRegion.font_family || 'Arial',
                fontSize: selectedRegion.font_size || 16,
                color: selectedRegion.font_color || '#000000',
                backgroundColor: selectedRegion.background_color || 'transparent',
              } : undefined}
              onStyleChange={(style) => {
                if (selectedRegionId) {
                  handleTextRegionUpdate(selectedRegionId, {
                    font_family: style.fontFamily,
                    font_size: style.fontSize,
                    font_color: style.color,
                    background_color: style.backgroundColor,
                  });
                }
              }}
            />
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
