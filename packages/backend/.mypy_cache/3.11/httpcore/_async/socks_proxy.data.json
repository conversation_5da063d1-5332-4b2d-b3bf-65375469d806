{".class": "MypyFile", "_fullname": "httpcore._async.socks_proxy", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AUTH_METHODS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpcore._async.socks_proxy.AUTH_METHODS", "name": "AUTH_METHODS", "type": {".class": "Instance", "args": ["builtins.bytes", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "AsyncConnectionInterface": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.interfaces.AsyncConnectionInterface", "kind": "Gdef"}, "AsyncConnectionPool": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.connection_pool.AsyncConnectionPool", "kind": "Gdef"}, "AsyncHTTP11Connection": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.http11.AsyncHTTP11Connection", "kind": "Gdef"}, "AsyncLock": {".class": "SymbolTableNode", "cross_ref": "httpcore._synchronization.AsyncLock", "kind": "Gdef"}, "AsyncNetworkBackend": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.base.AsyncNetworkBackend", "kind": "Gdef"}, "AsyncNetworkStream": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.base.AsyncNetworkStream", "kind": "Gdef"}, "AsyncSOCKSProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._async.connection_pool.AsyncConnectionPool"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._async.socks_proxy.AsyncSOCKSProxy", "name": "AsyncSOCKSProxy", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpcore._async.socks_proxy.AsyncSOCKSProxy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._async.socks_proxy", "mro": ["httpcore._async.socks_proxy.AsyncSOCKSProxy", "httpcore._async.connection_pool.AsyncConnectionPool", "httpcore._async.interfaces.AsyncRequestInterface", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "proxy_url", "proxy_auth", "ssl_context", "max_connections", "max_keepalive_connections", "keepalive_expiry", "http1", "http2", "retries", "network_backend"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpcore._async.socks_proxy.AsyncSOCKSProxy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "proxy_url", "proxy_auth", "ssl_context", "max_connections", "max_keepalive_connections", "keepalive_expiry", "http1", "http2", "retries", "network_backend"], "arg_types": ["httpcore._async.socks_proxy.AsyncSOCKSProxy", {".class": "UnionType", "items": ["httpcore._models.URL", "builtins.bytes", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["httpcore._backends.base.AsyncNetworkBackend", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncSOCKSProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_proxy_auth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "httpcore._async.socks_proxy.AsyncSOCKSProxy._proxy_auth", "name": "_proxy_auth", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_proxy_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.socks_proxy.AsyncSOCKSProxy._proxy_url", "name": "_proxy_url", "type": "httpcore._models.URL"}}, "create_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpcore._async.socks_proxy.AsyncSOCKSProxy.create_connection", "name": "create_connection", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "arg_types": ["httpcore._async.socks_proxy.AsyncSOCKSProxy", "httpcore._models.Origin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_connection of AsyncSOCKSProxy", "ret_type": "httpcore._async.interfaces.AsyncConnectionInterface", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._async.socks_proxy.AsyncSOCKSProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._async.socks_proxy.AsyncSOCKSProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncSocks5Connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._async.interfaces.AsyncConnectionInterface"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection", "name": "AsyncSocks5Connection", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._async.socks_proxy", "mro": ["httpcore._async.socks_proxy.AsyncSocks5Connection", "httpcore._async.interfaces.AsyncConnectionInterface", "httpcore._async.interfaces.AsyncRequestInterface", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "proxy_origin", "remote_origin", "proxy_auth", "ssl_context", "keepalive_expiry", "http1", "http2", "network_backend"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "proxy_origin", "remote_origin", "proxy_auth", "ssl_context", "keepalive_expiry", "http1", "http2", "network_backend"], "arg_types": ["httpcore._async.socks_proxy.AsyncSocks5Connection", "httpcore._models.Origin", "httpcore._models.Origin", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["httpcore._backends.base.AsyncNetworkBackend", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncSocks5Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["httpcore._async.socks_proxy.AsyncSocks5Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of AsyncSocks5Connection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_connect_failed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection._connect_failed", "name": "_connect_failed", "type": "builtins.bool"}}, "_connect_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection._connect_lock", "name": "_connect_lock", "type": "httpcore._synchronization.AsyncLock"}}, "_connection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection._connection", "name": "_connection", "type": {".class": "UnionType", "items": ["httpcore._async.interfaces.AsyncConnectionInterface", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_http1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection._http1", "name": "_http1", "type": "builtins.bool"}}, "_http2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection._http2", "name": "_http2", "type": "builtins.bool"}}, "_keepalive_expiry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection._keepalive_expiry", "name": "_keepalive_expiry", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_network_backend": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection._network_backend", "name": "_network_backend", "type": "httpcore._backends.base.AsyncNetworkBackend"}}, "_proxy_auth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection._proxy_auth", "name": "_proxy_auth", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_proxy_origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection._proxy_origin", "name": "_proxy_origin", "type": "httpcore._models.Origin"}}, "_remote_origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection._remote_origin", "name": "_remote_origin", "type": "httpcore._models.Origin"}}, "_ssl_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection._ssl_context", "name": "_ssl_context", "type": {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "aclose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection.aclose", "name": "aclose", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.socks_proxy.AsyncSocks5Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aclose of AsyncSocks5Connection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_handle_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection.can_handle_request", "name": "can_handle_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "arg_types": ["httpcore._async.socks_proxy.AsyncSocks5Connection", "httpcore._models.Origin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_handle_request of AsyncSocks5Connection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_async_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection.handle_async_request", "name": "handle_async_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["httpcore._async.socks_proxy.AsyncSocks5Connection", "httpcore._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_async_request of AsyncSocks5Connection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "httpcore._models.Response"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_expired": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection.has_expired", "name": "has_expired", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.socks_proxy.AsyncSocks5Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_expired of AsyncSocks5Connection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.socks_proxy.AsyncSocks5Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of AsyncSocks5Connection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_available": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection.is_available", "name": "is_available", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.socks_proxy.AsyncSocks5Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_available of AsyncSocks5Connection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection.is_closed", "name": "is_closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.socks_proxy.AsyncSocks5Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_closed of AsyncSocks5Connection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_idle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection.is_idle", "name": "is_idle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.socks_proxy.AsyncSocks5Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_idle of AsyncSocks5Connection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._async.socks_proxy.AsyncSocks5Connection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._async.socks_proxy.AsyncSocks5Connection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AutoBackend": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.auto.AutoBackend", "kind": "Gdef"}, "ConnectionNotAvailable": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.ConnectionNotAvailable", "kind": "Gdef"}, "Origin": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.Origin", "kind": "Gdef"}, "ProxyError": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.ProxyError", "kind": "Gdef"}, "REPLY_CODES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpcore._async.socks_proxy.REPLY_CODES", "name": "REPLY_CODES", "type": {".class": "Instance", "args": ["builtins.bytes", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "Request": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.Request", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.Response", "kind": "Gdef"}, "Trace": {".class": "SymbolTableNode", "cross_ref": "httpcore._trace.Trace", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.URL", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.socks_proxy.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.socks_proxy.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.socks_proxy.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.socks_proxy.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.socks_proxy.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.socks_proxy.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_init_socks5_connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5], "arg_names": ["stream", "host", "port", "auth"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "httpcore._async.socks_proxy._init_socks5_connection", "name": "_init_socks5_connection", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5], "arg_names": ["stream", "host", "port", "auth"], "arg_types": ["httpcore._backends.base.AsyncNetworkStream", "builtins.bytes", "builtins.int", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_socks5_connection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "default_ssl_context": {".class": "SymbolTableNode", "cross_ref": "httpcore._ssl.default_ssl_context", "kind": "Gdef"}, "enforce_bytes": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.enforce_bytes", "kind": "Gdef"}, "enforce_url": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.enforce_url", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpcore._async.socks_proxy.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "socksio": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "httpcore._async.socks_proxy.socksio", "name": "<PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "httpcore._async.socks_proxy.socksio", "source_any": null, "type_of_any": 3}}}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/httpcore/_async/socks_proxy.py"}