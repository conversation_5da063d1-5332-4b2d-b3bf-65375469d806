{"data_mtime": 1751259991, "dep_lines": [1, 2, 3, 4, 5, 8, 20, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["httpcore._async.connection", "httpcore._async.connection_pool", "httpcore._async.http11", "httpcore._async.http_proxy", "httpcore._async.interfaces", "httpcore._async.http2", "httpcore._async.socks_proxy", "builtins", "abc", "importlib", "importlib.machinery", "typing"], "hash": "b229f232e43d292ba57d309b82fed0e0ee362493", "id": "httpcore._async", "ignore_all": true, "interface_hash": "5e798e167101a823747c041d161546a4d12d2084", "mtime": 1751256097, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/httpcore/_async/__init__.py", "plugin_data": null, "size": 1221, "suppressed": [], "version_id": "1.13.0"}