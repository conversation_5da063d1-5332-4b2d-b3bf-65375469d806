{".class": "MypyFile", "_fullname": "httpcore._async", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncConnectionInterface": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.interfaces.AsyncConnectionInterface", "kind": "Gdef"}, "AsyncConnectionPool": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.connection_pool.AsyncConnectionPool", "kind": "Gdef"}, "AsyncHTTP11Connection": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.http11.AsyncHTTP11Connection", "kind": "Gdef"}, "AsyncHTTP2Connection": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.http2.AsyncHTTP2Connection", "kind": "Gdef"}, "AsyncHTTPConnection": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.connection.AsyncHTTPConnection", "kind": "Gdef"}, "AsyncHTTPProxy": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.http_proxy.AsyncHTTPProxy", "kind": "Gdef"}, "AsyncSOCKSProxy": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.socks_proxy.AsyncSOCKSProxy", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpcore._async.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/httpcore/_async/__init__.py"}