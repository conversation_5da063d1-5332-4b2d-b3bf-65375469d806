{"data_mtime": 1751259987, "dep_lines": [20, 9, 19, 1, 3, 4, 5, 6, 7, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 30, 30, 30, 30], "dependencies": ["httpcore._backends.base", "httpcore._exceptions", "httpcore._utils", "__future__", "functools", "socket", "ssl", "sys", "typing", "builtins", "_socket", "abc", "importlib", "importlib.machinery"], "hash": "63837d17d3e6c826846ef4caaf82b62d116574cf", "id": "httpcore._backends.sync", "ignore_all": true, "interface_hash": "68ec7ecd1d01a8a43d97cc07ec73e951ecb9607d", "mtime": 1751256097, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/httpcore/_backends/sync.py", "plugin_data": null, "size": 7977, "suppressed": [], "version_id": "1.13.0"}