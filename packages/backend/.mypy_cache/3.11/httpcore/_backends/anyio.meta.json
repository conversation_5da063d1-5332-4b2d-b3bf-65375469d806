{"data_mtime": 1751259988, "dep_lines": [18, 8, 17, 1, 3, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["httpcore._backends.base", "httpcore._exceptions", "httpcore._utils", "__future__", "ssl", "typing", "anyio", "builtins", "abc", "anyio._core", "anyio._core._typedattr", "anyio.abc", "anyio.abc._resources", "anyio.abc._streams", "importlib", "importlib.machinery"], "hash": "81cffa237a37043dee9f9747e1c54fbb8ad8f621", "id": "httpcore._backends.anyio", "ignore_all": true, "interface_hash": "2f1001c16bf703c819cc62d3786237d9babe54e7", "mtime": 1751256097, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/httpcore/_backends/anyio.py", "plugin_data": null, "size": 5252, "suppressed": [], "version_id": "1.13.0"}