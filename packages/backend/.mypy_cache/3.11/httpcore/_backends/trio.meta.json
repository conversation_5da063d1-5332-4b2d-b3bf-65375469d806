{"data_mtime": 1751259987, "dep_lines": [18, 8, 1, 3, 4, 1, 1, 1, 1, 6], "dep_prios": [5, 5, 5, 10, 10, 5, 30, 30, 30, 10], "dependencies": ["httpcore._backends.base", "httpcore._exceptions", "__future__", "ssl", "typing", "builtins", "abc", "importlib", "importlib.machinery"], "hash": "64b850cda4b89ae9e6401d4fd40a55dab2d384d2", "id": "httpcore._backends.trio", "ignore_all": true, "interface_hash": "f9cac2e48a18328eb6e8c7c5cbd809c426da78aa", "mtime": 1751256097, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/httpcore/_backends/trio.py", "plugin_data": null, "size": 5996, "suppressed": ["trio"], "version_id": "1.13.0"}