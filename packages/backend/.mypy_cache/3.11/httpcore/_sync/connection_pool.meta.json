{"data_mtime": 1751259991, "dep_lines": [8, 9, 13, 14, 131, 144, 10, 11, 12, 1, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 20, 5, 5, 5, 5, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["httpcore._backends.sync", "httpcore._backends.base", "httpcore._sync.connection", "httpcore._sync.interfaces", "httpcore._sync.socks_proxy", "httpcore._sync.http_proxy", "httpcore._exceptions", "httpcore._models", "httpcore._synchronization", "__future__", "ssl", "sys", "types", "typing", "builtins", "_typeshed", "abc", "anyio", "anyio._core", "anyio._core._synchronization", "httpcore._backends", "importlib", "importlib.machinery"], "hash": "a4fa103de61479074f51e3c33f787c950ce05b80", "id": "httpcore._sync.connection_pool", "ignore_all": true, "interface_hash": "db7dfbd342d6daa1ee58d433f17636758f01406e", "mtime": 1751256097, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", "plugin_data": null, "size": 16955, "suppressed": [], "version_id": "1.13.0"}