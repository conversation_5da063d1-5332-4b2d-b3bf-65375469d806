{".class": "MypyFile", "_fullname": "h11._connection", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "CLIENT": {".class": "SymbolTableNode", "cross_ref": "h11._state.CLIENT", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "h11._connection.Connection", "name": "Connection", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "h11._connection.Connection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "h11._connection", "mro": ["h11._connection.Connection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "our_role", "max_incomplete_event_size"], "dataclass_transform_spec": null, "flags": [], "fullname": "h11._connection.Connection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "our_role", "max_incomplete_event_size"], "arg_types": ["h11._connection.Connection", {".class": "TypeType", "item": "h11._util.Sentinel"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_clean_up_response_headers_for_sending": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "flags": [], "fullname": "h11._connection.Connection._clean_up_response_headers_for_sending", "name": "_clean_up_response_headers_for_sending", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["h11._connection.Connection", "h11._events.Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clean_up_response_headers_for_sending of Connection", "ret_type": "h11._events.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cstate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "h11._connection.Connection._cstate", "name": "_cstate", "type": "h11._state.ConnectionState"}}, "_extract_next_receive_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "h11._connection.Connection._extract_next_receive_event", "name": "_extract_next_receive_event", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._connection.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_next_receive_event of Connection", "ret_type": {".class": "UnionType", "items": ["h11._events.Event", {".class": "TypeType", "item": "h11._connection.NEED_DATA"}, {".class": "TypeType", "item": "h11._connection.PAUSED"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_io_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "role", "event", "io_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "h11._connection.Connection._get_io_object", "name": "_get_io_object", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "role", "event", "io_dict"], "arg_types": ["h11._connection.Connection", {".class": "TypeType", "item": "h11._util.Sentinel"}, {".class": "UnionType", "items": ["h11._events.Event", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "h11._readers.ReadersType"}, {".class": "TypeAliasType", "args": [], "type_ref": "h11._writers.WritersType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_io_object of Connection", "ret_type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_max_incomplete_event_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "h11._connection.Connection._max_incomplete_event_size", "name": "_max_incomplete_event_size", "type": "builtins.int"}}, "_process_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "role"], "dataclass_transform_spec": null, "flags": [], "fullname": "h11._connection.Connection._process_error", "name": "_process_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "role"], "arg_types": ["h11._connection.Connection", {".class": "TypeType", "item": "h11._util.Sentinel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_error of Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "role", "event"], "dataclass_transform_spec": null, "flags": [], "fullname": "h11._connection.Connection._process_event", "name": "_process_event", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "role", "event"], "arg_types": ["h11._connection.Connection", {".class": "TypeType", "item": "h11._util.Sentinel"}, "h11._events.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_event of Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reader": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "h11._connection.Connection._reader", "name": "_reader", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_receive_buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "h11._connection.Connection._receive_buffer", "name": "_receive_buffer", "type": "h11._receivebuffer.ReceiveBuffer"}}, "_receive_buffer_closed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "h11._connection.Connection._receive_buffer_closed", "name": "_receive_buffer_closed", "type": "builtins.bool"}}, "_request_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "h11._connection.Connection._request_method", "name": "_request_method", "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_respond_to_state_changes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "old_states", "event"], "dataclass_transform_spec": null, "flags": [], "fullname": "h11._connection.Connection._respond_to_state_changes", "name": "_respond_to_state_changes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "old_states", "event"], "arg_types": ["h11._connection.Connection", {".class": "Instance", "args": [{".class": "TypeType", "item": "h11._util.Sentinel"}, {".class": "TypeType", "item": "h11._util.Sentinel"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["h11._events.Event", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_respond_to_state_changes of Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_server_switch_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "flags": [], "fullname": "h11._connection.Connection._server_switch_event", "name": "_server_switch_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["h11._connection.Connection", "h11._events.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_server_switch_event of Connection", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "h11._util.Sentinel"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_writer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "h11._connection.Connection._writer", "name": "_writer", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "client_is_waiting_for_100_continue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "h11._connection.Connection.client_is_waiting_for_100_continue", "name": "client_is_waiting_for_100_continue", "type": "builtins.bool"}}, "next_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "h11._connection.Connection.next_event", "name": "next_event", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._connection.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next_event of Connection", "ret_type": {".class": "UnionType", "items": ["h11._events.Event", {".class": "TypeType", "item": "h11._connection.NEED_DATA"}, {".class": "TypeType", "item": "h11._connection.PAUSED"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "our_role": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "h11._connection.Connection.our_role", "name": "our_role", "type": {".class": "TypeType", "item": "h11._util.Sentinel"}}}, "our_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "h11._connection.Connection.our_state", "name": "our_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._connection.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "our_state of Connection", "ret_type": {".class": "TypeType", "item": "h11._util.Sentinel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "h11._connection.Connection.our_state", "name": "our_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._connection.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "our_state of Connection", "ret_type": {".class": "TypeType", "item": "h11._util.Sentinel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "receive_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "flags": [], "fullname": "h11._connection.Connection.receive_data", "name": "receive_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["h11._connection.Connection", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive_data of Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "h11._connection.Connection.send", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "h11._connection.Connection.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["h11._connection.Connection", "h11._events.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of Connection", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "h11._connection.Connection.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["h11._connection.Connection", "h11._events.ConnectionClosed"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "h11._connection.Connection.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["h11._connection.Connection", "h11._events.ConnectionClosed"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "h11._connection.Connection.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["h11._connection.Connection", {".class": "UnionType", "items": ["h11._events.Request", "h11._events.InformationalResponse", "h11._events.Response", "h11._events.Data", "h11._events.EndOfMessage"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of Connection", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "h11._connection.Connection.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["h11._connection.Connection", {".class": "UnionType", "items": ["h11._events.Request", "h11._events.InformationalResponse", "h11._events.Response", "h11._events.Data", "h11._events.EndOfMessage"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of Connection", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "h11._connection.Connection.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["h11._connection.Connection", "h11._events.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of Connection", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "h11._connection.Connection.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["h11._connection.Connection", "h11._events.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of Connection", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["h11._connection.Connection", "h11._events.ConnectionClosed"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["h11._connection.Connection", {".class": "UnionType", "items": ["h11._events.Request", "h11._events.InformationalResponse", "h11._events.Response", "h11._events.Data", "h11._events.EndOfMessage"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of Connection", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["h11._connection.Connection", "h11._events.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of Connection", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "send_failed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "h11._connection.Connection.send_failed", "name": "send_failed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._connection.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_failed of Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_with_data_passthrough": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "flags": [], "fullname": "h11._connection.Connection.send_with_data_passthrough", "name": "send_with_data_passthrough", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["h11._connection.Connection", "h11._events.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_with_data_passthrough of Connection", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_next_cycle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "h11._connection.Connection.start_next_cycle", "name": "start_next_cycle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._connection.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_next_cycle of Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "h11._connection.Connection.states", "name": "states", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._connection.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "states of Connection", "ret_type": {".class": "Instance", "args": [{".class": "TypeType", "item": "h11._util.Sentinel"}, {".class": "TypeType", "item": "h11._util.Sentinel"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "h11._connection.Connection.states", "name": "states", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._connection.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "states of Connection", "ret_type": {".class": "Instance", "args": [{".class": "TypeType", "item": "h11._util.Sentinel"}, {".class": "TypeType", "item": "h11._util.Sentinel"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "their_http_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "h11._connection.Connection.their_http_version", "name": "their_http_version", "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "their_role": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "h11._connection.Connection.their_role", "name": "their_role", "type": {".class": "TypeType", "item": "h11._util.Sentinel"}}}, "their_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "h11._connection.Connection.their_state", "name": "their_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._connection.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "their_state of Connection", "ret_type": {".class": "TypeType", "item": "h11._util.Sentinel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "h11._connection.Connection.their_state", "name": "their_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._connection.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "their_state of Connection", "ret_type": {".class": "TypeType", "item": "h11._util.Sentinel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "they_are_waiting_for_100_continue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "h11._connection.Connection.they_are_waiting_for_100_continue", "name": "they_are_waiting_for_100_continue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._connection.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "they_are_waiting_for_100_continue of Connection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "h11._connection.Connection.they_are_waiting_for_100_continue", "name": "they_are_waiting_for_100_continue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._connection.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "they_are_waiting_for_100_continue of Connection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "trailing_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "h11._connection.Connection.trailing_data", "name": "trailing_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._connection.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trailing_data of Connection", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "h11._connection.Connection.trailing_data", "name": "trailing_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._connection.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trailing_data of Connection", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._connection.Connection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._connection.Connection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionClosed": {".class": "SymbolTableNode", "cross_ref": "h11._events.ConnectionClosed", "kind": "Gdef", "module_public": false}, "ConnectionState": {".class": "SymbolTableNode", "cross_ref": "h11._state.ConnectionState", "kind": "Gdef", "module_public": false}, "DEFAULT_MAX_INCOMPLETE_EVENT_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "h11._connection.DEFAULT_MAX_INCOMPLETE_EVENT_SIZE", "name": "DEFAULT_MAX_INCOMPLETE_EVENT_SIZE", "type": "builtins.int"}}, "DONE": {".class": "SymbolTableNode", "cross_ref": "h11._state.DONE", "kind": "Gdef", "module_public": false}, "Data": {".class": "SymbolTableNode", "cross_ref": "h11._events.Data", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "ERROR": {".class": "SymbolTableNode", "cross_ref": "h11._state.ERROR", "kind": "Gdef", "module_public": false}, "EndOfMessage": {".class": "SymbolTableNode", "cross_ref": "h11._events.EndOfMessage", "kind": "Gdef", "module_public": false}, "Event": {".class": "SymbolTableNode", "cross_ref": "h11._events.Event", "kind": "Gdef", "module_public": false}, "InformationalResponse": {".class": "SymbolTableNode", "cross_ref": "h11._events.InformationalResponse", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "LocalProtocolError": {".class": "SymbolTableNode", "cross_ref": "h11._util.LocalProtocolError", "kind": "Gdef", "module_public": false}, "MIGHT_SWITCH_PROTOCOL": {".class": "SymbolTableNode", "cross_ref": "h11._state.MIGHT_SWITCH_PROTOCOL", "kind": "Gdef", "module_public": false}, "NEED_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["h11._util.Sentinel"], "dataclass_transform_spec": null, "declared_metaclass": "h11._util.Sentinel", "defn": {".class": "ClassDef", "fullname": "h11._connection.NEED_DATA", "name": "NEED_DATA", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "h11._connection.NEED_DATA", "has_param_spec_type": false, "metaclass_type": "h11._util.Sentinel", "metadata": {}, "module_name": "h11._connection", "mro": ["h11._connection.NEED_DATA", "h11._util.Sentinel", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._connection.NEED_DATA.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._connection.NEED_DATA", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PAUSED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["h11._util.Sentinel"], "dataclass_transform_spec": null, "declared_metaclass": "h11._util.Sentinel", "defn": {".class": "ClassDef", "fullname": "h11._connection.PAUSED", "name": "PAUSED", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "h11._connection.PAUSED", "has_param_spec_type": false, "metaclass_type": "h11._util.Sentinel", "metadata": {}, "module_name": "h11._connection", "mro": ["h11._connection.PAUSED", "h11._util.Sentinel", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._connection.PAUSED.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._connection.PAUSED", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "READERS": {".class": "SymbolTableNode", "cross_ref": "h11._readers.READERS", "kind": "Gdef", "module_public": false}, "ReadersType": {".class": "SymbolTableNode", "cross_ref": "h11._readers.ReadersType", "kind": "Gdef", "module_public": false}, "ReceiveBuffer": {".class": "SymbolTableNode", "cross_ref": "h11._receivebuffer.ReceiveBuffer", "kind": "Gdef", "module_public": false}, "RemoteProtocolError": {".class": "SymbolTableNode", "cross_ref": "h11._util.RemoteProtocolError", "kind": "Gdef", "module_public": false}, "Request": {".class": "SymbolTableNode", "cross_ref": "h11._events.Request", "kind": "Gdef", "module_public": false}, "Response": {".class": "SymbolTableNode", "cross_ref": "h11._events.Response", "kind": "Gdef", "module_public": false}, "SEND_BODY": {".class": "SymbolTableNode", "cross_ref": "h11._state.SEND_BODY", "kind": "Gdef", "module_public": false}, "SERVER": {".class": "SymbolTableNode", "cross_ref": "h11._state.SERVER", "kind": "Gdef", "module_public": false}, "SWITCHED_PROTOCOL": {".class": "SymbolTableNode", "cross_ref": "h11._state.SWITCHED_PROTOCOL", "kind": "Gdef", "module_public": false}, "Sentinel": {".class": "SymbolTableNode", "cross_ref": "h11._util.Sentinel", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "WRITERS": {".class": "SymbolTableNode", "cross_ref": "h11._writers.WRITERS", "kind": "Gdef", "module_public": false}, "WritersType": {".class": "SymbolTableNode", "cross_ref": "h11._writers.WritersType", "kind": "Gdef", "module_public": false}, "_SWITCH_CONNECT": {".class": "SymbolTableNode", "cross_ref": "h11._state._SWITCH_CONNECT", "kind": "Gdef", "module_public": false}, "_SWITCH_UPGRADE": {".class": "SymbolTableNode", "cross_ref": "h11._state._SWITCH_UPGRADE", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._connection.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._connection.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._connection.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._connection.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._connection.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._connection.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._connection.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_body_framing": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["request_method", "event"], "dataclass_transform_spec": null, "flags": [], "fullname": "h11._connection._body_framing", "name": "_body_framing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["request_method", "event"], "arg_types": ["builtins.bytes", {".class": "UnionType", "items": ["h11._events.Request", "h11._events.Response"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_body_framing", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_keep_alive": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "flags": [], "fullname": "h11._connection._keep_alive", "name": "_keep_alive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": [{".class": "UnionType", "items": ["h11._events.Request", "h11._events.Response"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_keep_alive", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "get_comma_header": {".class": "SymbolTableNode", "cross_ref": "h11._headers.get_comma_header", "kind": "Gdef", "module_public": false}, "has_expect_100_continue": {".class": "SymbolTableNode", "cross_ref": "h11._headers.has_expect_100_continue", "kind": "Gdef", "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_public": false}, "set_comma_header": {".class": "SymbolTableNode", "cross_ref": "h11._headers.set_comma_header", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/h11/_connection.py"}