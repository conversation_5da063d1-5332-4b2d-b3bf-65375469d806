{"data_mtime": 1751259987, "dep_lines": [16, 25, 26, 27, 28, 40, 45, 3, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["h11._events", "h11._headers", "h11._readers", "h11._receivebuffer", "h11._state", "h11._util", "h11._writers", "typing", "builtins", "_typeshed", "abc", "importlib", "importlib.machinery", "types"], "hash": "b3aba12b3b94c2d76e446206392cf154565b1551", "id": "h11._connection", "ignore_all": true, "interface_hash": "27cc774069fb1ac064e86fdf522bd0ca9d405a48", "mtime": 1751256091, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/h11/_connection.py", "plugin_data": null, "size": 26863, "suppressed": [], "version_id": "1.13.0"}