{".class": "MypyFile", "_fullname": "h11", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CLIENT": {".class": "SymbolTableNode", "cross_ref": "h11._state.CLIENT", "kind": "Gdef"}, "CLOSED": {".class": "SymbolTableNode", "cross_ref": "h11._state.CLOSED", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "h11._connection.Connection", "kind": "Gdef"}, "ConnectionClosed": {".class": "SymbolTableNode", "cross_ref": "h11._events.ConnectionClosed", "kind": "Gdef"}, "DONE": {".class": "SymbolTableNode", "cross_ref": "h11._state.DONE", "kind": "Gdef"}, "Data": {".class": "SymbolTableNode", "cross_ref": "h11._events.Data", "kind": "Gdef"}, "ERROR": {".class": "SymbolTableNode", "cross_ref": "h11._state.ERROR", "kind": "Gdef"}, "EndOfMessage": {".class": "SymbolTableNode", "cross_ref": "h11._events.EndOfMessage", "kind": "Gdef"}, "Event": {".class": "SymbolTableNode", "cross_ref": "h11._events.Event", "kind": "Gdef"}, "IDLE": {".class": "SymbolTableNode", "cross_ref": "h11._state.IDLE", "kind": "Gdef"}, "InformationalResponse": {".class": "SymbolTableNode", "cross_ref": "h11._events.InformationalResponse", "kind": "Gdef"}, "LocalProtocolError": {".class": "SymbolTableNode", "cross_ref": "h11._util.LocalProtocolError", "kind": "Gdef"}, "MIGHT_SWITCH_PROTOCOL": {".class": "SymbolTableNode", "cross_ref": "h11._state.MIGHT_SWITCH_PROTOCOL", "kind": "Gdef", "module_public": false}, "MUST_CLOSE": {".class": "SymbolTableNode", "cross_ref": "h11._state.MUST_CLOSE", "kind": "Gdef"}, "NEED_DATA": {".class": "SymbolTableNode", "cross_ref": "h11._connection.NEED_DATA", "kind": "Gdef"}, "PAUSED": {".class": "SymbolTableNode", "cross_ref": "h11._connection.PAUSED", "kind": "Gdef"}, "PRODUCT_ID": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11.PRODUCT_ID", "name": "PRODUCT_ID", "type": "builtins.str"}}, "ProtocolError": {".class": "SymbolTableNode", "cross_ref": "h11._util.ProtocolError", "kind": "Gdef"}, "RemoteProtocolError": {".class": "SymbolTableNode", "cross_ref": "h11._util.RemoteProtocolError", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "h11._events.Request", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "h11._events.Response", "kind": "Gdef"}, "SEND_BODY": {".class": "SymbolTableNode", "cross_ref": "h11._state.SEND_BODY", "kind": "Gdef"}, "SEND_RESPONSE": {".class": "SymbolTableNode", "cross_ref": "h11._state.SEND_RESPONSE", "kind": "Gdef"}, "SERVER": {".class": "SymbolTableNode", "cross_ref": "h11._state.SERVER", "kind": "Gdef"}, "SWITCHED_PROTOCOL": {".class": "SymbolTableNode", "cross_ref": "h11._state.SWITCHED_PROTOCOL", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "h11._version.__version__", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/h11/__init__.py"}