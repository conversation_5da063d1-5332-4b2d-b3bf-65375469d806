{".class": "MypyFile", "_fullname": "multiprocessing.sharedctypes", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Array": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "multiprocessing.sharedctypes.Array", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Array#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Array#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Array#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Array#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Array#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Array#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "arg_types": [{".class": "TypeType", "item": "ctypes.c_char"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array", "ret_type": "multiprocessing.sharedctypes.SynchronizedString", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "arg_types": [{".class": "TypeType", "item": "ctypes.c_char"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array", "ret_type": "multiprocessing.sharedctypes.SynchronizedString", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "_ctypes._CData"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "_ctypes._CData"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 3, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Array#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Array#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Array#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "arg_types": [{".class": "TypeType", "item": "ctypes.c_char"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array", "ret_type": "multiprocessing.sharedctypes.SynchronizedString", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["typecode_or_type", "size_or_initializer", "lock", "ctx"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "_ctypes._CData"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "BaseContext": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.context.BaseContext", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RawArray": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "multiprocessing.sharedctypes.RawArray", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["typecode_or_type", "size_or_initializer"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.RawArray", "name": "RawArray", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["typecode_or_type", "size_or_initializer"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawArray", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.RawArray", "name": "RawArray", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["typecode_or_type", "size_or_initializer"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawArray", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["typecode_or_type", "size_or_initializer"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.RawArray", "name": "RawArray", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["typecode_or_type", "size_or_initializer"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawArray", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.RawArray", "name": "RawArray", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["typecode_or_type", "size_or_initializer"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawArray", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["typecode_or_type", "size_or_initializer"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawArray", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["typecode_or_type", "size_or_initializer"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawArray", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "RawValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "multiprocessing.sharedctypes.RawValue", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["typecode_or_type", "args"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.RawValue", "name": "RawValue", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["typecode_or_type", "args"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawValue", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.RawValue", "name": "RawValue", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["typecode_or_type", "args"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawValue", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["typecode_or_type", "args"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.RawValue", "name": "RawValue", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["typecode_or_type", "args"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawValue", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.RawValue", "name": "RawValue", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["typecode_or_type", "args"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawValue", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["typecode_or_type", "args"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawValue", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["typecode_or_type", "args"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawValue", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Synchronized": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.Synchronized", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.sharedctypes.Synchronized", "name": "Synchronized", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.Synchronized", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "multiprocessing.sharedctypes.Synchronized", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.sharedctypes", "mro": ["multiprocessing.sharedctypes.Synchronized", "multiprocessing.sharedctypes.SynchronizedBase", "builtins.object"], "names": {".class": "SymbolTable", "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "multiprocessing.sharedctypes.Synchronized.value", "name": "value", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.Synchronized", "upper_bound": "builtins.object", "values": [], "variance": 0}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes.Synchronized.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.Synchronized", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "SynchronizedArray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "extra_attrs": null, "type_ref": "_ctypes.Array"}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.sharedctypes.SynchronizedArray", "name": "SynchronizedArray", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedArray", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.sharedctypes", "mro": ["multiprocessing.sharedctypes.SynchronizedArray", "multiprocessing.sharedctypes.SynchronizedBase", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedArray.__getitem__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.SynchronizedArray.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "builtins.slice"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of SynchronizedArray", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.SynchronizedArray.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "builtins.slice"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of SynchronizedArray", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.SynchronizedArray.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of SynchronizedArray", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.SynchronizedArray.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of SynchronizedArray", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "builtins.slice"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of SynchronizedArray", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of SynchronizedArray", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__getslice__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start", "stop"], "dataclass_transform_spec": null, "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedArray.__getslice__", "name": "__getslice__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "start", "stop"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getslice__ of SynchronizedArray", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedArray.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of SynchronizedArray", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedArray.__setitem__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.SynchronizedArray.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "builtins.slice", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SynchronizedArray", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.SynchronizedArray.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "builtins.slice", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SynchronizedArray", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.SynchronizedArray.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "builtins.int", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SynchronizedArray", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.SynchronizedArray.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "builtins.int", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SynchronizedArray", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "builtins.slice", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SynchronizedArray", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "builtins.int", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SynchronizedArray", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__setslice__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "start", "stop", "values"], "dataclass_transform_spec": null, "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedArray.__setslice__", "name": "__setslice__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "start", "stop", "values"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "builtins.int", "builtins.int", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setslice__ of SynchronizedArray", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes.SynchronizedArray.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": 1, "name": "_T", "namespace": "multiprocessing.sharedctypes.SynchronizedArray", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "SynchronizedBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.sharedctypes.SynchronizedBase", "name": "SynchronizedBase", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": 1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.SynchronizedBase", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.sharedctypes", "mro": ["multiprocessing.sharedctypes.SynchronizedBase", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedBase.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": 1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.SynchronizedBase", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of SynchronizedBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedBase.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": 1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.SynchronizedBase", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of SynchronizedBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "obj", "lock", "ctx"], "dataclass_transform_spec": null, "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "obj", "lock", "ctx"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": 1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.SynchronizedBase", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SynchronizedBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedBase.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": 1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.SynchronizedBase", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of SynchronizedBase", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "acquire": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "multiprocessing.sharedctypes.SynchronizedBase.acquire", "name": "acquire", "type": "multiprocessing.sharedctypes._AcquireFunc"}}, "get_lock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedBase.get_lock", "name": "get_lock", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": 1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.SynchronizedBase", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_lock of SynchronizedBase", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_obj": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedBase.get_obj", "name": "get_obj", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": 1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.SynchronizedBase", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_obj of SynchronizedBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": 1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.SynchronizedBase", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "release": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "multiprocessing.sharedctypes.SynchronizedBase.release", "name": "release", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes.SynchronizedBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": 1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.SynchronizedBase", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_CT"], "typeddict_type": null}}, "SynchronizedString": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.sharedctypes.SynchronizedString", "name": "SynchronizedString", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedString", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.sharedctypes", "mro": ["multiprocessing.sharedctypes.SynchronizedString", "multiprocessing.sharedctypes.SynchronizedArray", "multiprocessing.sharedctypes.SynchronizedBase", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedString.__getitem__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.SynchronizedString.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["multiprocessing.sharedctypes.SynchronizedString", "builtins.slice"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of SynchronizedString", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.SynchronizedString.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["multiprocessing.sharedctypes.SynchronizedString", "builtins.slice"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of SynchronizedString", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.SynchronizedString.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["multiprocessing.sharedctypes.SynchronizedString", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of SynchronizedString", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.SynchronizedString.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["multiprocessing.sharedctypes.SynchronizedString", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of SynchronizedString", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["multiprocessing.sharedctypes.SynchronizedString", "builtins.slice"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of SynchronizedString", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["multiprocessing.sharedctypes.SynchronizedString", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of SynchronizedString", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__getslice__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start", "stop"], "dataclass_transform_spec": null, "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedString.__getslice__", "name": "__getslice__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "start", "stop"], "arg_types": ["multiprocessing.sharedctypes.SynchronizedString", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getslice__ of SynchronizedString", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedString.__setitem__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.SynchronizedString.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["multiprocessing.sharedctypes.SynchronizedString", "builtins.slice", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SynchronizedString", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.SynchronizedString.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["multiprocessing.sharedctypes.SynchronizedString", "builtins.slice", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SynchronizedString", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.SynchronizedString.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["multiprocessing.sharedctypes.SynchronizedString", "builtins.int", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SynchronizedString", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.SynchronizedString.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["multiprocessing.sharedctypes.SynchronizedString", "builtins.int", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SynchronizedString", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["multiprocessing.sharedctypes.SynchronizedString", "builtins.slice", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SynchronizedString", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["multiprocessing.sharedctypes.SynchronizedString", "builtins.int", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SynchronizedString", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__setslice__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "start", "stop", "values"], "dataclass_transform_spec": null, "flags": [], "fullname": "multiprocessing.sharedctypes.SynchronizedString.__setslice__", "name": "__setslice__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "start", "stop", "values"], "arg_types": ["multiprocessing.sharedctypes.SynchronizedString", "builtins.int", "builtins.int", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setslice__ of SynchronizedString", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "multiprocessing.sharedctypes.SynchronizedString.raw", "name": "raw", "type": "builtins.bytes"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "multiprocessing.sharedctypes.SynchronizedString.value", "name": "value", "type": "builtins.bytes"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes.SynchronizedString.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "multiprocessing.sharedctypes.SynchronizedString", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "multiprocessing.sharedctypes.Value", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 3, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "_ctypes._CData"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "_ctypes._CData"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 2, 3, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["typecode_or_type", "args", "lock", "ctx"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "_ctypes._CData"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_AcquireFunc": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.sharedctypes._AcquireFunc", "name": "_AcquireFunc", "type_vars": []}, "deletable_attributes": [], "flags": ["is_protocol"], "fullname": "multiprocessing.sharedctypes._AcquireFunc", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "multiprocessing.sharedctypes", "mro": ["multiprocessing.sharedctypes._AcquireFunc", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "multiprocessing.sharedctypes._AcquireFunc.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": [null, null, null], "arg_types": ["multiprocessing.sharedctypes._AcquireFunc", "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _AcquireFunc", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._AcquireFunc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "multiprocessing.sharedctypes._AcquireFunc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CData": {".class": "SymbolTableNode", "cross_ref": "_ctypes._CData", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "name": "_CT", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, "_LockLike": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.synchronize._LockLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SimpleCData": {".class": "SymbolTableNode", "cross_ref": "_ctypes._SimpleCData", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "multiprocessing.sharedctypes.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.sharedctypes.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.sharedctypes.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.sharedctypes.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.sharedctypes.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.sharedctypes.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.sharedctypes.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "c_char": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_char", "kind": "Gdef", "module_hidden": true, "module_public": false}, "copy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "multiprocessing.sharedctypes.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.copy", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.copy", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.copy", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}}, "ctypes": {".class": "SymbolTableNode", "cross_ref": "ctypes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "synchronized": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "multiprocessing.sharedctypes.synchronized", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.synchronized", "name": "synchronized", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "synchronized", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.synchronized", "name": "synchronized", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "synchronized", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.synchronized", "name": "synchronized", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "arg_types": [{".class": "Instance", "args": ["ctypes.c_char"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "synchronized", "ret_type": "multiprocessing.sharedctypes.SynchronizedString", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.synchronized", "name": "synchronized", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "arg_types": [{".class": "Instance", "args": ["ctypes.c_char"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "synchronized", "ret_type": "multiprocessing.sharedctypes.SynchronizedString", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.synchronized", "name": "synchronized", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "extra_attrs": null, "type_ref": "_ctypes.Array"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "synchronized", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.synchronized", "name": "synchronized", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "extra_attrs": null, "type_ref": "_ctypes.Array"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "synchronized", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.sharedctypes.synchronized", "name": "synchronized", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.synchronized", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "synchronized", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.synchronized", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.synchronized", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.sharedctypes.synchronized", "name": "synchronized", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.synchronized", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "synchronized", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.synchronized", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.synchronized", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "synchronized", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "arg_types": [{".class": "Instance", "args": ["ctypes.c_char"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "synchronized", "ret_type": "multiprocessing.sharedctypes.SynchronizedString", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "extra_attrs": null, "type_ref": "_ctypes.Array"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "synchronized", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._T", "id": -1, "name": "_T", "namespace": "multiprocessing.sharedctypes.synchronized#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["obj", "lock", "ctx"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.synchronized", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.synchronize._LockLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "synchronized", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.synchronized", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedBase"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.sharedctypes._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.sharedctypes.synchronized", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}]}}}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/mypy/typeshed/stdlib/multiprocessing/sharedctypes.pyi"}