{".class": "MypyFile", "_fullname": "fastapi.concurrency", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncGenerator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncGenerator", "kind": "Gdef"}, "CapacityLimiter": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.CapacityLimiter", "kind": "Gdef"}, "ContextManager": {".class": "SymbolTableNode", "cross_ref": "typing.ContextManager", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.concurrency._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.concurrency.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.concurrency.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.concurrency.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.concurrency.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.concurrency.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.concurrency.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "anyio": {".class": "SymbolTableNode", "cross_ref": "anyio", "kind": "Gdef"}, "asynccontextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.asynccontextmanager", "kind": "Gdef"}, "contextmanager_in_threadpool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cm"], "dataclass_transform_spec": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_decorated"], "fullname": "fastapi.concurrency.contextmanager_in_threadpool", "name": "contextmanager_in_threadpool", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cm"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.concurrency._T", "id": -1, "name": "_T", "namespace": "fastapi.concurrency.contextmanager_in_threadpool", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.ContextManager"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contextmanager_in_threadpool", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.concurrency._T", "id": -1, "name": "_T", "namespace": "fastapi.concurrency.contextmanager_in_threadpool", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.AsyncGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.concurrency._T", "id": -1, "name": "_T", "namespace": "fastapi.concurrency.contextmanager_in_threadpool", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fastapi.concurrency.contextmanager_in_threadpool", "name": "contextmanager_in_threadpool", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cm"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "contextlib._T_co", "id": 16966, "name": "_T_co", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.ContextManager"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contextmanager_in_threadpool", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "contextlib._T_co", "id": 16966, "name": "_T_co", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "contextlib._AsyncGeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "contextlib._T_co", "id": 16966, "name": "_T_co", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 1}]}}}}, "iterate_in_threadpool": {".class": "SymbolTableNode", "cross_ref": "starlette.concurrency.iterate_in_threadpool", "kind": "Gdef"}, "run_in_threadpool": {".class": "SymbolTableNode", "cross_ref": "starlette.concurrency.run_in_threadpool", "kind": "Gdef"}, "run_until_first_complete": {".class": "SymbolTableNode", "cross_ref": "starlette.concurrency.run_until_first_complete", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/fastapi/concurrency.py"}