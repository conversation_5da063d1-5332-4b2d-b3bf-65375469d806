{"data_mtime": 1751259992, "dep_lines": [54, 56, 57, 58, 22, 23, 49, 50, 55, 59, 61, 62, 63, 64, 71, 72, 73, 1, 2, 3, 4, 5, 21, 22, 60, 74, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 10, 20, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["fastapi.dependencies.models", "fastapi.security.base", "fastapi.security.oauth2", "fastapi.security.open_id_connect_url", "fastapi.params", "fastapi._compat", "fastapi.background", "fastapi.concurrency", "fastapi.logger", "fastapi.utils", "pydantic.fields", "starlette.background", "starlette.concurrency", "starlette.datastructures", "starlette.requests", "starlette.responses", "starlette.websockets", "inspect", "contextlib", "copy", "dataclasses", "typing", "anyio", "<PERSON><PERSON><PERSON>", "pydantic", "typing_extensions", "builtins", "abc", "importlib", "importlib.machinery", "starlette"], "hash": "4cd032deea88a2c0b5fc96aa9c026bc2948ebefe", "id": "fastapi.dependencies.utils", "ignore_all": true, "interface_hash": "3fa615c235ec69b7f01bfcd75de5b96366c252a5", "mtime": 1751256099, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/fastapi/dependencies/utils.py", "plugin_data": null, "size": 35579, "suppressed": [], "version_id": "1.13.0"}