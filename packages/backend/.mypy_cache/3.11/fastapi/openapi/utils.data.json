{".class": "MypyFile", "_fullname": "fastapi.openapi.utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseRoute": {".class": "SymbolTableNode", "cross_ref": "starlette.routing.BaseRoute", "kind": "Gdef"}, "Body": {".class": "SymbolTableNode", "cross_ref": "fastapi.params.Body", "kind": "Gdef"}, "DefaultPlaceholder": {".class": "SymbolTableNode", "cross_ref": "fastapi.datastructures.DefaultPlaceholder", "kind": "Gdef"}, "Dependant": {".class": "SymbolTableNode", "cross_ref": "fastapi.dependencies.models.Dependant", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GenerateJsonSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic.json_schema.GenerateJsonSchema", "kind": "Gdef"}, "HTTP_422_UNPROCESSABLE_ENTITY": {".class": "SymbolTableNode", "cross_ref": "starlette.status.HTTP_422_UNPROCESSABLE_ENTITY", "kind": "Gdef"}, "JSONResponse": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.JSONResponse", "kind": "Gdef"}, "JsonSchemaValue": {".class": "SymbolTableNode", "cross_ref": "pydantic.json_schema.JsonSchemaValue", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "METHODS_WITH_BODY": {".class": "SymbolTableNode", "cross_ref": "fastapi.openapi.constants.METHODS_WITH_BODY", "kind": "Gdef"}, "ModelField": {".class": "SymbolTableNode", "cross_ref": "fastapi._compat.ModelField", "kind": "Gdef"}, "ModelNameMap": {".class": "SymbolTableNode", "cross_ref": "fastapi.types.ModelNameMap", "kind": "Gdef"}, "OpenAPI": {".class": "SymbolTableNode", "cross_ref": "fastapi.openapi.models.OpenAPI", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ParamTypes": {".class": "SymbolTableNode", "cross_ref": "fastapi.params.ParamTypes", "kind": "Gdef"}, "REF_PREFIX": {".class": "SymbolTableNode", "cross_ref": "fastapi.openapi.constants.REF_PREFIX", "kind": "Gdef"}, "REF_TEMPLATE": {".class": "SymbolTableNode", "cross_ref": "fastapi.openapi.constants.REF_TEMPLATE", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.Response", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Undefined": {".class": "SymbolTableNode", "cross_ref": "fastapi._compat.Undefined", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.openapi.utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.openapi.utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.openapi.utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.openapi.utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.openapi.utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.openapi.utils.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_get_flat_fields_from_params": {".class": "SymbolTableNode", "cross_ref": "fastapi.dependencies.utils._get_flat_fields_from_params", "kind": "Gdef"}, "_get_openapi_operation_parameters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 3, 3, 5], "arg_names": ["dependant", "schema_generator", "model_name_map", "field_mapping", "separate_input_output_schemas"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.openapi.utils._get_openapi_operation_parameters", "name": "_get_openapi_operation_parameters", "type": {".class": "CallableType", "arg_kinds": [3, 3, 3, 3, 5], "arg_names": ["dependant", "schema_generator", "model_name_map", "field_mapping", "separate_input_output_schemas"], "arg_types": ["fastapi.dependencies.models.Dependant", "pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "fastapi.types.ModelNameMap"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["fastapi._compat.ModelField", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_openapi_operation_parameters", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "deep_dict_update": {".class": "SymbolTableNode", "cross_ref": "fastapi.utils.deep_dict_update", "kind": "Gdef"}, "generate_operation_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["route", "method"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.openapi.utils.generate_operation_id", "name": "generate_operation_id", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["route", "method"], "arg_types": ["fastapi.routing.APIRoute", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_operation_id", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_operation_id_for_path": {".class": "SymbolTableNode", "cross_ref": "fastapi.utils.generate_operation_id_for_path", "kind": "Gdef"}, "generate_operation_summary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["route", "method"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.openapi.utils.generate_operation_summary", "name": "generate_operation_summary", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["route", "method"], "arg_types": ["fastapi.routing.APIRoute", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_operation_summary", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_compat_model_name_map": {".class": "SymbolTableNode", "cross_ref": "fastapi._compat.get_compat_model_name_map", "kind": "Gdef"}, "get_definitions": {".class": "SymbolTableNode", "cross_ref": "fastapi._compat.get_definitions", "kind": "Gdef"}, "get_fields_from_routes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["routes"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.openapi.utils.get_fields_from_routes", "name": "get_fields_from_routes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["routes"], "arg_types": [{".class": "Instance", "args": ["starlette.routing.BaseRoute"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fields_from_routes", "ret_type": {".class": "Instance", "args": ["fastapi._compat.ModelField"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flat_dependant": {".class": "SymbolTableNode", "cross_ref": "fastapi.dependencies.utils.get_flat_dependant", "kind": "Gdef"}, "get_flat_params": {".class": "SymbolTableNode", "cross_ref": "fastapi.dependencies.utils.get_flat_params", "kind": "Gdef"}, "get_openapi": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["title", "version", "openapi_version", "summary", "description", "routes", "webhooks", "tags", "servers", "terms_of_service", "contact", "license_info", "separate_input_output_schemas"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.openapi.utils.get_openapi", "name": "get_openapi", "type": {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["title", "version", "openapi_version", "summary", "description", "routes", "webhooks", "tags", "servers", "terms_of_service", "contact", "license_info", "separate_input_output_schemas"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["starlette.routing.BaseRoute"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["starlette.routing.BaseRoute"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_openapi", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_openapi_operation_metadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 3], "arg_names": ["route", "method", "operation_ids"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.openapi.utils.get_openapi_operation_metadata", "name": "get_openapi_operation_metadata", "type": {".class": "CallableType", "arg_kinds": [3, 3, 3], "arg_names": ["route", "method", "operation_ids"], "arg_types": ["fastapi.routing.APIRoute", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_openapi_operation_metadata", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_openapi_operation_request_body": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 3, 3, 5], "arg_names": ["body_field", "schema_generator", "model_name_map", "field_mapping", "separate_input_output_schemas"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.openapi.utils.get_openapi_operation_request_body", "name": "get_openapi_operation_request_body", "type": {".class": "CallableType", "arg_kinds": [3, 3, 3, 3, 5], "arg_names": ["body_field", "schema_generator", "model_name_map", "field_mapping", "separate_input_output_schemas"], "arg_types": [{".class": "UnionType", "items": ["fastapi._compat.ModelField", {".class": "NoneType"}], "uses_pep604_syntax": false}, "pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "fastapi.types.ModelNameMap"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["fastapi._compat.ModelField", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_openapi_operation_request_body", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_openapi_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 3, 3, 3, 5], "arg_names": ["route", "operation_ids", "schema_generator", "model_name_map", "field_mapping", "separate_input_output_schemas"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.openapi.utils.get_openapi_path", "name": "get_openapi_path", "type": {".class": "CallableType", "arg_kinds": [3, 3, 3, 3, 3, 5], "arg_names": ["route", "operation_ids", "schema_generator", "model_name_map", "field_mapping", "separate_input_output_schemas"], "arg_types": ["fastapi.routing.APIRoute", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "fastapi.types.ModelNameMap"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["fastapi._compat.ModelField", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_openapi_path", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_openapi_security_definitions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["flat_dependant"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.openapi.utils.get_openapi_security_definitions", "name": "get_openapi_security_definitions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["flat_dependant"], "arg_types": ["fastapi.dependencies.models.Dependant"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_openapi_security_definitions", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_schema_from_model_field": {".class": "SymbolTableNode", "cross_ref": "fastapi._compat.get_schema_from_model_field", "kind": "Gdef"}, "http": {".class": "SymbolTableNode", "cross_ref": "http", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_body_allowed_for_status_code": {".class": "SymbolTableNode", "cross_ref": "fastapi.utils.is_body_allowed_for_status_code", "kind": "Gdef"}, "jsonable_encoder": {".class": "SymbolTableNode", "cross_ref": "fastapi.encoders.jsonable_encoder", "kind": "Gdef"}, "lenient_issubclass": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.lenient_issubclass", "kind": "Gdef"}, "routing": {".class": "SymbolTableNode", "cross_ref": "fastapi.routing", "kind": "Gdef"}, "status_code_ranges": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "fastapi.openapi.utils.status_code_ranges", "name": "status_code_ranges", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "validation_error_definition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "fastapi.openapi.utils.validation_error_definition", "name": "validation_error_definition", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "validation_error_response_definition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "fastapi.openapi.utils.validation_error_response_definition", "name": "validation_error_response_definition", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/fastapi/openapi/utils.py"}