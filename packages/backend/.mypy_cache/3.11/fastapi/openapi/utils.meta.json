{"data_mtime": **********, "dep_lines": [18, 19, 25, 26, 1, 6, 7, 17, 24, 27, 28, 29, 30, 35, 36, 37, 1, 2, 3, 4, 6, 38, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 10, 10, 5, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.dependencies.models", "fastapi.dependencies.utils", "fastapi.openapi.constants", "fastapi.openapi.models", "http.client", "fastapi.routing", "fastapi._compat", "fastapi.datastructures", "fastapi.encoders", "fastapi.params", "fastapi.responses", "fastapi.types", "fastapi.utils", "starlette.responses", "starlette.routing", "starlette.status", "http", "inspect", "warnings", "typing", "<PERSON><PERSON><PERSON>", "typing_extensions", "builtins", "abc", "enum", "fastapi.dependencies", "importlib", "importlib.machinery", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.json_schema", "pydantic.main", "starlette"], "hash": "fddcfce4b8130a0b0c8c95f8fae8bb61ffb7c47a", "id": "fastapi.openapi.utils", "ignore_all": true, "interface_hash": "efd44d2a52fd1976b9a9659121cabf120b157ef9", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/fastapi/openapi/utils.py", "plugin_data": null, "size": 23177, "suppressed": [], "version_id": "1.13.0"}