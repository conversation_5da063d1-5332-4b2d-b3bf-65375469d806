{"data_mtime": **********, "dep_lines": [5, 7, 8, 9, 10, 12, 21, 22, 23, 24, 5, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30], "dependencies": ["starlette.status", "fastapi.applications", "fastapi.background", "fastapi.datastructures", "fastapi.exceptions", "fastapi.param_functions", "fastapi.requests", "fastapi.responses", "fastapi.routing", "fastapi.websockets", "starlette", "builtins", "abc", "importlib", "importlib.machinery", "typing"], "hash": "7476a239f281a83b9dd09af5368b7e312247ea0f", "id": "<PERSON><PERSON><PERSON>", "ignore_all": true, "interface_hash": "436387bade0b24ee96ddf4ca0b33d9b79f0ca19b", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/fastapi/__init__.py", "plugin_data": null, "size": 1081, "suppressed": [], "version_id": "1.13.0"}