{"data_mtime": 1751259991, "dep_lines": [6, 8, 9, 5, 11, 12, 1, 2, 3, 10, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.openapi.models", "fastapi.security.base", "fastapi.security.utils", "fastapi.exceptions", "starlette.requests", "starlette.status", "<PERSON><PERSON><PERSON><PERSON>", "base64", "typing", "pydantic", "typing_extensions", "builtins", "abc", "enum", "fastapi.openapi", "importlib", "importlib.machinery", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "starlette"], "hash": "4c6d4a89416be7d880183d5ce0ace3dcc6ffd4a3", "id": "fastapi.security.http", "ignore_all": true, "interface_hash": "4168f87356ef70c2d57d74e1013d2733d98b1557", "mtime": 1751256099, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/fastapi/security/http.py", "plugin_data": null, "size": 13512, "suppressed": [], "version_id": "1.13.0"}