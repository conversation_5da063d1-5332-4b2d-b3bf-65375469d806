{".class": "MypyFile", "_fullname": "fastapi.security.oauth2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Annotated", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Doc": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Doc", "kind": "Gdef"}, "Form": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.Form", "kind": "Gdef"}, "HTTPException": {".class": "SymbolTableNode", "cross_ref": "fastapi.exceptions.HTTPException", "kind": "Gdef"}, "HTTP_401_UNAUTHORIZED": {".class": "SymbolTableNode", "cross_ref": "starlette.status.HTTP_401_UNAUTHORIZED", "kind": "Gdef"}, "HTTP_403_FORBIDDEN": {".class": "SymbolTableNode", "cross_ref": "starlette.status.HTTP_403_FORBIDDEN", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "OAuth2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["fastapi.security.base.SecurityBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fastapi.security.oauth2.OAuth2", "name": "OAuth2", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "fastapi.security.oauth2.OAuth2", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "fastapi.security.oauth2", "mro": ["fastapi.security.oauth2.OAuth2", "fastapi.security.base.SecurityBase", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "fastapi.security.oauth2.OAuth2.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["fastapi.security.oauth2.OAuth2", "starlette.requests.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of OAuth2", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "flows", "scheme_name", "description", "auto_error"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.security.oauth2.OAuth2.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "flows", "scheme_name", "description", "auto_error"], "arg_types": ["fastapi.security.oauth2.OAuth2", {".class": "UnionType", "items": ["fastapi.openapi.models.OAuthFlows", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OAuth2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auto_error": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "fastapi.security.oauth2.OAuth2.auto_error", "name": "auto_error", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.security.oauth2.OAuth2.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fastapi.security.oauth2.OAuth2", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OAuth2AuthorizationCodeBearer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["fastapi.security.oauth2.OAuth2"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fastapi.security.oauth2.OAuth2AuthorizationCodeBearer", "name": "OAuth2AuthorizationCodeBearer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "fastapi.security.oauth2.OAuth2AuthorizationCodeBearer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "fastapi.security.oauth2", "mro": ["fastapi.security.oauth2.OAuth2AuthorizationCodeBearer", "fastapi.security.oauth2.OAuth2", "fastapi.security.base.SecurityBase", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "fastapi.security.oauth2.OAuth2AuthorizationCodeBearer.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["fastapi.security.oauth2.OAuth2AuthorizationCodeBearer", "starlette.requests.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of OAuth2AuthorizationCodeBearer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "authorizationUrl", "tokenUrl", "refreshUrl", "scheme_name", "scopes", "description", "auto_error"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.security.oauth2.OAuth2AuthorizationCodeBearer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "authorizationUrl", "tokenUrl", "refreshUrl", "scheme_name", "scopes", "description", "auto_error"], "arg_types": ["fastapi.security.oauth2.OAuth2AuthorizationCodeBearer", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OAuth2AuthorizationCodeBearer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.security.oauth2.OAuth2AuthorizationCodeBearer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fastapi.security.oauth2.OAuth2AuthorizationCodeBearer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OAuth2Model": {".class": "SymbolTableNode", "cross_ref": "fastapi.openapi.models.OAuth2", "kind": "Gdef"}, "OAuth2PasswordBearer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["fastapi.security.oauth2.OAuth2"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fastapi.security.oauth2.OAuth2PasswordBearer", "name": "OAuth2PasswordBearer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "fastapi.security.oauth2.OAuth2PasswordBearer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "fastapi.security.oauth2", "mro": ["fastapi.security.oauth2.OAuth2PasswordBearer", "fastapi.security.oauth2.OAuth2", "fastapi.security.base.SecurityBase", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "fastapi.security.oauth2.OAuth2PasswordBearer.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["fastapi.security.oauth2.OAuth2PasswordBearer", "starlette.requests.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of OAuth2PasswordBearer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "tokenUrl", "scheme_name", "scopes", "description", "auto_error"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.security.oauth2.OAuth2PasswordBearer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "tokenUrl", "scheme_name", "scopes", "description", "auto_error"], "arg_types": ["fastapi.security.oauth2.OAuth2PasswordBearer", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OAuth2PasswordBearer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.security.oauth2.OAuth2PasswordBearer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fastapi.security.oauth2.OAuth2PasswordBearer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OAuth2PasswordRequestForm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fastapi.security.oauth2.OAuth2PasswordRequestForm", "name": "OAuth2PasswordRequestForm", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "fastapi.security.oauth2.OAuth2PasswordRequestForm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "fastapi.security.oauth2", "mro": ["fastapi.security.oauth2.OAuth2PasswordRequestForm", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 5, 5, 5], "arg_names": ["self", "grant_type", "username", "password", "scope", "client_id", "client_secret"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.security.oauth2.OAuth2PasswordRequestForm.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 5, 5, 5], "arg_names": ["self", "grant_type", "username", "password", "scope", "client_id", "client_secret"], "arg_types": ["fastapi.security.oauth2.OAuth2PasswordRequestForm", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OAuth2PasswordRequestForm", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "client_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "fastapi.security.oauth2.OAuth2PasswordRequestForm.client_id", "name": "client_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "client_secret": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "fastapi.security.oauth2.OAuth2PasswordRequestForm.client_secret", "name": "client_secret", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "grant_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "fastapi.security.oauth2.OAuth2PasswordRequestForm.grant_type", "name": "grant_type", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "password": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "fastapi.security.oauth2.OAuth2PasswordRequestForm.password", "name": "password", "type": "builtins.str"}}, "scopes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "fastapi.security.oauth2.OAuth2PasswordRequestForm.scopes", "name": "scopes", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "username": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "fastapi.security.oauth2.OAuth2PasswordRequestForm.username", "name": "username", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.security.oauth2.OAuth2PasswordRequestForm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fastapi.security.oauth2.OAuth2PasswordRequestForm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OAuth2PasswordRequestFormStrict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["fastapi.security.oauth2.OAuth2PasswordRequestForm"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fastapi.security.oauth2.OAuth2PasswordRequestFormStrict", "name": "OAuth2PasswordRequestFormStrict", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "fastapi.security.oauth2.OAuth2PasswordRequestFormStrict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "fastapi.security.oauth2", "mro": ["fastapi.security.oauth2.OAuth2PasswordRequestFormStrict", "fastapi.security.oauth2.OAuth2PasswordRequestForm", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "grant_type", "username", "password", "scope", "client_id", "client_secret"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.security.oauth2.OAuth2PasswordRequestFormStrict.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "grant_type", "username", "password", "scope", "client_id", "client_secret"], "arg_types": ["fastapi.security.oauth2.OAuth2PasswordRequestFormStrict", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OAuth2PasswordRequestFormStrict", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.security.oauth2.OAuth2PasswordRequestFormStrict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fastapi.security.oauth2.OAuth2PasswordRequestFormStrict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OAuthFlowsModel": {".class": "SymbolTableNode", "cross_ref": "fastapi.openapi.models.OAuthFlows", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "starlette.requests.Request", "kind": "Gdef"}, "SecurityBase": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.base.SecurityBase", "kind": "Gdef"}, "SecurityScopes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fastapi.security.oauth2.SecurityScopes", "name": "SecurityScopes", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "fastapi.security.oauth2.SecurityScopes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "fastapi.security.oauth2", "mro": ["fastapi.security.oauth2.SecurityScopes", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "scopes"], "dataclass_transform_spec": null, "flags": [], "fullname": "fastapi.security.oauth2.SecurityScopes.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "scopes"], "arg_types": ["fastapi.security.oauth2.SecurityScopes", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SecurityScopes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scope_str": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "fastapi.security.oauth2.SecurityScopes.scope_str", "name": "scope_str", "type": "builtins.str"}}, "scopes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "fastapi.security.oauth2.SecurityScopes.scopes", "name": "scopes", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi.security.oauth2.SecurityScopes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fastapi.security.oauth2.SecurityScopes", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.oauth2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.oauth2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.oauth2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.oauth2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.oauth2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.security.oauth2.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "get_authorization_scheme_param": {".class": "SymbolTableNode", "cross_ref": "fastapi.security.utils.get_authorization_scheme_param", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/fastapi/security/oauth2.py"}