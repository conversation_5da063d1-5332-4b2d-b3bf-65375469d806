{"data_mtime": **********, "dep_lines": [25, 30, 38, 16, 17, 18, 23, 24, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 1, 2, 16, 43, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.openapi.docs", "fastapi.openapi.utils", "starlette.middleware.base", "fastapi.routing", "fastapi.datastructures", "fastapi.exception_handlers", "fastapi.exceptions", "fastapi.logger", "fastapi.params", "fastapi.types", "fastapi.utils", "starlette.applications", "starlette.datastructures", "starlette.exceptions", "starlette.middleware", "starlette.requests", "starlette.responses", "starlette.routing", "starlette.types", "enum", "typing", "<PERSON><PERSON><PERSON>", "typing_extensions", "builtins", "_typeshed", "abc", "contextlib", "fastapi.openapi", "importlib", "importlib.machinery", "logging", "starlette", "starlette.background", "starlette.websockets"], "hash": "edd59adec2964cbbe87fb84fd502e430beb8e926", "id": "fastapi.applications", "ignore_all": true, "interface_hash": "00217c12822117d26df746ee4b26db750b7a137a", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/fastapi/applications.py", "plugin_data": null, "size": 176316, "suppressed": [], "version_id": "1.13.0"}