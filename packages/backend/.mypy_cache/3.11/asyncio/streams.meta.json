{"data_mtime": 1751259988, "dep_lines": [4, 9, 9, 9, 10, 1, 2, 3, 5, 6, 7, 9, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 10, 10, 5, 5, 5, 5, 20, 5, 30, 30, 30], "dependencies": ["collections.abc", "asyncio.events", "asyncio.protocols", "asyncio.transports", "asyncio.base_events", "ssl", "sys", "_typeshed", "types", "typing", "typing_extensions", "asyncio", "builtins", "abc", "importlib", "importlib.machinery"], "hash": "789b03394e7b8fb254962ee62289c820729049c5", "id": "asyncio.streams", "ignore_all": true, "interface_hash": "2bfc0672fe88c7b8c5eec52079aa5fc34910d0ce", "mtime": 1751256095, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/mypy/typeshed/stdlib/asyncio/streams.pyi", "plugin_data": null, "size": 5942, "suppressed": [], "version_id": "1.13.0"}