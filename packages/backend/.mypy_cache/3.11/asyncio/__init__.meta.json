{"data_mtime": 1751259988, "dep_lines": [2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 22, 25, 26, 31, 1, 3, 4, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "asyncio.base_events", "asyncio.coroutines", "asyncio.events", "asyncio.exceptions", "asyncio.futures", "asyncio.locks", "asyncio.protocols", "asyncio.queues", "asyncio.runners", "asyncio.streams", "asyncio.subprocess", "asyncio.tasks", "asyncio.transports", "asyncio.threads", "asyncio.taskgroups", "asyncio.timeouts", "asyncio.unix_events", "sys", "typing", "typing_extensions", "builtins", "_typeshed", "abc", "importlib", "importlib.machinery", "types"], "hash": "4b247430b65220705c22aef6e2712d87ba416a28", "id": "asyncio", "ignore_all": true, "interface_hash": "d2a923ae7cd56399f7cfd3bb78b784c0454645bd", "mtime": 1751256095, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/mypy/typeshed/stdlib/asyncio/__init__.pyi", "plugin_data": null, "size": 1221, "suppressed": [], "version_id": "1.13.0"}