{"data_mtime": 1751259988, "dep_lines": [5, 6, 7, 8, 9, 10, 1, 2, 3, 4, 11, 12, 13, 14, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["asyncio.events", "asyncio.futures", "asyncio.protocols", "asyncio.tasks", "asyncio.transports", "collections.abc", "ssl", "sys", "_typeshed", "asyncio", "<PERSON><PERSON><PERSON>", "socket", "typing", "typing_extensions", "builtins", "_socket", "abc", "enum", "importlib", "importlib.machinery", "types"], "hash": "f78b0d5bce214984b37d4b5372bd1f7b692942c6", "id": "asyncio.base_events", "ignore_all": true, "interface_hash": "212f5495fbdbf17073865cb571554ae6e1c728f0", "mtime": 1751256095, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/mypy/typeshed/stdlib/asyncio/base_events.pyi", "plugin_data": null, "size": 19450, "suppressed": [], "version_id": "1.13.0"}