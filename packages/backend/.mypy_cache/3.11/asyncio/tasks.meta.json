{"data_mtime": 1751259988, "dep_lines": [1, 3, 9, 10, 1, 2, 4, 5, 6, 8, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["concurrent.futures", "collections.abc", "asyncio.events", "asyncio.futures", "concurrent", "sys", "types", "typing", "typing_extensions", "asyncio", "<PERSON><PERSON><PERSON>", "builtins", "_typeshed", "abc", "anyio", "anyio._core", "anyio._core._eventloop", "concurrent.futures._base", "importlib", "importlib.machinery"], "hash": "024d09c60ec2e3ff3d0ec0438913d8360f8438bc", "id": "asyncio.tasks", "ignore_all": true, "interface_hash": "2885994e9c96c6fe179e0c5b8065f6128a4b1ee9", "mtime": 1751256095, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/mypy/typeshed/stdlib/asyncio/tasks.pyi", "plugin_data": null, "size": 18583, "suppressed": [], "version_id": "1.13.0"}