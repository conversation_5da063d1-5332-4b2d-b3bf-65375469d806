{".class": "MypyFile", "_fullname": "sqlalchemy.engine.cursor", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"Executable\" and \"ClauseElement\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.Executable", "sqlalchemy.sql.elements.ClauseElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.cursor.<subclass of \"Executable\" and \"ClauseElement\">", "name": "<subclass of \"Executable\" and \"ClauseElement\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "sqlalchemy.engine.cursor.<subclass of \"Executable\" and \"ClauseElement\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.cursor", "mro": ["sqlalchemy.engine.cursor.<subclass of \"Executable\" and \"ClauseElement\">", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BufferedRowCursorFetchStrategy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.cursor.CursorFetchStrategy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy", "name": "BufferedRowCursorFetchStrategy", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.cursor", "mro": ["sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy", "sqlalchemy.engine.cursor.CursorFetchStrategy", "sqlalchemy.engine.cursor.ResultFetchStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "dbapi_cursor", "execution_options", "growth_factor", "initial_buffer"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_buffer_rows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy._buffer_rows", "name": "_buffer_rows", "type": null}}, "_bufsize": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy._bufsize", "name": "_bufsize", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "_growth_factor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy._growth_factor", "name": "_growth_factor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_max_row_buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy._max_row_buffer", "name": "_max_row_buffer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "_rowbuffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy._rowbuffer", "name": "_rowbuffer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "result"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy.create", "name": "create", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "result"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of BufferedRowCursorFetchStrategy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fetchall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy.fetchall", "name": "fetchall", "type": null}}, "fetchmany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "dbapi_cursor", "size"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy.fetchmany", "name": "fetchmany", "type": null}}, "fetchone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "dbapi_cursor", "hard_close"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy.fetchone", "name": "fetchone", "type": null}}, "hard_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy.hard_close", "name": "hard_close", "type": null}}, "soft_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy.soft_close", "name": "soft_close", "type": null}}, "yield_per": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor", "num"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy.yield_per", "name": "yield_per", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.cursor.BufferedRowCursorFetchStrategy", "values": [], "variance": 0}, "slots": ["_bufsize", "_growth_factor", "_max_row_buffer", "_rowbuffer"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "CursorFetchStrategy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.cursor.ResultFetchStrategy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.cursor.CursorFetchStrategy", "name": "CursorFetchStrategy", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorFetchStrategy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.cursor", "mro": ["sqlalchemy.engine.cursor.CursorFetchStrategy", "sqlalchemy.engine.cursor.ResultFetchStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.engine.cursor.CursorFetchStrategy.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "fetchall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorFetchStrategy.fetchall", "name": "fetchall", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "arg_types": ["sqlalchemy.engine.cursor.CursorFetchStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "sqlalchemy.engine.interfaces.DBAPICursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetchall of CursorFetchStrategy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fetchmany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "dbapi_cursor", "size"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorFetchStrategy.fetchmany", "name": "fetchmany", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "dbapi_cursor", "size"], "arg_types": ["sqlalchemy.engine.cursor.CursorFetchStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "sqlalchemy.engine.interfaces.DBAPICursor", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetchmany of CursorFetchStrategy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fetchone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "dbapi_cursor", "hard_close"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorFetchStrategy.fetchone", "name": "fetchone", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "dbapi_cursor", "hard_close"], "arg_types": ["sqlalchemy.engine.cursor.CursorFetchStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "sqlalchemy.engine.interfaces.DBAPICursor", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetchone of CursorFetchStrategy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor", "err"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorFetchStrategy.handle_exception", "name": "handle_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor", "err"], "arg_types": ["sqlalchemy.engine.cursor.CursorFetchStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.DBAPICursor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_exception of CursorFetchStrategy", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hard_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorFetchStrategy.hard_close", "name": "hard_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "arg_types": ["sqlalchemy.engine.cursor.CursorFetchStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.DBAPICursor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hard_close of CursorFetchStrategy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "soft_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorFetchStrategy.soft_close", "name": "soft_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "arg_types": ["sqlalchemy.engine.cursor.CursorFetchStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.DBAPICursor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "soft_close of CursorFetchStrategy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "yield_per": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor", "num"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorFetchStrategy.yield_per", "name": "yield_per", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor", "num"], "arg_types": ["sqlalchemy.engine.cursor.CursorFetchStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.DBAPICursor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "yield_per of CursorFetchStrategy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor.CursorFetchStrategy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.cursor.CursorFetchStrategy", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CursorResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.Result"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.cursor.CursorResult", "name": "CursorResult", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.cursor", "mro": ["sqlalchemy.engine.cursor.CursorResult", "sqlalchemy.engine.result.Result", "sqlalchemy.engine.result._<PERSON><PERSON><PERSON><PERSON>", "sqlalchemy.engine.result.ResultInternal", "sqlalchemy.sql.base.InPlaceGenerative", "sqlalchemy.util.langhelpers.HasMemoized", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "cursor_strategy", "cursor_description"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "cursor_strategy", "cursor_description"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "sqlalchemy.engine.default.DefaultExecutionContext", "sqlalchemy.engine.cursor.ResultFetchStrategy", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPICursorDescription"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CursorResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.engine.cursor.CursorResult.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_echo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.engine.cursor.CursorResult._echo", "name": "_echo", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "uses_pep604_syntax": false}}}, "_fetchall_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult._fetchall_impl", "name": "_fetchall_impl", "type": null}}, "_fetchiter_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult._fetchiter_impl", "name": "_fetchiter_impl", "type": null}}, "_fetchmany_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "size"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult._fetchmany_impl", "name": "_fetchmany_impl", "type": null}}, "_fetchone_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "hard_close"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult._fetchone_impl", "name": "_fetchone_impl", "type": null}}, "_init_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "cursor_description"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult._init_metadata", "name": "_init_metadata", "type": null}}, "_is_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor.CursorResult._is_cursor", "name": "_is_cursor", "type": "builtins.bool"}}, "_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.cursor.CursorResult._metadata", "name": "_metadata", "type": {".class": "UnionType", "items": ["sqlalchemy.engine.cursor.CursorResultMetaData", "sqlalchemy.engine.cursor._NoResultMetaData"], "uses_pep604_syntax": false}}}, "_no_result_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor.CursorResult._no_result_metadata", "name": "_no_result_metadata", "type": "sqlalchemy.engine.cursor._NoResultMetaData"}}, "_raw_row_iterator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult._raw_row_iterator", "name": "_raw_row_iterator", "type": null}}, "_rewind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rows"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult._rewind", "name": "_rewind", "type": null}}, "_soft_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "hard"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult._soft_close", "name": "_soft_close", "type": null}}, "_soft_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor.CursorResult._soft_closed", "name": "_soft_closed", "type": "builtins.bool"}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of CursorResult", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor.CursorResult.closed", "name": "closed", "type": "builtins.bool"}}, "connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.cursor.CursorResult.connection", "name": "connection", "type": "sqlalchemy.engine.base.Connection"}}, "context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.cursor.CursorResult.context", "name": "context", "type": "sqlalchemy.engine.default.DefaultExecutionContext"}}, "cursor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.engine.cursor.CursorResult.cursor", "name": "cursor", "type": "sqlalchemy.engine.interfaces.DBAPICursor"}}, "cursor_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.cursor.CursorResult.cursor_strategy", "name": "cursor_strategy", "type": "sqlalchemy.engine.cursor.ResultFetchStrategy"}}, "dialect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.cursor.CursorResult.dialect", "name": "dialect", "type": "sqlalchemy.engine.interfaces.Dialect"}}, "inserted_primary_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.cursor.CursorResult.inserted_primary_key", "name": "inserted_primary_key", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.cursor.CursorResult.inserted_primary_key", "name": "inserted_primary_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inserted_primary_key of CursorResult", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "inserted_primary_key_rows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.cursor.CursorResult.inserted_primary_key_rows", "name": "inserted_primary_key_rows", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.cursor.CursorResult.inserted_primary_key_rows", "name": "inserted_primary_key_rows", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inserted_primary_key_rows of CursorResult", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.cursor.CursorResult.is_insert", "name": "is_insert", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.cursor.CursorResult.is_insert", "name": "is_insert", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_insert of CursorResult", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "last_inserted_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult.last_inserted_params", "name": "last_inserted_params", "type": null}}, "last_updated_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult.last_updated_params", "name": "last_updated_params", "type": null}}, "lastrow_has_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult.lastrow_has_defaults", "name": "lastrow_has_defaults", "type": null}}, "lastrowid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.cursor.CursorResult.lastrowid", "name": "lastrowid", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.cursor.CursorResult.lastrowid", "name": "lastrowid", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "last<PERSON><PERSON> of CursorResult", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "merge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "others"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult.merge", "name": "merge", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "others"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.Result"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge of CursorResult", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.MergedResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "postfetch_cols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult.postfetch_cols", "name": "postfetch_cols", "type": null}}, "prefetch_cols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult.prefetch_cols", "name": "prefetch_cols", "type": null}}, "returned_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.cursor.CursorResult.returned_defaults", "name": "returned_defaults", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.cursor.CursorResult.returned_defaults", "name": "returned_defaults", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "returned_defaults of CursorResult", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "returned_defaults_rows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.cursor.CursorResult.returned_defaults_rows", "name": "returned_defaults_rows", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.cursor.CursorResult.returned_defaults_rows", "name": "returned_defaults_rows", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "returned_defaults_rows of CursorResult", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "returns_rows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.cursor.CursorResult.returns_rows", "name": "returns_rows", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.cursor.CursorResult.returns_rows", "name": "returns_rows", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "returns_rows of CursorResult", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.cursor.CursorResult.rowcount", "name": "rowcount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rowcount of CursorResult", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.cursor.CursorResult.rowcount", "name": "rowcount", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "splice_horizontally": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult.splice_horizontally", "name": "splice_horizontally", "type": null}}, "splice_vertically": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult.splice_vertically", "name": "splice_vertically", "type": null}}, "supports_sane_multi_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult.supports_sane_multi_rowcount", "name": "supports_sane_multi_rowcount", "type": null}}, "supports_sane_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResult.supports_sane_rowcount", "name": "supports_sane_rowcount", "type": null}}, "yield_per": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "num"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.cursor.CursorResult.yield_per", "name": "yield_per", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "num"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor.CursorResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "values": [], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "yield_per of CursorResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor.CursorResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor.CursorResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.cursor.CursorResult.yield_per", "name": "yield_per", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "num"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor.CursorResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "values": [], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "yield_per of CursorResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor.CursorResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor.CursorResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor.CursorResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.engine.cursor.CursorResult", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "CursorResultMetaData": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.result.ResultMetaData"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData", "name": "CursorResultMetaData", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.cursor", "mro": ["sqlalchemy.engine.cursor.CursorResultMetaData", "sqlalchemy.engine.result.ResultMetaData", "builtins.object"], "names": {".class": "SymbolTable", "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData.__getstate__", "name": "__getstate__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "cursor_description"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "cursor_description"], "arg_types": ["sqlalchemy.engine.cursor.CursorResultMetaData", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPICursorDescription"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CursorResultMetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData.__setstate__", "name": "__setstate__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_adapt_to_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._adapt_to_context", "name": "_adapt_to_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["sqlalchemy.engine.cursor.CursorResultMetaData", "sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_adapt_to_context of CursorResultMetaData", "ret_type": "sqlalchemy.engine.result.ResultMetaData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_colnames_from_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "cursor_description"], "dataclass_transform_spec": null, "flags": ["is_generator"], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._colnames_from_description", "name": "_colnames_from_description", "type": null}}, "_create_description_match_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "result_columns", "loose_column_name_matching"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._create_description_match_map", "name": "_create_description_match_map", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "result_columns", "loose_column_name_matching"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.cursor.CursorResultMetaData"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler.ResultColumnsEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_description_match_map of CursorResultMetaData", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.object"], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._create_description_match_map", "name": "_create_description_match_map", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "result_columns", "loose_column_name_matching"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.cursor.CursorResultMetaData"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler.ResultColumnsEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_description_match_map of CursorResultMetaData", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.object"], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_for_freeze": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._for_freeze", "name": "_for_freeze", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.cursor.CursorResultMetaData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_for_freeze of CursorResultMetaData", "ret_type": "sqlalchemy.engine.result.ResultMetaData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_has_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._has_key", "name": "_has_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["sqlalchemy.engine.cursor.CursorResultMetaData", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_key of CursorResultMetaData", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_index_for_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "raiseerr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._index_for_key", "name": "_index_for_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "raiseerr"], "arg_types": ["sqlalchemy.engine.cursor.CursorResultMetaData", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_index_for_key of CursorResultMetaData", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_indexes_for_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._indexes_for_keys", "name": "_indexes_for_keys", "type": null}}, "_keymap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._keymap", "name": "_keymap", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.cursor._CursorKeyMapType"}}}, "_keymap_by_result_column_idx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._keymap_by_result_column_idx", "name": "_keymap_by_result_column_idx", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.result._KeyMapRecType"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_make_new_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "unpickled", "processors", "keys", "keymap", "tuplefilter", "translated_indexes", "safe_for_cache", "keymap_by_result_column_idx"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._make_new_metadata", "name": "_make_new_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "unpickled", "processors", "keys", "keymap", "tuplefilter", "translated_indexes", "safe_for_cache", "keymap_by_result_column_idx"], "arg_types": ["sqlalchemy.engine.cursor.CursorResultMetaData", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.result._ProcessorsType"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.result._KeyMapType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.result._TupleGetterType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_new_metadata of CursorResultMetaData", "ret_type": "sqlalchemy.engine.cursor.CursorResultMetaData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_merge_cols_by_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "context", "cursor_description", "result_columns", "loose_column_name_matching"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._merge_cols_by_name", "name": "_merge_cols_by_name", "type": null}}, "_merge_cols_by_none": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "cursor_description"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._merge_cols_by_none", "name": "_merge_cols_by_none", "type": null}}, "_merge_cursor_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "cursor_description", "result_columns", "num_ctx_cols", "cols_are_ordered", "textual_ordered", "ad_hoc_textual", "loose_column_name_matching"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._merge_cursor_description", "name": "_merge_cursor_description", "type": null}}, "_merge_textual_cols_by_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "cursor_description", "result_columns"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._merge_textual_cols_by_position", "name": "_merge_textual_cols_by_position", "type": null}}, "_metadata_for_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._metadata_for_keys", "name": "_metadata_for_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "arg_types": ["sqlalchemy.engine.cursor.CursorResultMetaData", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_metadata_for_keys of CursorResultMetaData", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.cursor._NonAmbigCursorKeyMapRecType"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_processors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._processors", "name": "_processors", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.result._ProcessorsType"}}}, "_raise_for_ambiguous_column_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rec"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._raise_for_ambiguous_column_name", "name": "_raise_for_ambiguous_column_name", "type": null}}, "_reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._reduce", "name": "_reduce", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "arg_types": ["sqlalchemy.engine.cursor.CursorResultMetaData", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.result._KeyIndexType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reduce of CursorResultMetaData", "ret_type": "sqlalchemy.engine.result.ResultMetaData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_remove_processors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._remove_processors", "name": "_remove_processors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.cursor.CursorResultMetaData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_processors of CursorResultMetaData", "ret_type": "sqlalchemy.engine.cursor.CursorResultMetaData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_safe_for_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._safe_for_cache", "name": "_safe_for_cache", "type": "builtins.bool"}}, "_splice_horizontally": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._splice_horizontally", "name": "_splice_horizontally", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["sqlalchemy.engine.cursor.CursorResultMetaData", "sqlalchemy.engine.cursor.CursorResultMetaData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_splice_horizontally of CursorResultMetaData", "ret_type": "sqlalchemy.engine.cursor.CursorResultMetaData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_translated_indexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._translated_indexes", "name": "_translated_indexes", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_unpickled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData._unpickled", "name": "_unpickled", "type": "builtins.bool"}}, "returns_rows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData.returns_rows", "name": "returns_rows", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor.CursorResultMetaData.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.cursor.CursorResultMetaData", "values": [], "variance": 0}, "slots": ["_key_to_index", "_keymap", "_keymap_by_result_column_idx", "_keys", "_processors", "_safe_for_cache", "_translated_indexes", "_tuplefilter", "_unpickled"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBAPICursor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.DBAPICursor", "kind": "Gdef"}, "DefaultExecutionContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.default.DefaultExecutionContext", "kind": "Gdef"}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ExecutionContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ExecutionContext", "kind": "Gdef"}, "FullyBufferedCursorFetchStrategy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.cursor.CursorFetchStrategy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.cursor.FullyBufferedCursorFetchStrategy", "name": "FullyBufferedCursorFetchStrategy", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.cursor.FullyBufferedCursorFetchStrategy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.cursor", "mro": ["sqlalchemy.engine.cursor.FullyBufferedCursorFetchStrategy", "sqlalchemy.engine.cursor.CursorFetchStrategy", "sqlalchemy.engine.cursor.ResultFetchStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "dbapi_cursor", "alternate_description", "initial_buffer"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.FullyBufferedCursorFetchStrategy.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.engine.cursor.FullyBufferedCursorFetchStrategy.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_rowbuffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.engine.cursor.FullyBufferedCursorFetchStrategy._rowbuffer", "name": "_rowbuffer", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "collections.deque"}}}, "fetchall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.FullyBufferedCursorFetchStrategy.fetchall", "name": "fetchall", "type": null}}, "fetchmany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "dbapi_cursor", "size"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.FullyBufferedCursorFetchStrategy.fetchmany", "name": "fetchmany", "type": null}}, "fetchone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "dbapi_cursor", "hard_close"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.FullyBufferedCursorFetchStrategy.fetchone", "name": "fetchone", "type": null}}, "hard_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.FullyBufferedCursorFetchStrategy.hard_close", "name": "hard_close", "type": null}}, "soft_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.FullyBufferedCursorFetchStrategy.soft_close", "name": "soft_close", "type": null}}, "yield_per": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor", "num"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.FullyBufferedCursorFetchStrategy.yield_per", "name": "yield_per", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor.FullyBufferedCursorFetchStrategy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.cursor.FullyBufferedCursorFetchStrategy", "values": [], "variance": 0}, "slots": ["_rowbuffer", "alternate_cursor_description"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "IteratorResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.IteratorResult", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "MD_INDEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor.MD_INDEX", "name": "MD_INDEX", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}}}, "MD_LOOKUP_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor.MD_LOOKUP_KEY", "name": "MD_LOOKUP_KEY", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}}}, "MD_OBJECTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor.MD_OBJECTS", "name": "MD_OBJECTS", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}}}, "MD_PROCESSOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor.MD_PROCESSOR", "name": "MD_PROCESSOR", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}}}, "MD_RENDERED_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor.MD_RENDERED_NAME", "name": "MD_RENDERED_NAME", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}}}, "MD_RESULT_MAP_INDEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor.MD_RESULT_MAP_INDEX", "name": "MD_RESULT_MAP_INDEX", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}}}, "MD_UNTRANSLATED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor.MD_UNTRANSLATED", "name": "MD_UNTRANSLATED", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 6}}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MergedResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.MergedResult", "kind": "Gdef"}, "NoCursorDMLFetchStrategy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.cursor.NoCursorFetchStrategy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.cursor.NoCursorDMLFetchStrategy", "name": "NoCursorDMLFetchStrategy", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.cursor.NoCursorDMLFetchStrategy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.cursor", "mro": ["sqlalchemy.engine.cursor.NoCursorDMLFetchStrategy", "sqlalchemy.engine.cursor.NoCursorFetchStrategy", "sqlalchemy.engine.cursor.ResultFetchStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.engine.cursor.NoCursorDMLFetchStrategy.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_non_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "default", "err"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.NoCursorDMLFetchStrategy._non_result", "name": "_non_result", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor.NoCursorDMLFetchStrategy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.cursor.NoCursorDMLFetchStrategy", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoCursorDQLFetchStrategy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.cursor.NoCursorFetchStrategy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.cursor.NoCursorDQLFetchStrategy", "name": "NoCursorDQLFetchStrategy", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.cursor.NoCursorDQLFetchStrategy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.cursor", "mro": ["sqlalchemy.engine.cursor.NoCursorDQLFetchStrategy", "sqlalchemy.engine.cursor.NoCursorFetchStrategy", "sqlalchemy.engine.cursor.ResultFetchStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.engine.cursor.NoCursorDQLFetchStrategy.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_non_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "default", "err"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.NoCursorDQLFetchStrategy._non_result", "name": "_non_result", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor.NoCursorDQLFetchStrategy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.cursor.NoCursorDQLFetchStrategy", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoCursorFetchStrategy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.cursor.ResultFetchStrategy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.cursor.NoCursorFetchStrategy", "name": "NoCursorFetchStrategy", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.cursor.NoCursorFetchStrategy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.cursor", "mro": ["sqlalchemy.engine.cursor.NoCursorFetchStrategy", "sqlalchemy.engine.cursor.ResultFetchStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.engine.cursor.NoCursorFetchStrategy.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_non_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "default", "err"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.NoCursorFetchStrategy._non_result", "name": "_non_result", "type": null}}, "fetchall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.NoCursorFetchStrategy.fetchall", "name": "fetchall", "type": null}}, "fetchmany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "dbapi_cursor", "size"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.NoCursorFetchStrategy.fetchmany", "name": "fetchmany", "type": null}}, "fetchone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "dbapi_cursor", "hard_close"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.NoCursorFetchStrategy.fetchone", "name": "fetchone", "type": null}}, "hard_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.NoCursorFetchStrategy.hard_close", "name": "hard_close", "type": null}}, "soft_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.NoCursorFetchStrategy.soft_close", "name": "soft_close", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor.NoCursorFetchStrategy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.cursor.NoCursorFetchStrategy", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RM_NAME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.RM_NAME", "kind": "Gdef"}, "RM_OBJECTS": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.RM_OBJECTS", "kind": "Gdef"}, "RM_RENDERED_NAME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.RM_RENDERED_NAME", "kind": "Gdef"}, "RM_TYPE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.RM_TYPE", "kind": "Gdef"}, "Result": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.Result", "kind": "Gdef"}, "ResultColumnsEntry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.ResultColumnsEntry", "kind": "Gdef"}, "ResultFetchStrategy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.cursor.ResultFetchStrategy", "name": "ResultFetchStrategy", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.cursor.ResultFetchStrategy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.cursor", "mro": ["sqlalchemy.engine.cursor.ResultFetchStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.engine.cursor.ResultFetchStrategy.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "alternate_cursor_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor.ResultFetchStrategy.alternate_cursor_description", "name": "alternate_cursor_description", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPICursorDescription"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "fetchall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.ResultFetchStrategy.fetchall", "name": "fetchall", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "arg_types": ["sqlalchemy.engine.cursor.ResultFetchStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "sqlalchemy.engine.interfaces.DBAPICursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetchall of ResultFetchStrategy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fetchmany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "dbapi_cursor", "size"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.ResultFetchStrategy.fetchmany", "name": "fetchmany", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "dbapi_cursor", "size"], "arg_types": ["sqlalchemy.engine.cursor.ResultFetchStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "sqlalchemy.engine.interfaces.DBAPICursor", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetchmany of ResultFetchStrategy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fetchone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "dbapi_cursor", "hard_close"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.ResultFetchStrategy.fetchone", "name": "fetchone", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "result", "dbapi_cursor", "hard_close"], "arg_types": ["sqlalchemy.engine.cursor.ResultFetchStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "sqlalchemy.engine.interfaces.DBAPICursor", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetchone of ResultFetchStrategy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor", "err"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.ResultFetchStrategy.handle_exception", "name": "handle_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor", "err"], "arg_types": ["sqlalchemy.engine.cursor.ResultFetchStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.DBAPICursor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_exception of ResultFetchStrategy", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hard_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.ResultFetchStrategy.hard_close", "name": "hard_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "arg_types": ["sqlalchemy.engine.cursor.ResultFetchStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.DBAPICursor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hard_close of ResultFetchStrategy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "soft_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.ResultFetchStrategy.soft_close", "name": "soft_close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor"], "arg_types": ["sqlalchemy.engine.cursor.ResultFetchStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.DBAPICursor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "soft_close of ResultFetchStrategy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "yield_per": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor", "num"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.ResultFetchStrategy.yield_per", "name": "yield_per", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "result", "dbapi_cursor", "num"], "arg_types": ["sqlalchemy.engine.cursor.ResultFetchStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.DBAPICursor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "yield_per of ResultFetchStrategy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor.ResultFetchStrategy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.cursor.ResultFetchStrategy", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResultMetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.ResultMetaData", "kind": "Gdef"}, "ResultProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.cursor.ResultProxy", "line": 2181, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}}}, "Row": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.row.Row", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SimpleResultMetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.SimpleResultMetaData", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_CursorKeyMapRecType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.cursor._CursorKeyMapRecType", "line": 117, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._ResultProcessorType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_CursorKeyMapType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.cursor._CursorKeyMapType", "line": 127, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.result._KeyType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.cursor._CursorKeyMapRecType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "_DBAPICursorDescription": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._DBAPICursorDescription", "kind": "Gdef"}, "_DEFAULT_FETCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor._DEFAULT_FETCH", "name": "_DEFAULT_FETCH", "type": "sqlalchemy.engine.cursor.CursorFetchStrategy"}}, "_KeyIndexType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result._KeyIndexType", "kind": "Gdef"}, "_KeyMapRecType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result._KeyMapRecType", "kind": "Gdef"}, "_KeyMapType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result._KeyMapType", "kind": "Gdef"}, "_KeyType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result._KeyType", "kind": "Gdef"}, "_NO_CURSOR_DML": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor._NO_CURSOR_DML", "name": "_NO_CURSOR_DML", "type": "sqlalchemy.engine.cursor.NoCursorDMLFetchStrategy"}}, "_NO_CURSOR_DQL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor._NO_CURSOR_DQL", "name": "_NO_CURSOR_DQL", "type": "sqlalchemy.engine.cursor.NoCursorDQLFetchStrategy"}}, "_NO_RESULT_METADATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor._NO_RESULT_METADATA", "name": "_NO_RESULT_METADATA", "type": "sqlalchemy.engine.cursor._NoResultMetaData"}}, "_NoResultMetaData": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.result.ResultMetaData"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.cursor._NoResultMetaData", "name": "_NoResultMetaData", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.cursor._NoResultMetaData", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.cursor", "mro": ["sqlalchemy.engine.cursor._NoResultMetaData", "sqlalchemy.engine.result.ResultMetaData", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.engine.cursor._NoResultMetaData.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_index_for_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "keys", "raiseerr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor._NoResultMetaData._index_for_key", "name": "_index_for_key", "type": null}}, "_key_to_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.cursor._NoResultMetaData._key_to_index", "name": "_key_to_index", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.cursor._NoResultMetaData._key_to_index", "name": "_key_to_index", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.cursor._NoResultMetaData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_key_to_index of _NoResultMetaData", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_keymap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.cursor._NoResultMetaData._keymap", "name": "_keymap", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.cursor._NoResultMetaData._keymap", "name": "_keymap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.cursor._NoResultMetaData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_keymap of _NoResultMetaData", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_metadata_for_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor._NoResultMetaData._metadata_for_keys", "name": "_metadata_for_keys", "type": null}}, "_processors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.cursor._NoResultMetaData._processors", "name": "_processors", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.cursor._NoResultMetaData._processors", "name": "_processors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.cursor._NoResultMetaData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_processors of _NoResultMetaData", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor._NoResultMetaData._reduce", "name": "_reduce", "type": null}}, "_we_dont_return_rows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "err"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor._NoResultMetaData._we_dont_return_rows", "name": "_we_dont_return_rows", "type": null}}, "keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.cursor._NoResultMetaData.keys", "name": "keys", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.cursor._NoResultMetaData.keys", "name": "keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.cursor._NoResultMetaData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keys of _NoResultMetaData", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "returns_rows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.cursor._NoResultMetaData.returns_rows", "name": "returns_rows", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._NoResultMetaData.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.cursor._NoResultMetaData", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_NonAmbigCursorKeyMapRecType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.engine.cursor._NonAmbigCursorKeyMapRecType", "line": 131, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._ResultProcessorType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_ProcessorsType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result._ProcessorsType", "kind": "Gdef"}, "_ResultProcessorType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api._ResultProcessorType", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.cursor._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_TupleGetterType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result._TupleGetterType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.cursor.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.cursor.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.cursor.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.cursor.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.cursor.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.cursor.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_generative": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._generative", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "compat": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat", "kind": "Gdef"}, "elements": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "null_dml_result": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.cursor.null_dml_result", "name": "null_dml_result", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "null_dml_result", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.IteratorResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "sql_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.util", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes", "kind": "Gdef"}, "tuplegetter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine._py_row.tuplegetter", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/cursor.py"}