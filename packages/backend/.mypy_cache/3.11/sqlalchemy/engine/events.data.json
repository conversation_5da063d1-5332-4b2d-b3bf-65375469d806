{".class": "MypyFile", "_fullname": "sqlalchemy.engine.events", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BindParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BindParameter", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "ConnectionEvents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["sqlalchemy.engine.interfaces.ConnectionEventsTarget"], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.Events"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.events.ConnectionEvents", "name": "ConnectionEvents", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.events", "mro": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.event.base.Events", "sqlalchemy.event.base._HasEventsDispatch", "builtins.object"], "names": {".class": "SymbolTable", "_accept_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.events.ConnectionEvents._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.events.ConnectionEvents"}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.ConnectionEventsTarget", {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.ConnectionEventsTarget"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of ConnectionEvents", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.ConnectionEventsTarget", {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.ConnectionEventsTarget"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.events.ConnectionEvents._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.events.ConnectionEvents"}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.ConnectionEventsTarget", {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.ConnectionEventsTarget"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of ConnectionEvents", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.ConnectionEventsTarget", {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.ConnectionEventsTarget"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_dispatch_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.events.ConnectionEvents._dispatch_target", "name": "_dispatch_target", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["sqlalchemy.engine.interfaces.ConnectionEventsTarget"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.engine.interfaces.ConnectionEventsTarget", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 4], "arg_names": ["cls", "event_key", "retval", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.events.ConnectionEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["cls", "event_key", "retval", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.events.ConnectionEvents"}, {".class": "Instance", "args": ["sqlalchemy.engine.interfaces.ConnectionEventsTarget"], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.events.ConnectionEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["cls", "event_key", "retval", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.events.ConnectionEvents"}, {".class": "Instance", "args": ["sqlalchemy.engine.interfaces.ConnectionEventsTarget"], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_target_class_doc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.events.ConnectionEvents._target_class_doc", "name": "_target_class_doc", "type": "builtins.str"}}, "after_cursor_execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "conn", "cursor", "statement", "parameters", "context", "executemany"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents.after_cursor_execute", "name": "after_cursor_execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "conn", "cursor", "statement", "parameters", "context", "executemany"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection", "sqlalchemy.engine.interfaces.DBAPICursor", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.ExecutionContext", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_cursor_execute of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "after_execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "conn", "clauseelement", "multiparams", "params", "execution_options", "result"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.events.ConnectionEvents.after_execute", "name": "after_execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "conn", "clauseelement", "multiparams", "params", "execution_options", "result"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection", "sqlalchemy.sql.base.Executable", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.Result"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_execute of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.events.ConnectionEvents.after_execute", "name": "after_execute", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "after_execute of ConnectionEvents", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "before_cursor_execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "conn", "cursor", "statement", "parameters", "context", "executemany"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents.before_cursor_execute", "name": "before_cursor_execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "conn", "cursor", "statement", "parameters", "context", "executemany"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection", "sqlalchemy.engine.interfaces.DBAPICursor", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.ExecutionContext", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_cursor_execute of ConnectionEvents", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "before_execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "conn", "clauseelement", "multiparams", "params", "execution_options"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.events.ConnectionEvents.before_execute", "name": "before_execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "conn", "clauseelement", "multiparams", "params", "execution_options"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection", "sqlalchemy.sql.base.Executable", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_execute of ConnectionEvents", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["sqlalchemy.sql.base.Executable", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.events.ConnectionEvents.before_execute", "name": "before_execute", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "before_execute of ConnectionEvents", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents.begin", "name": "begin", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "begin of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "begin_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "conn", "xid"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents.begin_twophase", "name": "begin_twophase", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "conn", "xid"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "begin_twophase of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents.commit", "name": "commit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "commit of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "commit_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "conn", "xid", "is_prepared"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents.commit_twophase", "name": "commit_twophase", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "conn", "xid", "is_prepared"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "commit_twophase of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "engine_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.events.ConnectionEvents.engine_connect", "name": "engine_connect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "engine_connect of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.events.ConnectionEvents.engine_connect", "name": "engine_connect", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "engine_connect of ConnectionEvents", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "engine_disposed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "engine"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents.engine_disposed", "name": "engine_disposed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "engine"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Engine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "engine_disposed of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "conn", "xid"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents.prepare_twophase", "name": "prepare_twophase", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "conn", "xid"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_twophase of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "release_savepoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "conn", "name", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents.release_savepoint", "name": "release_savepoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "conn", "name", "context"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection", "builtins.str", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release_savepoint of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rollback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents.rollback", "name": "rollback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rollback of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rollback_savepoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "conn", "name", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents.rollback_savepoint", "name": "rollback_savepoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "conn", "name", "context"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection", "builtins.str", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rollback_savepoint of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rollback_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "conn", "xid", "is_prepared"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents.rollback_twophase", "name": "rollback_twophase", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "conn", "xid", "is_prepared"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rollback_twophase of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "savepoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "conn", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents.savepoint", "name": "savepoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "conn", "name"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "savepoint of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_connection_execution_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "conn", "opts"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents.set_connection_execution_options", "name": "set_connection_execution_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "conn", "opts"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Connection", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_connection_execution_options of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_engine_execution_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "engine", "opts"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.ConnectionEvents.set_engine_execution_options", "name": "set_engine_execution_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "engine", "opts"], "arg_types": ["sqlalchemy.engine.events.ConnectionEvents", "sqlalchemy.engine.base.Engine", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_engine_execution_options of ConnectionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.events.ConnectionEvents.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.events.ConnectionEvents", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionEventsTarget": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ConnectionEventsTarget", "kind": "Gdef"}, "ConnectionPoolEntry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.ConnectionPoolEntry", "kind": "Gdef"}, "DBAPIConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.DBAPIConnection", "kind": "Gdef"}, "DBAPICursor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.DBAPICursor", "kind": "Gdef"}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "DialectEvents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["sqlalchemy.engine.interfaces.Dialect"], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.Events"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.events.DialectEvents", "name": "DialectEvents", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.engine.events.DialectEvents", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.events", "mro": ["sqlalchemy.engine.events.DialectEvents", "sqlalchemy.event.base.Events", "sqlalchemy.event.base._HasEventsDispatch", "builtins.object"], "names": {".class": "SymbolTable", "_accept_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.events.DialectEvents._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.events.DialectEvents"}, {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", {".class": "TypeType", "item": "sqlalchemy.engine.base.Engine"}, "sqlalchemy.engine.interfaces.Dialect", {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of DialectEvents", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.events.DialectEvents._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.events.DialectEvents"}, {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", {".class": "TypeType", "item": "sqlalchemy.engine.base.Engine"}, "sqlalchemy.engine.interfaces.Dialect", {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of DialectEvents", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_dispatch_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.events.DialectEvents._dispatch_target", "name": "_dispatch_target", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["sqlalchemy.engine.interfaces.Dialect"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.engine.interfaces.Dialect", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 4], "arg_names": ["cls", "event_key", "retval", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.events.DialectEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["cls", "event_key", "retval", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.events.DialectEvents"}, {".class": "Instance", "args": ["sqlalchemy.engine.interfaces.Dialect"], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of DialectEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.events.DialectEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["cls", "event_key", "retval", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.events.DialectEvents"}, {".class": "Instance", "args": ["sqlalchemy.engine.interfaces.Dialect"], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of DialectEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_target_class_doc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.events.DialectEvents._target_class_doc", "name": "_target_class_doc", "type": "builtins.str"}}, "do_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "dialect", "conn_rec", "cargs", "cparams"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.DialectEvents.do_connect", "name": "do_connect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "dialect", "conn_rec", "cargs", "cparams"], "arg_types": ["sqlalchemy.engine.events.DialectEvents", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.pool.base.ConnectionPoolEntry", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_connect of DialectEvents", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.DBAPIConnection", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "cursor", "statement", "parameters", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.DialectEvents.do_execute", "name": "do_execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "cursor", "statement", "parameters", "context"], "arg_types": ["sqlalchemy.engine.events.DialectEvents", "sqlalchemy.engine.interfaces.DBAPICursor", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}, "sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_execute of DialectEvents", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_execute_no_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cursor", "statement", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.DialectEvents.do_execute_no_params", "name": "do_execute_no_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cursor", "statement", "context"], "arg_types": ["sqlalchemy.engine.events.DialectEvents", "sqlalchemy.engine.interfaces.DBAPICursor", "builtins.str", "sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_execute_no_params of DialectEvents", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_executemany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "cursor", "statement", "parameters", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.DialectEvents.do_executemany", "name": "do_executemany", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "cursor", "statement", "parameters", "context"], "arg_types": ["sqlalchemy.engine.events.DialectEvents", "sqlalchemy.engine.interfaces.DBAPICursor", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams"}, "sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_executemany of DialectEvents", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_setinputsizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "inputsizes", "cursor", "statement", "parameters", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.DialectEvents.do_setinputsizes", "name": "do_setinputsizes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "inputsizes", "cursor", "statement", "parameters", "context"], "arg_types": ["sqlalchemy.engine.events.DialectEvents", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "sqlalchemy.engine.interfaces.DBAPICursor", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, "sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_setinputsizes of DialectEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.events.DialectEvents.handle_error", "name": "handle_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception_context"], "arg_types": ["sqlalchemy.engine.events.DialectEvents", "sqlalchemy.engine.interfaces.ExceptionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_error of DialectEvents", "ret_type": {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.events.DialectEvents.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.events.DialectEvents", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Engine", "kind": "Gdef"}, "ExceptionContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ExceptionContext", "kind": "Gdef"}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef"}, "ExecutionContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ExecutionContext", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Result": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.Result", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_CoreMultiExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams", "kind": "Gdef"}, "_CoreSingleExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams", "kind": "Gdef"}, "_DBAPIAnyExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams", "kind": "Gdef"}, "_DBAPIMultiExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams", "kind": "Gdef"}, "_DBAPISingleExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams", "kind": "Gdef"}, "_ExecuteOptions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._ExecuteOptions", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.events.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.events.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.events.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.events.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.events.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.events.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "event": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/events.py"}