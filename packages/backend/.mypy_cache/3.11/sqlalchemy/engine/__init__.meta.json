{"data_mtime": 1751259990, "dep_lines": [18, 19, 20, 26, 29, 31, 42, 43, 46, 56, 59, 62, 62, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.events", "sqlalchemy.engine.util", "sqlalchemy.engine.base", "sqlalchemy.engine.create", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.mock", "sqlalchemy.engine.reflection", "sqlalchemy.engine.result", "sqlalchemy.engine.row", "sqlalchemy.engine.url", "sqlalchemy.sql.ddl", "sqlalchemy.sql", "builtins", "abc", "importlib", "importlib.machinery", "typing"], "hash": "a9306c345a3f4d70efad16b8812f9fafb24120e3", "id": "sqlalchemy.engine", "ignore_all": true, "interface_hash": "e301c8b51b6c29ccb5a743ea29a016fbfc51746c", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/__init__.py", "plugin_data": null, "size": 2818, "suppressed": [], "version_id": "1.13.0"}