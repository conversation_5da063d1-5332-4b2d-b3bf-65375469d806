{".class": "MypyFile", "_fullname": "sqlalchemy.sql._typing", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Alias": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.<PERSON><PERSON>", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CTE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.CTE", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "ColumnExpressionArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing.ColumnExpressionArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.sql._typing.ColumnExpressionArgument", "line": 196, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing.ColumnExpressionArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}}}, "Compiled": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.Compiled", "kind": "Gdef"}, "DDLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.DDLCompiler", "kind": "Gdef"}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "_decimal.Decimal", "kind": "Gdef"}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef"}, "FromClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.FromClause", "kind": "Gdef"}, "FromClauseRole": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles.FromClauseRole", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "Inspectable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection.Inspectable", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Join": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Join", "kind": "Gdef"}, "KeyedColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.KeyedColumnElement", "kind": "Gdef"}, "LambdaElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.lambdas.LambdaElement", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "NamedFromClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.NamedFromClause", "kind": "Gdef"}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef"}, "NotNullable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.sql._typing.NotNullable", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.sql._typing.NotNullable", "name": "NotNullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NotNullable", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql._typing.NotNullable", "name": "NotNullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NotNullable", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql._typing.NotNullable", "name": "NotNullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NotNullable", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql._typing.NotNullable", "name": "NotNullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NotNullable", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql._typing.NotNullable", "name": "NotNullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NotNullable", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql._typing.NotNullable", "name": "NotNullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "NoneType"}}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NotNullable", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql._typing.NotNullable", "name": "NotNullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "NoneType"}}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NotNullable", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql._typing.NotNullable", "name": "NotNullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#3", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NotNullable", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#3", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#3", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql._typing.NotNullable", "name": "NotNullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#3", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NotNullable", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#3", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#3", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NotNullable", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NotNullable", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "NoneType"}}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NotNullable", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#3", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NotNullable", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#3", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.NotNullable#3", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}]}}}, "Nullable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.sql._typing.Nullable", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.sql._typing.Nullable", "name": "Nullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Nullable", "ret_type": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "type_ref": "sqlalchemy.sql._typing._TypedColumnClauseArgument"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql._typing.Nullable", "name": "Nullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Nullable", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql._typing.Nullable", "name": "Nullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Nullable", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql._typing.Nullable", "name": "Nullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Nullable", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql._typing.Nullable", "name": "Nullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Nullable", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql._typing.Nullable", "name": "Nullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Nullable", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "NoneType"}}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql._typing.Nullable", "name": "Nullable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Nullable", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "NoneType"}}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Nullable", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Nullable", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Nullable", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "NoneType"}}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._typing.Nullable#2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}]}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "ReturnsRows": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.ReturnsRows", "kind": "Gdef"}, "SQLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.SQLCompiler", "kind": "Gdef"}, "SQLCoreOperations": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.SQLCoreOperations", "kind": "Gdef"}, "Select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Select", "kind": "Gdef"}, "SelectBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.SelectBase", "kind": "Gdef"}, "Selectable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Selectable", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "Subquery": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Subquery", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TableClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TableClause", "kind": "Gdef"}, "TableValueType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.TableValueType", "kind": "Gdef"}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TupleType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.TupleType", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing.TypeGuard", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "UUID": {".class": "SymbolTableNode", "cross_ref": "uuid.UUID", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UpdateBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.UpdateBase", "kind": "Gdef"}, "ValuesBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.ValuesBase", "kind": "Gdef"}, "_AutoIncrementType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql._typing._AutoIncrementType", "line": 299, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ignore_fk"}], "uses_pep604_syntax": false}}}, "_ByArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql._typing._ByArgument", "line": 215, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionOrStrLabelArgument"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionOrStrLabelArgument"}], "uses_pep604_syntax": false}}}, "_CE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._CE", "name": "_CE", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}}, "_CLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._CLE", "name": "_CLE", "upper_bound": "sqlalchemy.sql.elements.ClauseElement", "values": [], "variance": 0}}, "_ColumnExpressionArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnExpressionArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.sql._typing._ColumnExpressionArgument", "line": 185, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnExpressionArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnExpressionArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnExpressionArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnExpressionArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnExpressionArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.TypedColumnsClauseRole"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnExpressionArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "sqlalchemy.sql.lambdas.LambdaElement"], "uses_pep604_syntax": false}}}, "_ColumnExpressionOrLiteralArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnExpressionOrLiteralArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.sql._typing._ColumnExpressionOrLiteralArgument", "line": 211, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnExpressionOrLiteralArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "uses_pep604_syntax": false}}}, "_ColumnExpressionOrStrLabelArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnExpressionOrStrLabelArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.sql._typing._ColumnExpressionOrStrLabelArgument", "line": 213, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnExpressionOrStrLabelArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "uses_pep604_syntax": false}}}, "_ColumnsClauseArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnsClauseArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.sql._typing._ColumnsClauseArgument", "line": 146, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnsClauseArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.TypedColumnsClauseRole"}, "sqlalchemy.sql.roles.ColumnsClauseRole", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnsClauseArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._StarOrOne"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnsClauseArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnsClauseArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}], "extra_attrs": null, "type_ref": "sqlalchemy.inspection.Inspectable"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._ColumnsClauseArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}], "uses_pep604_syntax": false}}}, "_CoreAdapterProto": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql._typing._CoreAdapterProto", "name": "_CoreAdapterProto", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.sql._typing._CoreAdapterProto", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.sql._typing", "mro": ["sqlalchemy.sql._typing._CoreAdapterProto", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.sql._typing._CoreAdapterProto.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql._typing._CoreAdapterProto", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._CE", "id": -1, "name": "_CE", "namespace": "sqlalchemy.sql._typing._CoreAdapterProto.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _CoreAdapterProto", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._CE", "id": -1, "name": "_CE", "namespace": "sqlalchemy.sql._typing._CoreAdapterProto.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._CE", "id": -1, "name": "_CE", "namespace": "sqlalchemy.sql._typing._CoreAdapterProto.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._CoreAdapterProto.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql._typing._CoreAdapterProto", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DDLColumnArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql._typing._DDLColumnArgument", "line": 274, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "sqlalchemy.sql.roles.DDLConstraintColumnRole"], "uses_pep604_syntax": false}}}, "_DMLColumnArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql._typing._DMLColumnArgument", "line": 254, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}, "sqlalchemy.sql.roles.DMLColumnRole", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}], "uses_pep604_syntax": false}}}, "_DMLColumnKeyMapping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._DMLKey", "id": 1, "name": "_DML<PERSON>ey", "namespace": "sqlalchemy.sql._typing._DMLColumnKeyMapping", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}, "sqlalchemy.sql.roles.DMLColumnRole", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.sql._typing._DMLColumnKeyMapping", "line": 271, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._DMLKey", "id": 1, "name": "_DML<PERSON>ey", "namespace": "sqlalchemy.sql._typing._DMLColumnKeyMapping", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}, "sqlalchemy.sql.roles.DMLColumnRole", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "_DMLKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._DMLKey", "name": "_DML<PERSON>ey", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}, "sqlalchemy.sql.roles.DMLColumnRole", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "_DMLTableArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql._typing._DMLTableArgument", "line": 281, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.TableClause", "sqlalchemy.sql.selectable.Join", "sqlalchemy.sql.selectable.<PERSON><PERSON>", "sqlalchemy.sql.selectable.CTE", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}], "extra_attrs": null, "type_ref": "sqlalchemy.inspection.Inspectable"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}], "uses_pep604_syntax": false}}}, "_EquivalentColumnMap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql._typing._EquivalentColumnMap", "line": 295, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_FromClauseArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql._typing._FromClauseArgument", "line": 225, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["sqlalchemy.sql.roles.FromClauseRole", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}], "extra_attrs": null, "type_ref": "sqlalchemy.inspection.Inspectable"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}], "uses_pep604_syntax": false}}}, "_HasClauseElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__clause_element__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement", "name": "_Has<PERSON><PERSON>eElement", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}]}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.sql._typing", "mro": ["sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement", "builtins.object"], "names": {".class": "SymbolTable", "__clause_element__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.sql._typing._HasClauseElement.__clause_element__", "name": "__clause_element__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__clause_element__ of _<PERSON><PERSON><PERSON>eElement", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._HasClauseElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "_HasDialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["dialect", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql._typing._HasDialect", "name": "_HasDialect", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.sql._typing._HasDialect", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.sql._typing", "mro": ["sqlalchemy.sql._typing._HasDialect", "builtins.object"], "names": {".class": "SymbolTable", "dialect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "sqlalchemy.sql._typing._HasDialect.dialect", "name": "dialect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql._typing._HasDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dialect of _HasDialect", "ret_type": "sqlalchemy.engine.interfaces.Dialect", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql._typing._HasDialect.dialect", "name": "dialect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql._typing._HasDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dialect of _HasDialect", "ret_type": "sqlalchemy.engine.interfaces.Dialect", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._HasDialect.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql._typing._HasDialect", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_InfoType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql._typing._InfoType", "line": 222, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_JoinTargetArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql._typing._JoinTargetArgument", "line": 237, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._FromClauseArgument"}, "sqlalchemy.sql.roles.JoinTargetRole"], "uses_pep604_syntax": false}}}, "_LimitOffsetType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql._typing._LimitOffsetType", "line": 297, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.int", {".class": "TypeAliasType", "args": ["builtins.int"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_MAYBE_ENTITY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._MAYBE_ENTITY", "name": "_MAYBE_ENTITY", "upper_bound": "builtins.object", "values": ["sqlalchemy.sql.roles.ColumnsClauseRole", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._StarOrOne"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}], "extra_attrs": null, "type_ref": "sqlalchemy.inspection.Inspectable"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}], "variance": 0}}, "_NOT_ENTITY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._NOT_ENTITY", "name": "_NOT_ENTITY", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.str", "builtins.bool", "datetime.datetime", "datetime.date", "datetime.time", "datetime.<PERSON><PERSON><PERSON>", "uuid.UUID", "builtins.float", "_decimal.Decimal"], "variance": 0}}, "_OnClauseArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql._typing._OnClauseArgument", "line": 243, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, "sqlalchemy.sql.roles.OnClauseRole"], "uses_pep604_syntax": false}}}, "_PropagateAttrsType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql._typing._PropagateAttrsType", "line": 291, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "_SelectStatementForCompoundArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql._typing._SelectStatementForCompoundArgument", "line": 249, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.SelectBase", "sqlalchemy.sql.roles.CompoundElementRole"], "uses_pep604_syntax": false}}}, "_StarOrOne": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql._typing._StarOrOne", "line": 120, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "*"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "uses_pep604_syntax": false}}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_T0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T0", "name": "_T0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_T1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T1", "name": "_T1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_T2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T2", "name": "_T2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_T3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T3", "name": "_T3", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_T4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T4", "name": "_T4", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_T5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T5", "name": "_T5", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_T6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T6", "name": "_T6", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_T7": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T7", "name": "_T7", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_T8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T8", "name": "_T8", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_T9": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T9", "name": "_T9", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_TP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._TP", "name": "_TP", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}}, "_T_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T_co", "name": "_T_co", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}}, "_TextCoercedExpressionArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._TextCoercedExpressionArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.sql._typing._TextCoercedExpressionArgument", "line": 138, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.elements.TextClause", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._TextCoercedExpressionArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._TextCoercedExpressionArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._TextCoercedExpressionArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}], "uses_pep604_syntax": false}}}, "_TypeEngineArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._TypeEngineArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.sql._typing._TypeEngineArgument", "line": 293, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._TypeEngineArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._TypeEngineArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}}}, "_TypedColumnClauseArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._TypedColumnClauseArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.sql._typing._TypedColumnClauseArgument", "line": 165, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._TypedColumnClauseArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.TypedColumnsClauseRole"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._TypedColumnClauseArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql._typing._TypedColumnClauseArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}], "uses_pep604_syntax": false}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._typing.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._typing.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._typing.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._typing.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._typing.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._typing.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_no_kw": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql._typing._no_kw", "name": "_no_kw", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_no_kw", "ret_type": "sqlalchemy.exc.ArgumentError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unexpected_kw": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["<PERSON><PERSON>ame", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql._typing._unexpected_kw", "name": "_unexpected_kw", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["<PERSON><PERSON>ame", "kw"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unexpected_kw", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "date": {".class": "SymbolTableNode", "cross_ref": "datetime.date", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "has_schema_attr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql._typing.has_schema_attr", "name": "has_schema_attr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": ["sqlalchemy.sql.roles.FromClauseRole"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_schema_attr", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.selectable.TableClause", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_column_element": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql._typing.is_column_element", "name": "is_column_element", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["c"], "arg_types": ["sqlalchemy.sql.elements.ClauseElement"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_column_element", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_ddl_compiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql._typing.is_ddl_compiler", "name": "is_ddl_compiler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["c"], "arg_types": ["sqlalchemy.sql.compiler.Compiled"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_ddl_compiler", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.compiler.DDLCompiler", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_dml": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql._typing.is_dml", "name": "is_dml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["c"], "arg_types": ["sqlalchemy.sql.elements.ClauseElement"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_dml", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.dml.UpdateBase", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_from_clause": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql._typing.is_from_clause", "name": "is_from_clause", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["c"], "arg_types": ["sqlalchemy.sql.elements.ClauseElement"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_from_clause", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.selectable.FromClause", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_has_clause_element": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql._typing.is_has_clause_element", "name": "is_has_clause_element", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_has_clause_element", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"}, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_insert_update": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql._typing.is_insert_update", "name": "is_insert_update", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["c"], "arg_types": ["sqlalchemy.sql.elements.ClauseElement"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_insert_update", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.dml.ValuesBase", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_keyed_column_element": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql._typing.is_keyed_column_element", "name": "is_keyed_column_element", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["c"], "arg_types": ["sqlalchemy.sql.elements.ClauseElement"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_keyed_column_element", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.KeyedColumnElement"}, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_named_from_clause": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql._typing.is_named_from_clause", "name": "is_named_from_clause", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": ["sqlalchemy.sql.roles.FromClauseRole"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_named_from_clause", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.selectable.NamedFromClause", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_quoted_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql._typing.is_quoted_name", "name": "is_quoted_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_quoted_name", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.elements.quoted_name", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_select_base": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql._typing.is_select_base", "name": "is_select_base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.sql.base.Executable", "sqlalchemy.sql.selectable.ReturnsRows"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_select_base", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.selectable.SelectBase", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_select_statement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql._typing.is_select_statement", "name": "is_select_statement", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.sql.base.Executable", "sqlalchemy.sql.selectable.ReturnsRows"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_select_statement", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_selectable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql._typing.is_selectable", "name": "is_selectable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_selectable", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.selectable.Selectable", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_sql_compiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql._typing.is_sql_compiler", "name": "is_sql_compiler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["c"], "arg_types": ["sqlalchemy.sql.compiler.Compiled"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_sql_compiler", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.compiler.SQLCompiler", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_subquery": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql._typing.is_subquery", "name": "is_subquery", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": ["sqlalchemy.sql.selectable.FromClause"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_subquery", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.selectable.Subquery", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql._typing.is_table", "name": "is_table", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": ["sqlalchemy.sql.selectable.FromClause"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_table", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.selectable.TableClause", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_table_value_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql._typing.is_table_value_type", "name": "is_table_value_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_table_value_type", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.sqltypes.TableValueType", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_text_clause": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql._typing.is_text_clause", "name": "is_text_clause", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["c"], "arg_types": ["sqlalchemy.sql.elements.ClauseElement"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_text_clause", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.elements.TextClause", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_tuple_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql._typing.is_tuple_type", "name": "is_tuple_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_tuple_type", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.sqltypes.TupleType", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "quoted_name": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.quoted_name", "kind": "Gdef"}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "datetime.time", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/sql/_typing.py"}