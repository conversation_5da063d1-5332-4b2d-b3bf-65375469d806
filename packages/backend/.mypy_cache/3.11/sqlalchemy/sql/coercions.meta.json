{"data_mtime": 1751259990, "dep_lines": [32, 33, 34, 35, 37, 42, 46, 47, 48, 49, 55, 525, 11, 32, 39, 40, 41, 9, 11, 12, 13, 14, 39, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 25, 25, 25, 25, 25, 20, 10, 20, 10, 10, 10, 5, 20, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.roles", "sqlalchemy.sql.visitors", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.util.typing", "sqlalchemy.sql.elements", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.dml", "sqlalchemy.sql.util", "collections.abc", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "__future__", "collections", "numbers", "re", "typing", "sqlalchemy", "builtins", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.operators", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types"], "hash": "9a54ab8e0c910aa271d74645ec17a0d4c8395e79", "id": "sqlalchemy.sql.coercions", "ignore_all": true, "interface_hash": "2f8eef50a089efb0f4d3268132c833f6978b1236", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py", "plugin_data": null, "size": 40664, "suppressed": [], "version_id": "1.13.0"}