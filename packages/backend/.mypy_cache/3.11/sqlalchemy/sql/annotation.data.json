{".class": "MypyFile", "_fullname": "sqlalchemy.sql.annotation", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.annotation.SupportsAnnotations"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.annotation.Annotated", "name": "Annotated", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.annotation.Annotated", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.annotation", "mro": ["sqlalchemy.sql.annotation.Annotated", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.annotation.Annotated.__element", "name": "__element", "type": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations"}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation.Annotated.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.annotation.Annotated", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of Annotated", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation.Annotated.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.annotation.Annotated"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of Annotated", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "element", "values"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation.Annotated.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "element", "values"], "arg_types": ["sqlalchemy.sql.annotation.Annotated", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.annotation._AnnotationDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Annotated", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["cls", "args"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "sqlalchemy.sql.annotation.Annotated.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["cls", "args"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of Annotated", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}]}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation.Annotated.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.annotation.Annotated"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of Annotated", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "sqlalchemy.sql.annotation.Annotated"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_annotate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "values"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation.Annotated._annotate", "name": "_annotate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "values"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.annotation._AnnotationDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_annotate of Annotated", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}]}}}, "_annotations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.annotation.Annotated._annotations", "name": "_annotations", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "_as_annotated_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "element", "values"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.sql.annotation.Annotated._as_annotated_instance", "name": "_as_annotated_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "element", "values"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.annotation.Annotated"}, "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.annotation._AnnotationDict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_as_annotated_instance of Annotated", "ret_type": "sqlalchemy.sql.annotation.Annotated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.annotation.Annotated._as_annotated_instance", "name": "_as_annotated_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "element", "values"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.annotation.Annotated"}, "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.annotation._AnnotationDict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_as_annotated_instance of Annotated", "ret_type": "sqlalchemy.sql.annotation.Annotated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation.Annotated._clone", "name": "_clone", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clone of Annotated", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}]}}}, "_deannotate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.sql.annotation.Annotated._deannotate", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.sql.annotation.Annotated._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.Annotated", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of Annotated", "ret_type": "sqlalchemy.sql.annotation.SupportsAnnotations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.annotation.Annotated._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of Annotated", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.annotation.Annotated._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of Annotated", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.annotation.Annotated._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.Annotated", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of Annotated", "ret_type": "sqlalchemy.sql.annotation.Annotated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.annotation.Annotated._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.Annotated", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of Annotated", "ret_type": "sqlalchemy.sql.annotation.Annotated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of Annotated", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.Annotated", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of Annotated", "ret_type": "sqlalchemy.sql.annotation.Annotated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.annotation.Annotated._hash", "name": "_hash", "type": "builtins.int"}}, "_is_column_operators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.annotation.Annotated._is_column_operators", "name": "_is_column_operators", "type": "builtins.bool"}}, "_with_annotations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "values"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation.Annotated._with_annotations", "name": "_with_annotations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "values"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.annotation._AnnotationDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_with_annotations of Annotated", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}]}}}, "entity_namespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.annotation.Annotated.entity_namespace", "name": "entity_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.annotation.Annotated"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_namespace of Annotated", "ret_type": "sqlalchemy.sql.base._EntityNamespace", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.annotation.Annotated.entity_namespace", "name": "entity_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.annotation.Annotated"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_namespace of Annotated", "ret_type": "sqlalchemy.sql.base._EntityNamespace", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.Annotated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.Annotated", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EMPTY_ANNOTATIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.annotation.EMPTY_ANNOTATIONS", "name": "EMPTY_ANNOTATIONS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "ExternallyTraversible": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors.ExternallyTraversible", "kind": "Gdef"}, "FrozenSet": {".class": "SymbolTableNode", "cross_ref": "typing.FrozenSet", "kind": "Gdef"}, "HasCacheKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.cache_key.HasCacheKey", "kind": "Gdef"}, "InternalTraversal": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors.InternalTraversal", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SupportsAnnotations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.visitors.ExternallyTraversible"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations", "name": "SupportsAnnotations", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.annotation", "mro": ["sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_annotate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "values"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations._annotate", "name": "_annotate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "values"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.annotation._AnnotationDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_annotate of SupportsAnnotations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}]}}}, "_annotations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations._annotations", "name": "_annotations", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "_annotations_cache_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations._annotations_cache_key", "name": "_annotations_cache_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.annotation.SupportsAnnotations"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_annotations_cache_key of SupportsAnnotations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations._annotations_cache_key", "name": "_annotations_cache_key", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_deannotate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations._deannotate", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.SupportsAnnotations", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsAnnotations", "ret_type": "sqlalchemy.sql.annotation.SupportsAnnotations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsAnnotations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsAnnotations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.SupportsAnnotations", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsAnnotations", "ret_type": "sqlalchemy.sql.annotation.SupportsAnnotations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.SupportsAnnotations", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsAnnotations", "ret_type": "sqlalchemy.sql.annotation.SupportsAnnotations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsAnnotations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.SupportsAnnotations", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsAnnotations", "ret_type": "sqlalchemy.sql.annotation.SupportsAnnotations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_gen_annotations_cache_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "anon_map"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations._gen_annotations_cache_key", "name": "_gen_annotations_cache_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "anon_map"], "arg_types": ["sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql._py_util.cache_anon_map"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_gen_annotations_cache_key of SupportsAnnotations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_immutable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations._is_immutable", "name": "_is_immutable", "type": "builtins.bool"}}, "proxy_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations.proxy_set", "name": "proxy_set", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.frozenset"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsCloneAnnotations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.annotation.SupportsWrappingAnnotations"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "name": "SupportsCloneAnnotations", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.annotation", "mro": ["sqlalchemy.sql.annotation.SupportsCloneAnnotations", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "_annotate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "values"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations._annotate", "name": "_annotate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "values"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.annotation._AnnotationDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_annotate of SupportsCloneAnnotations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}]}}}, "_clone_annotations_traverse_internals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations._clone_annotations_traverse_internals", "name": "_clone_annotations_traverse_internals", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.visitors._TraverseInternalsType"}}}, "_deannotate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations._deannotate", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.SupportsCloneAnnotations", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsCloneAnnotations", "ret_type": "sqlalchemy.sql.annotation.SupportsAnnotations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsCloneAnnotations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsCloneAnnotations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.SupportsCloneAnnotations", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsCloneAnnotations", "ret_type": "sqlalchemy.sql.annotation.SupportsAnnotations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.SupportsCloneAnnotations", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsCloneAnnotations", "ret_type": "sqlalchemy.sql.annotation.SupportsAnnotations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsCloneAnnotations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.SupportsCloneAnnotations", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsCloneAnnotations", "ret_type": "sqlalchemy.sql.annotation.SupportsAnnotations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_with_annotations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "values"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations._with_annotations", "name": "_with_annotations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "values"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.annotation._AnnotationDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_with_annotations of SupportsCloneAnnotations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsCloneAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsWrappingAnnotations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.annotation.SupportsAnnotations"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "name": "SupportsWrappingAnnotations", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.annotation", "mro": ["sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_annotate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "values"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations._annotate", "name": "_annotate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "values"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.annotation._AnnotationDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_annotate of SupportsWrappingAnnotations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}]}}}, "_constructor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations._constructor", "name": "_constructor", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_deannotate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations._deannotate", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.SupportsWrappingAnnotations", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsWrappingAnnotations", "ret_type": "sqlalchemy.sql.annotation.SupportsAnnotations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsWrappingAnnotations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsWrappingAnnotations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.SupportsWrappingAnnotations", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsWrappingAnnotations", "ret_type": "sqlalchemy.sql.annotation.SupportsAnnotations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations._deannotate", "name": "_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.SupportsWrappingAnnotations", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsWrappingAnnotations", "ret_type": "sqlalchemy.sql.annotation.SupportsAnnotations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsWrappingAnnotations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "values", "clone"], "arg_types": ["sqlalchemy.sql.annotation.SupportsWrappingAnnotations", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deannotate of SupportsWrappingAnnotations", "ret_type": "sqlalchemy.sql.annotation.SupportsAnnotations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_with_annotations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "values"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations._with_annotations", "name": "_with_annotations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "values"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.annotation._AnnotationDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_with_annotations of SupportsWrappingAnnotations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}]}}}, "entity_namespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.entity_namespace", "name": "entity_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.annotation.SupportsWrappingAnnotations"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_namespace of SupportsWrappingAnnotations", "ret_type": "sqlalchemy.sql.base._EntityNamespace", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.entity_namespace", "name": "entity_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.annotation.SupportsWrappingAnnotations"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_namespace of SupportsWrappingAnnotations", "ret_type": "sqlalchemy.sql.base._EntityNamespace", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "_AnnotationDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql.annotation._AnnotationDict", "line": 48, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "_EntityNamespace": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._EntityNamespace", "kind": "Gdef"}, "_SA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "name": "_SA", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}}, "_TraverseInternalsType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors._TraverseInternalsType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.annotation.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.annotation.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.annotation.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.annotation.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.annotation.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.annotation.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_deep_annotate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["element", "annotations", "exclude", "detect_subquery_cols", "ind_cols_on_fromclause", "annotate_callable"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation._deep_annotate", "name": "_deep_annotate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["element", "annotations", "exclude", "detect_subquery_cols", "ind_cols_on_fromclause", "annotate_callable"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._deep_annotate", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.annotation._AnnotationDict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.sql.annotation.SupportsAnnotations"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.annotation.SupportsAnnotations", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.annotation._AnnotationDict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.annotation.SupportsAnnotations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deep_annotate", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._deep_annotate", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._deep_annotate", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}]}}}, "_deep_deannotate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.sql.annotation._deep_deannotate", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["element", "values"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.sql.annotation._deep_deannotate", "name": "_deep_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["element", "values"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._deep_deannotate", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deep_deannotate", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._deep_deannotate", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._deep_deannotate", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["element", "values"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.annotation._deep_deannotate", "name": "_deep_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["element", "values"], "arg_types": [{".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deep_deannotate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.annotation._deep_deannotate", "name": "_deep_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["element", "values"], "arg_types": [{".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deep_deannotate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["element", "values"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.annotation._deep_deannotate", "name": "_deep_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["element", "values"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._deep_deannotate#1", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deep_deannotate", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._deep_deannotate#1", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._deep_deannotate#1", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.annotation._deep_deannotate", "name": "_deep_deannotate", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["element", "values"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._deep_deannotate#1", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deep_deannotate", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._deep_deannotate#1", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._deep_deannotate#1", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["element", "values"], "arg_types": [{".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deep_deannotate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["element", "values"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._deep_deannotate#1", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deep_deannotate", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._deep_deannotate#1", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._deep_deannotate#1", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}]}]}}}, "_new_annotation_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "base_cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation._new_annotation_type", "name": "_new_annotation_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "base_cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations"}, {".class": "TypeType", "item": "sqlalchemy.sql.annotation.Annotated"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_new_annotation_type", "ret_type": {".class": "TypeType", "item": "sqlalchemy.sql.annotation.Annotated"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_annotations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["target_hierarchy", "base_cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation._prepare_annotations", "name": "_prepare_annotations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["target_hierarchy", "base_cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations"}, {".class": "TypeType", "item": "sqlalchemy.sql.annotation.Annotated"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_annotations", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_safe_annotate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["to_annotate", "annotations"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation._safe_annotate", "name": "_safe_annotate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["to_annotate", "annotations"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._safe_annotate", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.annotation._AnnotationDict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_safe_annotate", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._safe_annotate", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._safe_annotate", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}]}}}, "_shallow_annotate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["element", "annotations"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.annotation._shallow_annotate", "name": "_shallow_annotate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["element", "annotations"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._shallow_annotate", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.annotation._AnnotationDict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_shallow_annotate", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._shallow_annotate", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "sqlalchemy.sql.annotation._shallow_annotate", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}]}}}, "annotated_classes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.annotation.annotated_classes", "name": "annotated_classes", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "sqlalchemy.sql.annotation.SupportsWrappingAnnotations"}, {".class": "TypeType", "item": "sqlalchemy.sql.annotation.Annotated"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "anon_map": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._py_util.cache_anon_map", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "operators": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/sql/annotation.py"}