{"data_mtime": 1751259990, "dep_lines": [39, 40, 41, 46, 47, 50, 37, 38, 13, 15, 16, 17, 18, 19, 37, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 25, 25, 25, 10, 10, 5, 5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util.langhelpers", "sqlalchemy.util._has_cy", "sqlalchemy.util.typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.elements", "sqlalchemy.sql._py_util", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "enum", "itertools", "operator", "typing", "sqlalchemy", "builtins", "_operator", "abc", "importlib", "importlib.machinery", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.util._py_collections", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "a8d6248a5ee5170654868a21db8d60d380d46c16", "id": "sqlalchemy.sql.visitors", "ignore_all": true, "interface_hash": "370e06083071dbed9a2a088597293c4e5550b917", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/sql/visitors.py", "plugin_data": null, "size": 36317, "suppressed": [], "version_id": "1.13.0"}