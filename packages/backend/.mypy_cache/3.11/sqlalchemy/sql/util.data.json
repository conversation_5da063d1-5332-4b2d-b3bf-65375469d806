{".class": "MypyFile", "_fullname": "sqlalchemy.sql.util", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractSet": {".class": "SymbolTableNode", "cross_ref": "typing.AbstractSet", "kind": "Gdef"}, "Alias": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.<PERSON><PERSON>", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BinaryExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BinaryExpression", "kind": "Gdef"}, "BindParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BindParameter", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClauseAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.visitors.ReplacingExternalTraversal"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.util.ClauseAdapter", "name": "ClauseAdapter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.util.ClauseAdapter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.util", "mro": ["sqlalchemy.sql.util.ClauseAdapter", "sqlalchemy.sql.visitors.ReplacingExternalTraversal", "sqlalchemy.sql.visitors.CloningExternalTraversal", "sqlalchemy.sql.visitors.ExternalTraversal", "sqlalchemy.util.langhelpers.MemoizedSlots", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "selectable", "equivalents", "include_fn", "exclude_fn", "adapt_on_names", "anonymize_labels", "adapt_from_selectables"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.ClauseAdapter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "selectable", "equivalents", "include_fn", "exclude_fn", "adapt_on_names", "anonymize_labels", "adapt_from_selectables"], "arg_types": ["sqlalchemy.sql.util.ClauseAdapter", "sqlalchemy.sql.selectable.Selectable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._EquivalentColumnMap"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.elements.ClauseElement"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.elements.ClauseElement"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.sql.selectable.FromClause"], "extra_attrs": null, "type_ref": "typing.AbstractSet"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClauseAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.util.ClauseAdapter.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_corresponding_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "col", "require_embedded", "_seen"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.ClauseAdapter._corresponding_column", "name": "_corresponding_column", "type": null}}, "adapt_from_selectables": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util.ClauseAdapter.adapt_from_selectables", "name": "adapt_from_selectables", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.sql.selectable.FromClause"], "extra_attrs": null, "type_ref": "typing.AbstractSet"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "adapt_on_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util.ClauseAdapter.adapt_on_names", "name": "adapt_on_names", "type": "builtins.bool"}}, "equivalents": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util.ClauseAdapter.equivalents", "name": "equivalents", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "exclude_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util.ClauseAdapter.exclude_fn", "name": "exclude_fn", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.elements.ClauseElement"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "include_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util.ClauseAdapter.include_fn", "name": "include_fn", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.elements.ClauseElement"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "col", "_include_singleton_constants"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.util.ClauseAdapter.replace", "name": "replace", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "col", "_include_singleton_constants"], "arg_types": ["sqlalchemy.sql.util.ClauseAdapter", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ClauseAdapter.replace", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace of <PERSON>eAdapt<PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ClauseAdapter.replace", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ClauseAdapter.replace", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.util.ClauseAdapter.replace", "name": "replace", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "col", "_include_singleton_constants"], "arg_types": ["sqlalchemy.sql.util.ClauseAdapter", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ClauseAdapter.replace", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace of <PERSON>eAdapt<PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ClauseAdapter.replace", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ClauseAdapter.replace", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}]}}}}, "selectable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util.ClauseAdapter.selectable", "name": "selectable", "type": "sqlalchemy.sql.selectable.Selectable"}}, "traverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.sql.util.ClauseAdapter.traverse", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_mypy_only"], "fullname": "sqlalchemy.sql.util.ClauseAdapter.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ClauseAdapter", {".class": "UnionType", "items": ["sqlalchemy.sql.visitors.ExternallyTraversible", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of ClauseAdapter", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.sql.visitors.ExternallyTraversible", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.sql.util.ClauseAdapter.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ClauseAdapter", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of ClauseAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.util.ClauseAdapter.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ClauseAdapter", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of ClauseAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.sql.util.ClauseAdapter.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ClauseAdapter", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ClauseAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of ClauseAdapter", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ClauseAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ClauseAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.util.ClauseAdapter.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ClauseAdapter", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ClauseAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of ClauseAdapter", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ClauseAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ClauseAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ClauseAdapter", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of ClauseAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ClauseAdapter", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ClauseAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of ClauseAdapter", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ClauseAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ClauseAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.util.ClauseAdapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.util.ClauseAdapter", "values": [], "variance": 0}, "slots": ["__traverse_options__", "_next", "_visitor_dict", "adapt_from_selectables", "adapt_on_names", "equivalents", "exclude_fn", "include_fn", "selectable"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef"}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "ColumnAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.util.ClauseAdapter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.util.ColumnAdapter", "name": "ColumnAdapter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.util.ColumnAdapter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.util", "mro": ["sqlalchemy.sql.util.ColumnAdapter", "sqlalchemy.sql.util.ClauseAdapter", "sqlalchemy.sql.visitors.ReplacingExternalTraversal", "sqlalchemy.sql.visitors.CloningExternalTraversal", "sqlalchemy.sql.visitors.ExternalTraversal", "sqlalchemy.util.langhelpers.MemoizedSlots", "builtins.object"], "names": {".class": "SymbolTable", "_IncludeExcludeMapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.util.ColumnAdapter._IncludeExcludeMapping", "name": "_IncludeExcludeMapping", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.util.ColumnAdapter._IncludeExcludeMapping", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.util", "mro": ["sqlalchemy.sql.util.ColumnAdapter._IncludeExcludeMapping", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.ColumnAdapter._IncludeExcludeMapping.__getitem__", "name": "__getitem__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "columns"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.ColumnAdapter._IncludeExcludeMapping.__init__", "name": "__init__", "type": null}}, "columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util.ColumnAdapter._IncludeExcludeMapping.columns", "name": "columns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "parent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util.ColumnAdapter._IncludeExcludeMapping.parent", "name": "parent", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.util.ColumnAdapter._IncludeExcludeMapping.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.util.ColumnAdapter._IncludeExcludeMapping", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "selectable", "equivalents", "adapt_required", "include_fn", "exclude_fn", "adapt_on_names", "allow_label_resolve", "anonymize_labels", "adapt_from_selectables"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.ColumnAdapter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "selectable", "equivalents", "adapt_required", "include_fn", "exclude_fn", "adapt_on_names", "allow_label_resolve", "anonymize_labels", "adapt_from_selectables"], "arg_types": ["sqlalchemy.sql.util.ColumnAdapter", "sqlalchemy.sql.selectable.Selectable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._EquivalentColumnMap"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.elements.ClauseElement"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.elements.ClauseElement"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.sql.selectable.FromClause"], "extra_attrs": null, "type_ref": "typing.AbstractSet"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ColumnAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.util.ColumnAdapter.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_locate_col": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "col"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.ColumnAdapter._locate_col", "name": "_locate_col", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "col"], "arg_types": ["sqlalchemy.sql.util.ColumnAdapter", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_locate_col of ColumnAdapter", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_wrap": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util.ColumnAdapter._wrap", "name": "_wrap", "type": {".class": "NoneType"}}}, "adapt_check_present": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "col"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.ColumnAdapter.adapt_check_present", "name": "adapt_check_present", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "col"], "arg_types": ["sqlalchemy.sql.util.ColumnAdapter", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adapt_check_present of ColumnAdapter", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "adapt_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.util.ColumnAdapter.adapt_clause", "name": "adapt_clause", "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ColumnAdapter", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ColumnAdapter", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ColumnAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ColumnAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ColumnAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}]}]}}}, "adapt_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.util.ColumnAdapter.adapt_list", "name": "adapt_list", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "list_"], "arg_types": ["sqlalchemy.sql.visitors.CloningExternalTraversal", {".class": "Instance", "args": ["sqlalchemy.sql.visitors.ExternallyTraversible"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.visitors.ExternallyTraversible"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "adapt_required": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util.ColumnAdapter.adapt_required", "name": "adapt_required", "type": "builtins.bool"}}, "allow_label_resolve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util.ColumnAdapter.allow_label_resolve", "name": "allow_label_resolve", "type": "builtins.bool"}}, "chain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "visitor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.ColumnAdapter.chain", "name": "chain", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "visitor"], "arg_types": ["sqlalchemy.sql.util.ColumnAdapter", "sqlalchemy.sql.visitors.ExternalTraversal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chain of ColumnAdapter", "ret_type": "sqlalchemy.sql.util.ColumnAdapter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.util.ColumnAdapter.columns", "name": "columns", "type": "sqlalchemy.sql.util._ColumnLookup"}}, "traverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.sql.util.ColumnAdapter.traverse", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.sql.util.ColumnAdapter.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ColumnAdapter", {".class": "UnionType", "items": ["sqlalchemy.sql.visitors.ExternallyTraversible", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of ColumnAdapter", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.sql.visitors.ExternallyTraversible", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.util.ColumnAdapter.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ColumnAdapter", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of ColumnAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.util.ColumnAdapter.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ColumnAdapter", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of ColumnAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.util.ColumnAdapter.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ColumnAdapter", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ColumnAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of ColumnAdapter", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ColumnAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ColumnAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.util.ColumnAdapter.traverse", "name": "traverse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ColumnAdapter", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ColumnAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of ColumnAdapter", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ColumnAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ColumnAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ColumnAdapter", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of ColumnAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.util.ColumnAdapter", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ColumnAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "traverse of ColumnAdapter", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ColumnAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util.ColumnAdapter.traverse#1", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}]}]}}}, "visitor_iterator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.sql.util.ColumnAdapter.visitor_iterator", "name": "visitor_iterator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.util.ColumnAdapter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visitor_iterator of ColumnAdapter", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.util.ColumnAdapter"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.util.ColumnAdapter.visitor_iterator", "name": "visitor_iterator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.util.ColumnAdapter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visitor_iterator of ColumnAdapter", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.util.ColumnAdapter"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "wrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "adapter"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.ColumnAdapter.wrap", "name": "wrap", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.util.ColumnAdapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.util.ColumnAdapter", "values": [], "variance": 0}, "slots": ["__traverse_options__", "__weakref__", "_next", "_visitor_dict", "_wrap", "adapt_from_selectables", "adapt_on_names", "adapt_required", "allow_label_resolve", "columns", "equivalents", "exclude_fn", "include_fn", "selectable"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColumnClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnClause", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ExternalTraversal": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors.ExternalTraversal", "kind": "Gdef"}, "ExternallyTraversible": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors.ExternallyTraversible", "kind": "Gdef"}, "FromClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.FromClause", "kind": "Gdef"}, "FromGrouping": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.FromGrouping", "kind": "Gdef"}, "Grouping": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Grouping", "kind": "Gdef"}, "HasCacheKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.cache_key.HasCacheKey", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Join": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Join", "kind": "Gdef"}, "KeyedColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.KeyedColumnElement", "kind": "Gdef"}, "Label": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Label", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "NamedColumn": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.NamedColumn", "kind": "Gdef"}, "Null": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Null", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "Row": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.row.Row", "kind": "Gdef"}, "ScalarSelect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.ScalarSelect", "kind": "Gdef"}, "SelectBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.SelectBase", "kind": "Gdef"}, "Selectable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Selectable", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TableClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TableClause", "kind": "Gdef"}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "UnaryExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.UnaryExpression", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_AnyExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams", "kind": "Gdef"}, "_AnyMultiExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._AnyMultiExecuteParams", "kind": "Gdef"}, "_AnySingleExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._AnySingleExecuteParams", "kind": "Gdef"}, "_CE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.util._CE", "name": "_CE", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}}, "_ColumnLookup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.util._ColumnLookup", "name": "_ColumnLookup", "type_vars": []}, "deletable_attributes": [], "flags": ["is_protocol"], "fullname": "sqlalchemy.sql.util._ColumnLookup", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.sql.util", "mro": ["sqlalchemy.sql.util._ColumnLookup", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.sql.util._ColumnLookup.__getitem__", "impl": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_trivial_body"], "fullname": "sqlalchemy.sql.util._ColumnLookup.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.util._ColumnLookup", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _ColumnLookup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.util._ColumnLookup.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.util._ColumnLookup", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _ColumnLookup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.util._ColumnLookup.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.util._ColumnLookup", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _ColumnLookup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.util._ColumnLookup.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.util._ColumnLookup", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnClause"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _ColumnLookup", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnClause"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.util._ColumnLookup.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.util._ColumnLookup", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnClause"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _ColumnLookup", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnClause"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.util._ColumnLookup.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.util._ColumnLookup", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _ColumnLookup", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.util._ColumnLookup.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.util._ColumnLookup", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _ColumnLookup", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.util._ColumnLookup.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.util._ColumnLookup", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util._ColumnLookup.__getitem__#3", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _ColumnLookup", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util._ColumnLookup.__getitem__#3", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util._ColumnLookup.__getitem__#3", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.util._ColumnLookup.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.util._ColumnLookup", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util._ColumnLookup.__getitem__#3", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _ColumnLookup", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util._ColumnLookup.__getitem__#3", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util._ColumnLookup.__getitem__#3", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.util._ColumnLookup", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _ColumnLookup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.util._ColumnLookup", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnClause"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _ColumnLookup", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnClause"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.util._ColumnLookup", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _ColumnLookup", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.util._ColumnLookup", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util._ColumnLookup.__getitem__#3", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _ColumnLookup", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util._ColumnLookup.__getitem__#3", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.visitors._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.sql.util._ColumnLookup.__getitem__#3", "upper_bound": "sqlalchemy.sql.visitors.ExternallyTraversible", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.util._ColumnLookup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.util._ColumnLookup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CoreSingleExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams", "kind": "Gdef"}, "_ET": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors._ET", "kind": "Gdef"}, "_EquivalentColumnMap": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._EquivalentColumnMap", "kind": "Gdef"}, "_JoinTargetElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable._JoinTargetElement", "kind": "Gdef"}, "_LimitOffsetType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._LimitOffsetType", "kind": "Gdef"}, "_SelectIterable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable._SelectIterable", "kind": "Gdef"}, "_TraverseCallableType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors._TraverseCallableType", "kind": "Gdef"}, "_TypeEngineArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._TypeEngineArgument", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.util.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.util.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.util.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.util.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.util.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.util.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_deep_annotate": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.annotation._deep_annotate", "kind": "Gdef"}, "_deep_deannotate": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.annotation._deep_deannotate", "kind": "Gdef"}, "_expand_cloned": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._expand_cloned", "kind": "Gdef"}, "_find_columns": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements._find_columns", "kind": "Gdef"}, "_from_objects": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._from_objects", "kind": "Gdef"}, "_label_reference": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements._label_reference", "kind": "Gdef"}, "_long_statement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.util._long_statement", "name": "_long_statement", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.util._long_statement", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.sql.util", "mro": ["sqlalchemy.sql.util._long_statement", "builtins.str", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._long_statement.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.util._long_statement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of _long_statement", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.util._long_statement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.util._long_statement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_make_slice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["limit_clause", "offset_clause", "start", "stop"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._make_slice", "name": "_make_slice", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["limit_clause", "offset_clause", "start", "stop"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._LimitOffsetType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._LimitOffsetType"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_slice", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_offset_or_limit_clause": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["element", "name", "type_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._offset_or_limit_clause", "name": "_offset_or_limit_clause", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["element", "name", "type_"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._LimitOffsetType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.int"], "type_ref": "sqlalchemy.sql._typing._TypeEngineArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_offset_or_limit_clause", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_offset_or_limit_clause_asint_if_possible": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["clause"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._offset_or_limit_clause_asint_if_possible", "name": "_offset_or_limit_clause_asint_if_possible", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["clause"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._LimitOffsetType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_offset_or_limit_clause_asint_if_possible", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._LimitOffsetType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_quote_ddl_expr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["element"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._quote_ddl_expr", "name": "_quote_ddl_expr", "type": null}}, "_repr_base": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.util._repr_base", "name": "_repr_base", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.util._repr_base", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.util", "mro": ["sqlalchemy.sql.util._repr_base", "builtins.object"], "names": {".class": "SymbolTable", "_DICT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.util._repr_base._DICT", "name": "_DICT", "type": "builtins.int"}}, "_LIST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.util._repr_base._LIST", "name": "_LIST", "type": "builtins.int"}}, "_TUPLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.util._repr_base._TUPLE", "name": "_TUPLE", "type": "builtins.int"}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.util._repr_base.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "max_chars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.util._repr_base.max_chars", "name": "max_chars", "type": "builtins.int"}}, "trunc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._repr_base.trunc", "name": "trunc", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["sqlalchemy.sql.util._repr_base", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trunc of _repr_base", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.util._repr_base.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.util._repr_base", "values": [], "variance": 0}, "slots": ["max_chars"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_repr_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.util._repr_base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.util._repr_params", "name": "_repr_params", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.util._repr_params", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.util", "mro": ["sqlalchemy.sql.util._repr_params", "sqlalchemy.sql.util._repr_base", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "params", "batches", "max_params", "max_chars", "<PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._repr_params.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "params", "batches", "max_params", "max_chars", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": ["sqlalchemy.sql.util._repr_params", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _repr_params", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._repr_params.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.util._repr_params"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of _repr_params", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.util._repr_params.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_get_batches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._repr_params._get_batches", "name": "_get_batches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "params"], "arg_types": ["sqlalchemy.sql.util._repr_params", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_batches of _repr_params", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_repr_multi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "multi_params", "typ"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._repr_params._repr_multi", "name": "_repr_multi", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "multi_params", "typ"], "arg_types": ["sqlalchemy.sql.util._repr_params", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyMultiExecuteParams"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_repr_multi of _repr_params", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_repr_param_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._repr_params._repr_param_dict", "name": "_repr_param_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "params"], "arg_types": ["sqlalchemy.sql.util._repr_params", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_repr_param_dict of _repr_params", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_repr_param_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._repr_params._repr_param_list", "name": "_repr_param_list", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "params"], "arg_types": ["sqlalchemy.sql.util._repr_params", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnySingleExecuteParams"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_repr_param_list of _repr_params", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_repr_param_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._repr_params._repr_param_tuple", "name": "_repr_param_tuple", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "params"], "arg_types": ["sqlalchemy.sql.util._repr_params", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_repr_param_tuple of _repr_params", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_repr_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "params", "typ"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._repr_params._repr_params", "name": "_repr_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "params", "typ"], "arg_types": ["sqlalchemy.sql.util._repr_params", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnySingleExecuteParams"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_repr_params of _repr_params", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batches": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util._repr_params.batches", "name": "batches", "type": "builtins.int"}}, "ismulti": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util._repr_params.ismulti", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util._repr_params.max_params", "name": "max_params", "type": "builtins.int"}}, "params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util._repr_params.params", "name": "params", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.util._repr_params.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.util._repr_params", "values": [], "variance": 0}, "slots": ["batches", "<PERSON><PERSON><PERSON><PERSON>", "max_chars", "max_params", "params"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_repr_row": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.util._repr_base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.util._repr_row", "name": "_repr_row", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.util._repr_row", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.util", "mro": ["sqlalchemy.sql.util._repr_row", "sqlalchemy.sql.util._repr_base", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "row", "max_chars"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._repr_row.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "row", "max_chars"], "arg_types": ["sqlalchemy.sql.util._repr_row", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.row.Row"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _repr_row", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._repr_row.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.util._repr_row"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of _repr_row", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.util._repr_row.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "row": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.util._repr_row.row", "name": "row", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.row.Row"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.util._repr_row.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.util._repr_row", "values": [], "variance": 0}, "slots": ["max_chars", "row"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_repr_single_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util._repr_single_value", "name": "_repr_single_value", "type": null}}, "_shallow_annotate": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.annotation._shallow_annotate", "kind": "Gdef"}, "_textual_label_reference": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements._textual_label_reference", "kind": "Gdef"}, "adapt_criterion_to_null": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["crit", "nulls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.adapt_criterion_to_null", "name": "adapt_criterion_to_null", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["crit", "nulls"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.util._CE", "id": -1, "name": "_CE", "namespace": "sqlalchemy.sql.util.adapt_criterion_to_null", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Collection"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adapt_criterion_to_null", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.util._CE", "id": -1, "name": "_CE", "namespace": "sqlalchemy.sql.util.adapt_criterion_to_null", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.util._CE", "id": -1, "name": "_CE", "namespace": "sqlalchemy.sql.util.adapt_criterion_to_null", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}]}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "bind_values": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["clause"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.bind_values", "name": "bind_values", "type": null}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "itertools.chain", "kind": "Gdef"}, "clause_is_present": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["clause", "search"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.clause_is_present", "name": "clause_is_present", "type": null}}, "coercions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.coercions", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "criterion_as_pairs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["expression", "consider_as_foreign_keys", "consider_as_referenced_keys", "any_operator"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.criterion_as_pairs", "name": "criterion_as_pairs", "type": null}}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "expand_column_list_from_order_by": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["collist", "order_by"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.expand_column_list_from_order_by", "name": "expand_column_list_from_order_by", "type": null}}, "extract_first_column_annotation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["column", "annotation_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.extract_first_column_annotation", "name": "extract_first_column_annotation", "type": null}}, "find_join_source": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["clauses", "join_to"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.find_join_source", "name": "find_join_source", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["clauses", "join_to"], "arg_types": [{".class": "Instance", "args": ["sqlalchemy.sql.selectable.FromClause"], "extra_attrs": null, "type_ref": "builtins.list"}, "sqlalchemy.sql.selectable.FromClause"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_join_source", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_left_clause_that_matches_given": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["clauses", "join_from"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.find_left_clause_that_matches_given", "name": "find_left_clause_that_matches_given", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["clauses", "join_from"], "arg_types": [{".class": "Instance", "args": ["sqlalchemy.sql.selectable.FromClause"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "sqlalchemy.sql.selectable.FromClause"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_left_clause_that_matches_given", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_left_clause_to_join_from": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["clauses", "join_to", "onclause"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.find_left_clause_to_join_from", "name": "find_left_clause_to_join_from", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["clauses", "join_to", "onclause"], "arg_types": [{".class": "Instance", "args": ["sqlalchemy.sql.selectable.FromClause"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable._JoinTargetElement"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_left_clause_to_join_from", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_tables": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["clause", "check_columns", "include_aliases", "include_joins", "include_selects", "include_crud"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.find_tables", "name": "find_tables", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["clause", "check_columns", "include_aliases", "include_joins", "include_selects", "include_crud"], "arg_types": ["sqlalchemy.sql.elements.ClauseElement", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_tables", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.selectable.TableClause"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_text_clause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing.is_text_clause", "kind": "Gdef"}, "join_condition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "b", "a_subset", "consider_as_foreign_keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.join_condition", "name": "join_condition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "b", "a_subset", "consider_as_foreign_keys"], "arg_types": ["sqlalchemy.sql.selectable.FromClause", "sqlalchemy.sql.selectable.FromClause", {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnClause"}], "extra_attrs": null, "type_ref": "typing.AbstractSet"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join_condition", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "operators": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "reduce_columns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.sql.util.reduce_columns", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["columns", "clauses", "kw"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.sql.util.reduce_columns", "name": "reduce_columns", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["columns", "clauses", "kw"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable._SelectIterable"}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce_columns", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.elements.TextClause"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Collection"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["columns", "clauses", "kw"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.util.reduce_columns", "name": "reduce_columns", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["columns", "clauses", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce_columns", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.util.reduce_columns", "name": "reduce_columns", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["columns", "clauses", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce_columns", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["columns", "clauses", "kw"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.util.reduce_columns", "name": "reduce_columns", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["columns", "clauses", "kw"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable._SelectIterable"}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce_columns", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.elements.TextClause"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.util.reduce_columns", "name": "reduce_columns", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["columns", "clauses", "kw"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable._SelectIterable"}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce_columns", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.elements.TextClause"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["columns", "clauses", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce_columns", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["columns", "clauses", "kw"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable._SelectIterable"}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce_columns", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.elements.TextClause"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef"}, "selectables_overlap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["left", "right"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.selectables_overlap", "name": "selectables_overlap", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["left", "right"], "arg_types": ["sqlalchemy.sql.selectable.FromClause", "sqlalchemy.sql.selectable.FromClause"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selectables_overlap", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sort_tables": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.sort_tables", "kind": "Gdef"}, "splice_joins": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["left", "right", "stop_on"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.splice_joins", "name": "splice_joins", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["left", "right", "stop_on"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "splice_joins", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "surface_selectables": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["clause"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.surface_selectables", "name": "surface_selectables", "type": null}}, "surface_selectables_only": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["clause"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.surface_selectables_only", "name": "surface_selectables_only", "type": null}}, "tables_from_leftmost": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["clause"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.tables_from_leftmost", "name": "tables_from_leftmost", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["clause"], "arg_types": ["sqlalchemy.sql.selectable.FromClause"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tables_from_leftmost", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.selectable.FromClause"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "unwrap_label_reference": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["element"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.unwrap_label_reference", "name": "unwrap_label_reference", "type": null}}, "unwrap_order_by": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["clause"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.unwrap_order_by", "name": "unwrap_order_by", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["clause"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unwrap_order_by", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "visit_binary_product": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fn", "expr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.util.visit_binary_product", "name": "visit_binary_product", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fn", "expr"], "arg_types": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BinaryExpression"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_binary_product", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visitors": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/sql/util.py"}