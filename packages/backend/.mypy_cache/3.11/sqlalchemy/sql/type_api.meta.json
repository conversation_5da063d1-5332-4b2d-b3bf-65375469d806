{"data_mtime": 1751259990, "dep_lines": [33, 34, 36, 37, 40, 48, 49, 52, 61, 38, 39, 12, 14, 15, 16, 38, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 25, 25, 25, 10, 10, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.sql._typing", "sqlalchemy.sql.elements", "sqlalchemy.sql.sqltypes", "sqlalchemy.engine.interfaces", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "enum", "types", "typing", "sqlalchemy", "builtins", "abc", "importlib", "importlib.machinery", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "typing_extensions"], "hash": "3949a80f996aa2e2db273157df1a957e263c91ca", "id": "sqlalchemy.sql.type_api", "ignore_all": true, "interface_hash": "a3d1a7a7004162a86d57ac62c5e77b01efe6aade", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/sql/type_api.py", "plugin_data": null, "size": 84464, "suppressed": [], "version_id": "1.13.0"}