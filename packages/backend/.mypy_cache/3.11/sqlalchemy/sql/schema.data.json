{".class": "MypyFile", "_fullname": "sqlalchemy.sql.schema", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "sqlalchemy.sql.base.SchemaEventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "name": "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "sqlalchemy.sql.base.SchemaEventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">1", "name": "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">1", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">1", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "sqlalchemy.sql.base.SchemaEventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">2", "name": "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">2", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">2", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "sqlalchemy.sql.base.SchemaEventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">3", "name": "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">3", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">3", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "sqlalchemy.sql.base.SchemaEventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">4", "name": "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">4", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">4", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "sqlalchemy.sql.base.SchemaEventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">5", "name": "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">5", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">5", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "sqlalchemy.sql.base.SchemaEventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">6", "name": "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">6", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">6", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">7": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "sqlalchemy.sql.base.SchemaEventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">7", "name": "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">7", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">7", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "sqlalchemy.sql.base.SchemaEventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">8", "name": "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">8", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">8", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BLANK_SCHEMA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.BLANK_SCHEMA", "name": "BLANK_SCHEMA", "type": {".class": "LiteralType", "fallback": "sqlalchemy.sql.schema.SchemaConst", "value": "BLANK_SCHEMA"}}}, "BindParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BindParameter", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CallableColumnDefault": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.ColumnDefault"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.CallableColumnDefault", "name": "CallableColumnDefault", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.CallableColumnDefault", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.CallableColumnDefault", "sqlalchemy.sql.schema.ColumnDefault", "sqlalchemy.sql.schema.DefaultGenerator", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "arg", "for_update"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.CallableColumnDefault.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "arg", "for_update"], "arg_types": ["sqlalchemy.sql.schema.CallableColumnDefault", {".class": "UnionType", "items": ["sqlalchemy.sql.schema._CallableColumnDefaultProtocol", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CallableColumnDefault", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.CallableColumnDefault._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.CallableColumnDefault"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of CallableColumnDefault", "ret_type": "sqlalchemy.sql.schema.CallableColumnDefault", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_maybe_wrap_callable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.CallableColumnDefault._maybe_wrap_callable", "name": "_maybe_wrap_callable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "arg_types": ["sqlalchemy.sql.schema.CallableColumnDefault", {".class": "UnionType", "items": ["sqlalchemy.sql.schema._CallableColumnDefaultProtocol", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_maybe_wrap_callable of CallableColumnDefault", "ret_type": "sqlalchemy.sql.schema._CallableColumnDefaultProtocol", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.CallableColumnDefault.arg", "name": "arg", "type": "sqlalchemy.sql.schema._CallableColumnDefaultProtocol"}}, "has_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.CallableColumnDefault.has_arg", "name": "has_arg", "type": "builtins.bool"}}, "is_callable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.CallableColumnDefault.is_callable", "name": "is_callable", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.CallableColumnDefault.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.CallableColumnDefault", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CheckConstraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.ColumnCollectionConstraint"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.CheckConstraint", "name": "CheckConstraint", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.CheckConstraint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.CheckConstraint", "sqlalchemy.sql.schema.ColumnCollectionConstraint", "sqlalchemy.sql.schema.ColumnCollectionMixin", "sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.schema.HasConditionalDDL", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "sqltext", "name", "deferrable", "initially", "table", "info", "_create_rule", "_autoattach", "_type_bound", "dialect_kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.CheckConstraint.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "sqltext", "name", "deferrable", "initially", "table", "info", "_create_rule", "_autoattach", "_type_bound", "dialect_kw"], "arg_types": ["sqlalchemy.sql.schema.CheckConstraint", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._TextCoercedExpressionArgument"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._ConstraintNameArgument"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CheckConstraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.CheckConstraint.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "sqltext", "name", "deferrable", "initially", "table", "info", "_create_rule", "_autoattach", "_type_bound", "dialect_kw"], "arg_types": ["sqlalchemy.sql.schema.CheckConstraint", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._TextCoercedExpressionArgument"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._ConstraintNameArgument"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CheckConstraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.CheckConstraint.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_allow_multiple_tables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.CheckConstraint._allow_multiple_tables", "name": "_allow_multiple_tables", "type": "builtins.bool"}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "target_table", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.CheckConstraint._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "target_table", "kw"], "arg_types": ["sqlalchemy.sql.schema.CheckConstraint", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of CheckConstraint", "ret_type": "sqlalchemy.sql.schema.CheckConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "target_table", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.CheckConstraint.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "target_table", "kw"], "arg_types": ["sqlalchemy.sql.schema.CheckConstraint", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of CheckConstraint", "ret_type": "sqlalchemy.sql.schema.CheckConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.CheckConstraint.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "target_table", "kw"], "arg_types": ["sqlalchemy.sql.schema.CheckConstraint", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of CheckConstraint", "ret_type": "sqlalchemy.sql.schema.CheckConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_column_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.CheckConstraint.is_column_level", "name": "is_column_level", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.CheckConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_column_level of CheckConstraint", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.CheckConstraint.is_column_level", "name": "is_column_level", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.CheckConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_column_level of CheckConstraint", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sqltext": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.CheckConstraint.sqltext", "name": "sqltext", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.CheckConstraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.CheckConstraint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef"}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.schema.SchemaItem", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnClause"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.Column", "name": "Column", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.Column", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.Column", "sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.elements.ColumnClause", "sqlalchemy.sql.roles.DDLReferredColumnRole", "sqlalchemy.sql.roles.LabeledColumnExprRole", "sqlalchemy.sql.roles.StrAsPlainColumnRole", "sqlalchemy.sql.base.Immutable", "sqlalchemy.sql.elements.NamedColumn", "sqlalchemy.sql.elements.KeyedColumnElement", "sqlalchemy.sql.elements.ColumnElement", "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "sqlalchemy.sql.roles.ColumnArgumentRole", "sqlalchemy.sql.roles.StatementOptionRole", "sqlalchemy.sql.roles.WhereHavingRole", "sqlalchemy.sql.roles.OnClauseRole", "sqlalchemy.sql.roles.BinaryElementRole", "sqlalchemy.sql.roles.OrderByRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.ByOfRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.LimitOffsetRole", "sqlalchemy.sql.roles.DMLColumnRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.DDLExpressionRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.elements.SQLColumnExpression", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", null, null, "args", "name", "type_", "autoincrement", "default", "insert_default", "doc", "key", "index", "unique", "info", "nullable", "onupdate", "primary_key", "server_default", "server_onupdate", "quote", "system", "comment", "insert_sentinel", "_omit_from_statements", "_proxies", "dialect_kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Column.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", null, null, "args", "name", "type_", "autoincrement", "default", "insert_default", "doc", "key", "index", "unique", "info", "nullable", "onupdate", "primary_key", "server_default", "server_onupdate", "quote", "system", "comment", "insert_sentinel", "_omit_from_statements", "_proxies", "dialect_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypeEngineArgument"}, "sqlalchemy.sql.base.SchemaEventTarget", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypeEngineArgument"}, "sqlalchemy.sql.base.SchemaEventTarget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "sqlalchemy.sql.base.SchemaEventTarget", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypeEngineArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._AutoIncrementType"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "sqlalchemy.sql.schema.SchemaConst", "value": "NULL_UNSPECIFIED"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._ServerDefaultArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._ServerOnUpdateArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Column", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Column.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Column", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Column.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of Column", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.Column.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Column._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of Column", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_default_description_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.Column._default_description_tuple", "name": "_default_description_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_default_description_tuple of Column", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.base._DefaultDescriptionTuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Column._default_description_tuple", "name": "_default_description_tuple", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": "sqlalchemy.sql.base._DefaultDescriptionTuple"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.HasMemoized.memoized_attribute"}}}}, "_extra_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Column._extra_kwargs", "name": "_extra_kwargs", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extra_kwargs of Column", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_gen_static_annotations_cache_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.Column._gen_static_annotations_cache_key", "name": "_gen_static_annotations_cache_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_gen_static_annotations_cache_key of Column", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Column._gen_static_annotations_cache_key", "name": "_gen_static_annotations_cache_key", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_make_proxy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "selectable", "name", "key", "name_is_truncatable", "compound_select_cols", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Column._make_proxy", "name": "_make_proxy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "selectable", "name", "key", "name_is_truncatable", "compound_select_cols", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "sqlalchemy.sql.selectable.FromClause", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_proxy of Column", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnClause"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_merge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Column._merge", "name": "_merge", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_merge of Column", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_table_attach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Column._on_table_attach", "name": "_on_table_attach", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_on_table_attach of Column", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_onupdate_description_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.Column._onupdate_description_tuple", "name": "_onupdate_description_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_onupdate_description_tuple of Column", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.base._DefaultDescriptionTuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Column._onupdate_description_tuple", "name": "_onupdate_description_tuple", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": "sqlalchemy.sql.base._DefaultDescriptionTuple"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.HasMemoized.memoized_attribute"}}}}, "_set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 4], "arg_names": ["self", "parent", "all_names", "allow_replacements", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Column._set_parent", "name": "_set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 4], "arg_names": ["self", "parent", "all_names", "allow_replacements", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "sqlalchemy.sql.base.SchemaEventTarget", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent of Column", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "type_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Column._set_type", "name": "_set_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "type_"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_type of Column", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_setup_on_memoized_fks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Column._setup_on_memoized_fks", "name": "_setup_on_memoized_fks", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_on_memoized_fks of Column", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_user_defined_nullable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Column._user_defined_nullable", "name": "_user_defined_nullable", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "sqlalchemy.sql.schema.SchemaConst", "value": "NULL_UNSPECIFIED"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "append_foreign_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fk"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Column.append_foreign_key", "name": "append_foreign_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fk"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "sqlalchemy.sql.schema.ForeignKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append_foreign_key of Column", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "autoincrement": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Column.autoincrement", "name": "autoincrement", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._AutoIncrementType"}}}, "comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Column.comment", "name": "comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "computed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Column.computed", "name": "computed", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Computed", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Column.constraints", "name": "constraints", "type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.Constraint"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.Column.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of Column", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Column.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of Column", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "doc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Column.doc", "name": "doc", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "foreign_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Column.foreign_keys", "name": "foreign_keys", "type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.ForeignKey"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "identity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Column.identity", "name": "identity", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Identity", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Column.index", "name": "index", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.Column.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Column.key", "name": "key", "type": "builtins.str"}}, "nullable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Column.nullable", "name": "nullable", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "references": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "column"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Column.references", "name": "references", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "column"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "references of Column", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "server_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Column.server_default", "name": "server_default", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.FetchedValue", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "system": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Column.system", "name": "system", "type": "builtins.bool"}}, "table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Column.table", "name": "table", "type": "sqlalchemy.sql.schema.Table"}}, "unique": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Column.unique", "name": "unique", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Column.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.schema.Column", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "ColumnClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnClause", "kind": "Gdef"}, "ColumnCollectionConstraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.ColumnCollectionMixin", "sqlalchemy.sql.schema.Constraint"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.ColumnCollectionConstraint", "name": "ColumnCollectionConstraint", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnCollectionConstraint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.ColumnCollectionConstraint", "sqlalchemy.sql.schema.ColumnCollectionMixin", "sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.schema.HasConditionalDDL", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnCollectionConstraint.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionConstraint", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of ColumnCollectionConstraint", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "columns", "name", "deferrable", "initially", "info", "_autoattach", "_column_flag", "_gather_expressions", "dialect_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnCollectionConstraint.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "columns", "name", "deferrable", "initially", "info", "_autoattach", "_column_flag", "_gather_expressions", "dialect_kw"], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionConstraint", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._DDLColumnArgument"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._ConstraintNameArgument"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._DDLColumnArgument"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ColumnCollectionConstraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnCollectionConstraint.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of ColumnCollectionConstraint", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnCollectionConstraint.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of ColumnCollectionConstraint", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "target_table", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnCollectionConstraint._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "target_table", "kw"], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionConstraint", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of ColumnCollectionConstraint", "ret_type": "sqlalchemy.sql.schema.ColumnCollectionConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnCollectionConstraint._set_parent", "name": "_set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionConstraint", "sqlalchemy.sql.base.SchemaEventTarget", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent of ColumnCollectionConstraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.ColumnCollectionConstraint.columns", "name": "columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}}}, "contains_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "col"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnCollectionConstraint.contains_column", "name": "contains_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "col"], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionConstraint", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contains_column of ColumnCollectionConstraint", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "target_table", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.ColumnCollectionConstraint.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "target_table", "kw"], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionConstraint", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of ColumnCollectionConstraint", "ret_type": "sqlalchemy.sql.schema.ColumnCollectionConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ColumnCollectionConstraint.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "target_table", "kw"], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionConstraint", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of ColumnCollectionConstraint", "ret_type": "sqlalchemy.sql.schema.ColumnCollectionConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.ColumnCollectionConstraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.ColumnCollectionConstraint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColumnCollectionMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin", "name": "ColumnCollectionMixin", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.ColumnCollectionMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5], "arg_names": ["self", "columns", "_autoattach", "_column_flag", "_gather_expressions"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5], "arg_names": ["self", "columns", "_autoattach", "_column_flag", "_gather_expressions"], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionMixin", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._DDLColumnArgument"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ColumnCollectionMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_allow_multiple_tables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin._allow_multiple_tables", "name": "_allow_multiple_tables", "type": "builtins.bool"}}, "_check_attach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "evt"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin._check_attach", "name": "_check_attach", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "evt"], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionMixin", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_attach of ColumnCollectionMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_col_expressions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin._col_expressions", "name": "_col_expressions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionMixin", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_col_expressions of ColumnCollectionMixin", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cols_wo_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin._cols_wo_table", "name": "_cols_wo_table", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_column_flag": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin._column_flag", "name": "_column_flag", "type": "builtins.bool"}}, "_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin._columns", "name": "_columns", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.base.DedupeColumnCollection"}}}, "_pending_colargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin._pending_colargs", "name": "_pending_colargs", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin._set_parent", "name": "_set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionMixin", "sqlalchemy.sql.base.SchemaEventTarget", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent of ColumnCollectionMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_parent_with_dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin._set_parent_with_dispatch", "name": "_set_parent_with_dispatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionMixin", "sqlalchemy.sql.base.SchemaEventTarget", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent_with_dispatch of ColumnCollectionMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "c": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin.c", "name": "c", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "c of ColumnCollectionMixin", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin.c", "name": "c", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "c of ColumnCollectionMixin", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin.columns", "name": "columns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "columns of ColumnCollectionMixin", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin.columns", "name": "columns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ColumnCollectionMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "columns of ColumnCollectionMixin", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.ColumnCollectionMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.ColumnCollectionMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColumnDefault": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.DefaultGenerator", "abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.ColumnDefault", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnDefault", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.ColumnDefault", "sqlalchemy.sql.schema.DefaultGenerator", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": ["is_static"], "fullname": "sqlalchemy.sql.schema.ColumnDefault.__new__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["cls", "arg", "for_update"], "dataclass_transform_spec": null, "flags": ["is_static", "is_overload"], "fullname": "sqlalchemy.sql.schema.ColumnDefault.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "arg", "for_update"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.ColumnDefault"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ColumnDefault", "ret_type": "sqlalchemy.sql.schema.ColumnDefault", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "arg", "for_update"], "dataclass_transform_spec": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.schema.ColumnDefault.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "arg", "for_update"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.ColumnDefault"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ColumnDefault", "ret_type": "sqlalchemy.sql.schema.CallableColumnDefault", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ColumnDefault.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "arg", "for_update"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.ColumnDefault"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ColumnDefault", "ret_type": "sqlalchemy.sql.schema.CallableColumnDefault", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "arg", "for_update"], "dataclass_transform_spec": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.schema.ColumnDefault.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "arg", "for_update"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.ColumnDefault"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ColumnDefault", "ret_type": "sqlalchemy.sql.schema.ColumnElementColumnDefault", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ColumnDefault.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "arg", "for_update"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.ColumnDefault"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ColumnDefault", "ret_type": "sqlalchemy.sql.schema.ColumnElementColumnDefault", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "arg", "for_update"], "dataclass_transform_spec": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.schema.ColumnDefault.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "arg", "for_update"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.ColumnDefault"}, "builtins.object", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ColumnDefault", "ret_type": "sqlalchemy.sql.schema.ColumnDefault", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ColumnDefault.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "arg", "for_update"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.ColumnDefault"}, "builtins.object", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ColumnDefault", "ret_type": "sqlalchemy.sql.schema.ColumnDefault", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "arg", "for_update"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.ColumnDefault"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ColumnDefault", "ret_type": "sqlalchemy.sql.schema.CallableColumnDefault", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "arg", "for_update"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.ColumnDefault"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ColumnDefault", "ret_type": "sqlalchemy.sql.schema.ColumnElementColumnDefault", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "arg", "for_update"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.ColumnDefault"}, "builtins.object", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ColumnDefault", "ret_type": "sqlalchemy.sql.schema.ColumnDefault", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnDefault.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.schema.ColumnDefault"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of ColumnDefault", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.ColumnDefault.arg", "name": "arg", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.ColumnDefault.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.ColumnDefault", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "ColumnElementColumnDefault": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.ColumnDefault"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.ColumnElementColumnDefault", "name": "ColumnElementColumnDefault", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnElementColumnDefault", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.ColumnElementColumnDefault", "sqlalchemy.sql.schema.ColumnDefault", "sqlalchemy.sql.schema.DefaultGenerator", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "arg", "for_update"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnElementColumnDefault.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "arg", "for_update"], "arg_types": ["sqlalchemy.sql.schema.ColumnElementColumnDefault", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._SQLExprDefault"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ColumnElementColumnDefault", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_arg_is_typed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.ColumnElementColumnDefault._arg_is_typed", "name": "_arg_is_typed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ColumnElementColumnDefault"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_arg_is_typed of ColumnElementColumnDefault", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ColumnElementColumnDefault._arg_is_typed", "name": "_arg_is_typed", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ColumnElementColumnDefault._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ColumnElementColumnDefault"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of ColumnElementColumnDefault", "ret_type": "sqlalchemy.sql.schema.ColumnElementColumnDefault", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.ColumnElementColumnDefault.arg", "name": "arg", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._SQLExprDefault"}}}, "has_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.ColumnElementColumnDefault.has_arg", "name": "has_arg", "type": "builtins.bool"}}, "is_clause_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.ColumnElementColumnDefault.is_clause_element", "name": "is_clause_element", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.ColumnElementColumnDefault.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.ColumnElementColumnDefault", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Computed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.FetchedValue", "sqlalchemy.sql.schema.SchemaItem"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.Computed", "name": "Computed", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.Computed", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.Computed", "sqlalchemy.sql.schema.FetchedValue", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "sqltext", "persisted"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.Computed.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "sqltext", "persisted"], "arg_types": ["sqlalchemy.sql.schema.Computed", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._DDLColumnArgument"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Computed", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Computed.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "sqltext", "persisted"], "arg_types": ["sqlalchemy.sql.schema.Computed", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._DDLColumnArgument"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Computed", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.Computed.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_as_for_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "for_update"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Computed._as_for_update", "name": "_as_for_update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "for_update"], "arg_types": ["sqlalchemy.sql.schema.Computed", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_as_for_update of Computed", "ret_type": "sqlalchemy.sql.schema.FetchedValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "target_table", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Computed._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "target_table", "kw"], "arg_types": ["sqlalchemy.sql.schema.Computed", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of Computed", "ret_type": "sqlalchemy.sql.schema.Computed", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Computed._set_parent", "name": "_set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "arg_types": ["sqlalchemy.sql.schema.Computed", "sqlalchemy.sql.base.SchemaEventTarget", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent of Computed", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Computed.column", "name": "column", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "target_table", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.Computed.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "target_table", "kw"], "arg_types": ["sqlalchemy.sql.schema.Computed", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of Computed", "ret_type": "sqlalchemy.sql.schema.Computed", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Computed.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "target_table", "kw"], "arg_types": ["sqlalchemy.sql.schema.Computed", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of Computed", "ret_type": "sqlalchemy.sql.schema.Computed", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "persisted": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Computed.persisted", "name": "persisted", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "sqltext": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Computed.sqltext", "name": "sqltext", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Computed.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.Computed", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "Constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.schema.HasConditionalDDL", "sqlalchemy.sql.schema.SchemaItem"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.Constraint", "name": "Constraint", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.Constraint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.schema.HasConditionalDDL", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "name", "deferrable", "initially", "info", "comment", "_create_rule", "_type_bound", "dialect_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Constraint.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "name", "deferrable", "initially", "info", "comment", "_create_rule", "_type_bound", "dialect_kw"], "arg_types": ["sqlalchemy.sql.schema.Constraint", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._ConstraintNameArgument"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Constraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.Constraint.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_column_flag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Constraint._column_flag", "name": "_column_flag", "type": "builtins.bool"}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Constraint._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Constraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.Constraint", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of Constraint", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Constraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.Constraint", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Constraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.Constraint", "values": [], "variance": 0}]}}}, "_create_rule": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Constraint._create_rule", "name": "_create_rule", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_creation_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Constraint._creation_order", "name": "_creation_order", "type": "builtins.int"}}, "_set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Constraint._set_parent", "name": "_set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "arg_types": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.base.SchemaEventTarget", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent of Constraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_should_create_for_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "compiler", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Constraint._should_create_for_compiler", "name": "_should_create_for_compiler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "compiler", "kw"], "arg_types": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.compiler.DDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_create_for_compiler of Constraint", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_type_bound": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Constraint._type_bound", "name": "_type_bound", "type": "builtins.bool"}}, "comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Constraint.comment", "name": "comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.Constraint.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Constraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.Constraint", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of Constraint", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Constraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.Constraint", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Constraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.Constraint", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Constraint.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Constraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.Constraint", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of Constraint", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Constraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.Constraint", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Constraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.Constraint", "values": [], "variance": 0}]}}}}, "deferrable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Constraint.deferrable", "name": "deferrable", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "initially": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Constraint.initially", "name": "initially", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Constraint.name", "name": "name", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._ConstraintNameArgument"}}}, "parent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Constraint.parent", "name": "parent", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "uses_pep604_syntax": false}}}, "table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.Constraint.table", "name": "table", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "table of Constraint", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Constraint.table", "name": "table", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "table of Constraint", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Constraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.Constraint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CoreExecuteOptionsParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.CoreExecuteOptionsParameter", "kind": "Gdef"}, "DDLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.DDLCompiler", "kind": "Gdef"}, "DEFAULT_NAMING_CONVENTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.DEFAULT_NAMING_CONVENTION", "name": "DEFAULT_NAMING_CONVENTION", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaParameter"}}}, "DedupeColumnCollection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.DedupeColumnCollection", "kind": "Gdef"}, "DefaultClause": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.FetchedValue"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.DefaultClause", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.DefaultClause", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.DefaultClause", "sqlalchemy.sql.schema.FetchedValue", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "arg", "for_update", "_reflected"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.DefaultClause.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "arg", "for_update", "_reflected"], "arg_types": ["sqlalchemy.sql.schema.DefaultClause", {".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.elements.TextClause"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.DefaultClause.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.schema.DefaultClause"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.DefaultClause._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.DefaultClause"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "sqlalchemy.sql.schema.DefaultClause", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.DefaultClause.arg", "name": "arg", "type": {".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.elements.TextClause"], "uses_pep604_syntax": false}}}, "has_argument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.DefaultClause.has_argument", "name": "has_argument", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.DefaultClause.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.DefaultClause", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DefaultGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.Executable", "sqlalchemy.sql.schema.SchemaItem"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.DefaultGenerator", "name": "DefaultGenerator", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.DefaultGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.DefaultGenerator", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "for_update"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.DefaultGenerator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "for_update"], "arg_types": ["sqlalchemy.sql.schema.DefaultGenerator", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DefaultGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.DefaultGenerator.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.DefaultGenerator._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.DefaultGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of DefaultGenerator", "ret_type": "sqlalchemy.sql.schema.DefaultGenerator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_execute_on_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "connection", "distilled_params", "execution_options"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.DefaultGenerator._execute_on_connection", "name": "_execute_on_connection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "connection", "distilled_params", "execution_options"], "arg_types": ["sqlalchemy.sql.schema.DefaultGenerator", "sqlalchemy.engine.base.Connection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.CoreExecuteOptionsParameter"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_on_connection of DefaultGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_execute_on_scalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "connection", "distilled_params", "execution_options"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.DefaultGenerator._execute_on_scalar", "name": "_execute_on_scalar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "connection", "distilled_params", "execution_options"], "arg_types": ["sqlalchemy.sql.schema.DefaultGenerator", "sqlalchemy.engine.base.Connection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.CoreExecuteOptionsParameter"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_on_scalar of DefaultGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_default_generator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.DefaultGenerator._is_default_generator", "name": "_is_default_generator", "type": "builtins.bool"}}, "_set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.DefaultGenerator._set_parent", "name": "_set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "arg_types": ["sqlalchemy.sql.schema.DefaultGenerator", "sqlalchemy.sql.base.SchemaEventTarget", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent of DefaultGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.DefaultGenerator.column", "name": "column", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "for_update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.DefaultGenerator.for_update", "name": "for_update", "type": "builtins.bool"}}, "has_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.DefaultGenerator.has_arg", "name": "has_arg", "type": "builtins.bool"}}, "is_callable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.DefaultGenerator.is_callable", "name": "is_callable", "type": "builtins.bool"}}, "is_clause_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.DefaultGenerator.is_clause_element", "name": "is_clause_element", "type": "builtins.bool"}}, "is_identity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.DefaultGenerator.is_identity", "name": "is_identity", "type": "builtins.bool"}}, "is_scalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.DefaultGenerator.is_scalar", "name": "is_scalar", "type": "builtins.bool"}}, "is_sentinel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.DefaultGenerator.is_sentinel", "name": "is_sentinel", "type": "builtins.bool"}}, "is_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.DefaultGenerator.is_sequence", "name": "is_sequence", "type": "builtins.bool"}}, "is_server_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.DefaultGenerator.is_server_default", "name": "is_server_default", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.DefaultGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.DefaultGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DialectKWArgs": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.DialectKWArgs", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Engine", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef"}, "ExecutionContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ExecutionContext", "kind": "Gdef"}, "ExternallyTraversible": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors.ExternallyTraversible", "kind": "Gdef"}, "FetchedValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.SchemaEventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.FetchedValue", "name": "FetchedValue", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.FetchedValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.FetchedValue", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "for_update"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.FetchedValue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "for_update"], "arg_types": ["sqlalchemy.sql.schema.FetchedValue", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FetchedValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.FetchedValue.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.schema.FetchedValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of FetchedValue", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_as_for_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "for_update"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.FetchedValue._as_for_update", "name": "_as_for_update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "for_update"], "arg_types": ["sqlalchemy.sql.schema.FetchedValue", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_as_for_update of FetchedValue", "ret_type": "sqlalchemy.sql.schema.FetchedValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "for_update"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.FetchedValue._clone", "name": "_clone", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "for_update"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.FetchedValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.FetchedValue", "values": [], "variance": 0}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clone of FetchedV<PERSON>ue", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.FetchedValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.FetchedValue", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.FetchedValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.FetchedValue", "values": [], "variance": 0}]}}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.FetchedValue._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.FetchedValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of FetchedValue", "ret_type": "sqlalchemy.sql.schema.FetchedValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.FetchedValue._set_parent", "name": "_set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "arg_types": ["sqlalchemy.sql.schema.FetchedValue", "sqlalchemy.sql.base.SchemaEventTarget", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent of FetchedV<PERSON>ue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.FetchedValue.column", "name": "column", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "for_update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.FetchedValue.for_update", "name": "for_update", "type": "builtins.bool"}}, "has_argument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.FetchedValue.has_argument", "name": "has_argument", "type": "builtins.bool"}}, "is_clause_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.FetchedValue.is_clause_element", "name": "is_clause_element", "type": "builtins.bool"}}, "is_identity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.FetchedValue.is_identity", "name": "is_identity", "type": "builtins.bool"}}, "is_server_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.FetchedValue.is_server_default", "name": "is_server_default", "type": "builtins.bool"}}, "reflected": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.FetchedValue.reflected", "name": "reflected", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.FetchedValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.FetchedValue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Final", "kind": "Gdef"}, "ForeignKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.schema.SchemaItem"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.ForeignKey", "name": "ForeignKey", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.ForeignKey", "sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "column", "_constraint", "use_alter", "name", "onupdate", "ondelete", "deferrable", "initially", "link_to_name", "match", "info", "comment", "_unresolvable", "dialect_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "column", "_constraint", "use_alter", "name", "onupdate", "ondelete", "deferrable", "initially", "link_to_name", "match", "info", "comment", "_unresolvable", "dialect_kw"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._DDLColumnArgument"}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.ForeignKeyConstraint", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._ConstraintNameArgument"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ForeignKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.schema.ForeignKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of ForeignKey", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.ForeignKey.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_colspec": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey._colspec", "name": "_colspec", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "_column_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.ForeignKey._column_tokens", "name": "_column_tokens", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_column_tokens of ForeignKey", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey._column_tokens", "name": "_column_tokens", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "schema", "kw"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of ForeignKey", "ret_type": "sqlalchemy.sql.schema.ForeignKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_colspec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "schema", "table_name", "_is_copy"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey._get_colspec", "name": "_get_colspec", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "schema", "table_name", "_is_copy"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "sqlalchemy.sql.schema.SchemaConst", "value": "RETAIN_SCHEMA"}, {".class": "LiteralType", "fallback": "sqlalchemy.sql.schema.SchemaConst", "value": "BLANK_SCHEMA"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_colspec of ForeignKey", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_link_to_col_by_colstring": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "parenttable", "table", "colname"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey._link_to_col_by_colstring", "name": "_link_to_col_by_colstring", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "parenttable", "table", "colname"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", "sqlalchemy.sql.schema.Table", "sqlalchemy.sql.schema.Table", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_link_to_col_by_colstring of ForeignKey", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_referred_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.ForeignKey._referred_schema", "name": "_referred_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_referred_schema of ForeignKey", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey._referred_schema", "name": "_referred_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_referred_schema of ForeignKey", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_remove_from_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metadata"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey._remove_from_metadata", "name": "_remove_from_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "metadata"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_from_metadata of ForeignKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_col_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey._resolve_col_tokens", "name": "_resolve_col_tokens", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_col_tokens of ForeignKey", "ret_type": {".class": "TupleType", "implicit": false, "items": ["sqlalchemy.sql.schema.Table", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey._resolve_column", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "raiseerr"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.sql.schema.ForeignKey._resolve_column", "name": "_resolve_column", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "raiseerr"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_column of ForeignKey", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "raiseerr"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.schema.ForeignKey._resolve_column", "name": "_resolve_column", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "raiseerr"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_column of ForeignKey", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey._resolve_column", "name": "_resolve_column", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "raiseerr"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_column of ForeignKey", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "raiseerr"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.schema.ForeignKey._resolve_column", "name": "_resolve_column", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "raiseerr"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_column of ForeignKey", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey._resolve_column", "name": "_resolve_column", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "raiseerr"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_column of ForeignKey", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "raiseerr"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_column of ForeignKey", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "raiseerr"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_column of ForeignKey", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey._set_parent", "name": "_set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", "sqlalchemy.sql.base.SchemaEventTarget", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent of ForeignKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_remote_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey._set_remote_table", "name": "_set_remote_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "table"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_remote_table of ForeignKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "column", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey._set_table", "name": "_set_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "column", "table"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_table of ForeignKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_target_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "column"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey._set_target_column", "name": "_set_target_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "column"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_target_column of ForeignKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_table_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.ForeignKey._table_column", "name": "_table_column", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_table_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey._table_key", "name": "_table_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_table_key of ForeignKey", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unresolvable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey._unresolvable", "name": "_unresolvable", "type": "builtins.bool"}}, "_unvalidated_dialect_kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey._unvalidated_dialect_kw", "name": "_unvalidated_dialect_kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.ForeignKey.column", "name": "column", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column of Foreign<PERSON>ey", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey.column", "name": "column", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column of Foreign<PERSON>ey", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey.comment", "name": "comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "constraint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey.constraint", "name": "constraint", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.ForeignKeyConstraint", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.ForeignKey.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "schema", "kw"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of Foreign<PERSON>ey", "ret_type": "sqlalchemy.sql.schema.ForeignKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "schema", "kw"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of Foreign<PERSON>ey", "ret_type": "sqlalchemy.sql.schema.ForeignKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "deferrable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey.deferrable", "name": "deferrable", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "get_referent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey.get_referent", "name": "get_referent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "table"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", "sqlalchemy.sql.selectable.FromClause"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_referent of ForeignKey", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "initially": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey.initially", "name": "initially", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "link_to_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey.link_to_name", "name": "link_to_name", "type": "builtins.bool"}}, "match": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey.match", "name": "match", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey.name", "name": "name", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._ConstraintNameArgument"}}}, "ondelete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey.ondelete", "name": "ondelete", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "onupdate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey.onupdate", "name": "onupdate", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.ForeignKey.parent", "name": "parent", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "references": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKey.references", "name": "references", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "table"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "references of Foreign<PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "target_fullname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.ForeignKey.target_fullname", "name": "target_fullname", "type": "builtins.property"}}, "use_alter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKey.use_alter", "name": "use_alter", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.ForeignKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.ForeignKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ForeignKeyConstraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.ColumnCollectionConstraint"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint", "name": "ForeignKeyConstraint", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.ForeignKeyConstraint", "sqlalchemy.sql.schema.ColumnCollectionConstraint", "sqlalchemy.sql.schema.ColumnCollectionMixin", "sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.schema.HasConditionalDDL", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "columns", "refcolumns", "name", "onupdate", "ondelete", "deferrable", "initially", "use_alter", "link_to_name", "match", "table", "info", "comment", "dialect_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "columns", "refcolumns", "name", "onupdate", "ondelete", "deferrable", "initially", "use_alter", "link_to_name", "match", "table", "info", "comment", "dialect_kw"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._DDLColumnArgument"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._DDLColumnArgument"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._ConstraintNameArgument"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ForeignKeyConstraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_append_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "column", "fk"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint._append_element", "name": "_append_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "column", "fk"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "sqlalchemy.sql.schema.ForeignKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_append_element of ForeignKeyConstraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_col_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint._col_description", "name": "_col_description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_col_description of ForeignKeyConstraint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint._col_description", "name": "_col_description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_col_description of ForeignKeyConstraint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "schema", "target_table", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "schema", "target_table", "kw"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of ForeignKeyConstraint", "ret_type": "sqlalchemy.sql.schema.ForeignKeyConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint._elements", "name": "_elements", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_elements of ForeignKeyConstraint", "ret_type": {".class": "Instance", "args": ["builtins.str", "sqlalchemy.sql.schema.ForeignKey"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint._elements", "name": "_elements", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_elements of ForeignKeyConstraint", "ret_type": {".class": "Instance", "args": ["builtins.str", "sqlalchemy.sql.schema.ForeignKey"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_referred_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint._referred_schema", "name": "_referred_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_referred_schema of ForeignKeyConstraint", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint._referred_schema", "name": "_referred_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_referred_schema of ForeignKeyConstraint", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint._set_parent", "name": "_set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint", "sqlalchemy.sql.base.SchemaEventTarget", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent of ForeignKeyConstraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_dest_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint._validate_dest_table", "name": "_validate_dest_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "table"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint", "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_dest_table of ForeignKeyConstraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "column_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.column_keys", "name": "column_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column_keys of ForeignKeyConstraint", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.column_keys", "name": "column_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column_keys of ForeignKeyConstraint", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.columns", "name": "columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "schema", "target_table", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "schema", "target_table", "kw"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of ForeignKeyConstraint", "ret_type": "sqlalchemy.sql.schema.ForeignKeyConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["self", "schema", "target_table", "kw"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of ForeignKeyConstraint", "ret_type": "sqlalchemy.sql.schema.ForeignKeyConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.elements", "name": "elements", "type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.ForeignKey"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "link_to_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.link_to_name", "name": "link_to_name", "type": "builtins.bool"}}, "match": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.match", "name": "match", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ondelete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.ondelete", "name": "ondelete", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "onupdate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.onupdate", "name": "onupdate", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "referred_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.referred_table", "name": "referred_table", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "referred_table of ForeignKeyConstraint", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.referred_table", "name": "referred_table", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "referred_table of ForeignKeyConstraint", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "use_alter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.use_alter", "name": "use_alter", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.ForeignKeyConstraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.ForeignKeyConstraint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FromClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.FromClause", "kind": "Gdef"}, "Function": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.Function", "kind": "Gdef"}, "HasConditionalDDL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.HasConditionalDDL", "name": "HasConditionalDDL", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.HasConditionalDDL", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.HasConditionalDDL", "builtins.object"], "names": {".class": "SymbolTable", "_ddl_if": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.HasConditionalDDL._ddl_if", "name": "_ddl_if", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.ddl.DDLIf"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ddl_if": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "dialect", "callable_", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.HasConditionalDDL.ddl_if", "name": "ddl_if", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "dialect", "callable_", "state"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.HasConditionalDDL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.HasConditionalDDL", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.ddl.DDLIfCallable", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ddl_if of HasConditionalDDL", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.HasConditionalDDL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.HasConditionalDDL", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.HasConditionalDDL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.HasConditionalDDL", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.HasConditionalDDL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.HasConditionalDDL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HasMemoized": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.HasMemoized", "kind": "Gdef"}, "HasSchemaAttr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.SchemaItem"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.HasSchemaAttr", "name": "HasSchemaAttr", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.HasSchemaAttr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.HasSchemaAttr", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.HasSchemaAttr.schema", "name": "schema", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.HasSchemaAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.HasSchemaAttr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Identity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.IdentityOptions", "sqlalchemy.sql.schema.FetchedValue", "sqlalchemy.sql.schema.SchemaItem"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.Identity", "name": "Identity", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.Identity", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.Identity", "sqlalchemy.sql.schema.IdentityOptions", "sqlalchemy.sql.schema.FetchedValue", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "always", "on_null", "start", "increment", "minvalue", "maxvalue", "nominvalue", "nomaxvalue", "cycle", "cache", "order"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Identity.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "always", "on_null", "start", "increment", "minvalue", "maxvalue", "nominvalue", "nomaxvalue", "cycle", "cache", "order"], "arg_types": ["sqlalchemy.sql.schema.Identity", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Identity", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.Identity.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_as_for_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "for_update"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Identity._as_for_update", "name": "_as_for_update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "for_update"], "arg_types": ["sqlalchemy.sql.schema.Identity", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_as_for_update of Identity", "ret_type": "sqlalchemy.sql.schema.FetchedValue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Identity._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": ["sqlalchemy.sql.schema.Identity", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of Identity", "ret_type": "sqlalchemy.sql.schema.Identity", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Identity._set_parent", "name": "_set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "arg_types": ["sqlalchemy.sql.schema.Identity", "sqlalchemy.sql.base.SchemaEventTarget", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent of Identity", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "always": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Identity.always", "name": "always", "type": "builtins.bool"}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.Identity.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": ["sqlalchemy.sql.schema.Identity", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of Identity", "ret_type": "sqlalchemy.sql.schema.Identity", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Identity.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": ["sqlalchemy.sql.schema.Identity", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of Identity", "ret_type": "sqlalchemy.sql.schema.Identity", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_identity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.Identity.is_identity", "name": "is_identity", "type": "builtins.bool"}}, "on_null": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Identity.on_null", "name": "on_null", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Identity.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.Identity", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IdentityOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.IdentityOptions", "name": "IdentityOptions", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.IdentityOptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.IdentityOptions", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "start", "increment", "minvalue", "maxvalue", "nominvalue", "nomaxvalue", "cycle", "cache", "order"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.IdentityOptions.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "start", "increment", "minvalue", "maxvalue", "nominvalue", "nomaxvalue", "cycle", "cache", "order"], "arg_types": ["sqlalchemy.sql.schema.IdentityOptions", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IdentityOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_increment_is_negative": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.IdentityOptions._increment_is_negative", "name": "_increment_is_negative", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.IdentityOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_increment_is_negative of IdentityOptions", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.IdentityOptions._increment_is_negative", "name": "_increment_is_negative", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.IdentityOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_increment_is_negative of IdentityOptions", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.IdentityOptions.cache", "name": "cache", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "cycle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.IdentityOptions.cycle", "name": "cycle", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "increment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.IdentityOptions.increment", "name": "increment", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "maxvalue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.IdentityOptions.maxvalue", "name": "maxvalue", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "minvalue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.IdentityOptions.minvalue", "name": "minvalue", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "nomaxvalue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.IdentityOptions.nomaxvalue", "name": "nomaxvalue", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "nominvalue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.IdentityOptions.nominvalue", "name": "nominvalue", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "order": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.IdentityOptions.order", "name": "order", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "start": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.IdentityOptions.start", "name": "start", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.IdentityOptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.IdentityOptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Index": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.schema.ColumnCollectionMixin", "sqlalchemy.sql.schema.HasConditionalDDL", "sqlalchemy.sql.schema.SchemaItem"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.Index", "name": "Index", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.Index", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.Index", "sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.schema.ColumnCollectionMixin", "sqlalchemy.sql.schema.HasConditionalDDL", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "name", "expressions", "unique", "quote", "info", "_table", "_column_flag", "dialect_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Index.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "name", "expressions", "unique", "quote", "info", "_table", "_column_flag", "dialect_kw"], "arg_types": ["sqlalchemy.sql.schema.Index", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._DDLColumnArgument"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Index", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Index.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.schema.Index"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Index", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.Index.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Index._set_parent", "name": "_set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "arg_types": ["sqlalchemy.sql.schema.Index", "sqlalchemy.sql.base.SchemaEventTarget", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent of Index", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_table_bound_expressions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Index._table_bound_expressions", "name": "_table_bound_expressions", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "bind", "checkfirst"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Index.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "bind", "checkfirst"], "arg_types": ["sqlalchemy.sql.schema.Index", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._CreateDropBind"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of Index", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "drop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "bind", "checkfirst"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Index.drop", "name": "drop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "bind", "checkfirst"], "arg_types": ["sqlalchemy.sql.schema.Index", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._CreateDropBind"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop of Index", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expressions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Index.expressions", "name": "expressions", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Index.name", "name": "name", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Index.table", "name": "table", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "unique": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Index.unique", "name": "unique", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Index.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.Index", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MetaData": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.HasSchemaAttr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.MetaData", "name": "MetaData", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.MetaData", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.MetaData", "sqlalchemy.sql.schema.HasSchemaAttr", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.MetaData.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.schema.MetaData", {".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.schema.Table"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of MetaData", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.MetaData.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getstate__ of MetaData", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "schema", "quote_schema", "naming_convention", "info"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.MetaData.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "schema", "quote_schema", "naming_convention", "info"], "arg_types": ["sqlalchemy.sql.schema.MetaData", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaParameter"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.MetaData.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of MetaData", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.MetaData.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["sqlalchemy.sql.schema.MetaData", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setstate__ of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.MetaData.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_add_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "schema", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.MetaData._add_table", "name": "_add_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "schema", "table"], "arg_types": ["sqlalchemy.sql.schema.MetaData", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_table of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fk_memos": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.MetaData._fk_memos", "name": "_fk_memos", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["sqlalchemy.sql.schema.ForeignKey"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_remove_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.MetaData._remove_table", "name": "_remove_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "schema"], "arg_types": ["sqlalchemy.sql.schema.MetaData", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_table of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_schemas": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.MetaData._schemas", "name": "_schemas", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_sequences": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.MetaData._sequences", "name": "_sequences", "type": {".class": "Instance", "args": ["builtins.str", "sqlalchemy.sql.schema.Sequence"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.MetaData.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "bind", "tables", "checkfirst"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.MetaData.create_all", "name": "create_all", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "bind", "tables", "checkfirst"], "arg_types": ["sqlalchemy.sql.schema.MetaData", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._CreateDropBind"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.sql.schema.Table"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_all of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "drop_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "bind", "tables", "checkfirst"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.MetaData.drop_all", "name": "drop_all", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "bind", "tables", "checkfirst"], "arg_types": ["sqlalchemy.sql.schema.MetaData", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._CreateDropBind"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.sql.schema.Table"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_all of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "naming_convention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.MetaData.naming_convention", "name": "naming_convention", "type": {".class": "UnionType", "items": [{".class": "TypedDictType", "fallback": "sqlalchemy.sql.schema._NamingSchemaTD", "items": [["fk", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaDirective"}], ["pk", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaDirective"}], ["ix", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaDirective"}], ["ck", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaDirective"}], ["uq", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaDirective"}]], "readonly_keys": [], "required_keys": []}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaDirective"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}}}, "reflect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.sql.schema.MetaData.reflect", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "bind", "schema", "views", "only", "extend_existing", "autoload_replace", "resolve_fks", "dialect_kwargs"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.MetaData.reflect", "name": "reflect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "bind", "schema", "views", "only", "extend_existing", "autoload_replace", "resolve_fks", "dialect_kwargs"], "arg_types": ["sqlalchemy.sql.schema.MetaData", {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reflect of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.MetaData.reflect", "name": "reflect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "bind", "schema", "views", "only", "extend_existing", "autoload_replace", "resolve_fks", "dialect_kwargs"], "arg_types": ["sqlalchemy.sql.schema.MetaData", {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reflect of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "bind", "schema", "views", "only", "extend_existing", "autoload_replace", "resolve_fks", "dialect_kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.schema.MetaData.reflect", "name": "reflect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "bind", "schema", "views", "only", "extend_existing", "autoload_replace", "resolve_fks", "dialect_kwargs"], "arg_types": ["sqlalchemy.sql.schema.MetaData", "sqlalchemy.engine.base.Engine", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reflect of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.MetaData.reflect", "name": "reflect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "bind", "schema", "views", "only", "extend_existing", "autoload_replace", "resolve_fks", "dialect_kwargs"], "arg_types": ["sqlalchemy.sql.schema.MetaData", "sqlalchemy.engine.base.Engine", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reflect of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "bind", "schema", "views", "only", "extend_existing", "autoload_replace", "resolve_fks", "dialect_kwargs"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql.schema.MetaData.reflect", "name": "reflect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "bind", "schema", "views", "only", "extend_existing", "autoload_replace", "resolve_fks", "dialect_kwargs"], "arg_types": ["sqlalchemy.sql.schema.MetaData", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reflect of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.MetaData.reflect", "name": "reflect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "bind", "schema", "views", "only", "extend_existing", "autoload_replace", "resolve_fks", "dialect_kwargs"], "arg_types": ["sqlalchemy.sql.schema.MetaData", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reflect of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "bind", "schema", "views", "only", "extend_existing", "autoload_replace", "resolve_fks", "dialect_kwargs"], "arg_types": ["sqlalchemy.sql.schema.MetaData", "sqlalchemy.engine.base.Engine", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reflect of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "bind", "schema", "views", "only", "extend_existing", "autoload_replace", "resolve_fks", "dialect_kwargs"], "arg_types": ["sqlalchemy.sql.schema.MetaData", "sqlalchemy.engine.base.Connection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reflect of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.MetaData.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "table"], "arg_types": ["sqlalchemy.sql.schema.MetaData", "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove of MetaData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sorted_tables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.MetaData.sorted_tables", "name": "sorted_tables", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sorted_tables of MetaData", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.Table"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.MetaData.sorted_tables", "name": "sorted_tables", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sorted_tables of MetaData", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.Table"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.MetaData.tables", "name": "tables", "type": {".class": "Instance", "args": ["builtins.str", "sqlalchemy.sql.schema.Table"], "extra_attrs": null, "type_ref": "sqlalchemy.util._collections.FacadeDict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.MetaData.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.MetaData", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MockConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.mock.MockConnection", "kind": "Gdef"}, "NULL_UNSPECIFIED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.NULL_UNSPECIFIED", "name": "NULL_UNSPECIFIED", "type": {".class": "LiteralType", "fallback": "sqlalchemy.sql.schema.SchemaConst", "value": "NULL_UNSPECIFIED"}}}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PrimaryKeyConstraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.ColumnCollectionConstraint"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.PrimaryKeyConstraint", "name": "PrimaryKeyConstraint", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.PrimaryKeyConstraint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.PrimaryKeyConstraint", "sqlalchemy.sql.schema.ColumnCollectionConstraint", "sqlalchemy.sql.schema.ColumnCollectionMixin", "sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.schema.HasConditionalDDL", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "columns", "name", "deferrable", "initially", "info", "_implicit_generated", "dialect_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.PrimaryKeyConstraint.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "columns", "name", "deferrable", "initially", "info", "_implicit_generated", "dialect_kw"], "arg_types": ["sqlalchemy.sql.schema.PrimaryKeyConstraint", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._DDLColumnArgument"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PrimaryKeyConstraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.PrimaryKeyConstraint.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_autoincrement_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.PrimaryKeyConstraint._autoincrement_column", "name": "_autoincrement_column", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.PrimaryKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_autoincrement_column of PrimaryKeyConstraint", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.PrimaryKeyConstraint._autoincrement_column", "name": "_autoincrement_column", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.PrimaryKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_autoincrement_column of PrimaryKeyConstraint", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_implicit_generated": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.PrimaryKeyConstraint._implicit_generated", "name": "_implicit_generated", "type": "builtins.bool"}}, "_reload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "columns"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.PrimaryKeyConstraint._reload", "name": "_reload", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "columns"], "arg_types": ["sqlalchemy.sql.schema.PrimaryKeyConstraint", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reload of PrimaryKeyConstraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "col"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.PrimaryKeyConstraint._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "col"], "arg_types": ["sqlalchemy.sql.schema.PrimaryKeyConstraint", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of PrimaryKeyConstraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.PrimaryKeyConstraint._set_parent", "name": "_set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "arg_types": ["sqlalchemy.sql.schema.PrimaryKeyConstraint", "sqlalchemy.sql.base.SchemaEventTarget", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent of PrimaryKeyConstraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "columns_autoinc_first": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.PrimaryKeyConstraint.columns_autoinc_first", "name": "columns_autoinc_first", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.PrimaryKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "columns_autoinc_first of PrimaryKeyConstraint", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.PrimaryKeyConstraint.columns_autoinc_first", "name": "columns_autoinc_first", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.PrimaryKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "columns_autoinc_first of PrimaryKeyConstraint", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.PrimaryKeyConstraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.PrimaryKeyConstraint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "RETAIN_SCHEMA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.RETAIN_SCHEMA", "name": "RETAIN_SCHEMA", "type": {".class": "LiteralType", "fallback": "sqlalchemy.sql.schema.SchemaConst", "value": "RETAIN_SCHEMA"}}}, "ReadOnlyColumnCollection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection", "kind": "Gdef"}, "ScalarElementColumnDefault": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.ColumnDefault"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.ScalarElementColumnDefault", "name": "ScalarElementColumnDefault", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.ScalarElementColumnDefault", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.ScalarElementColumnDefault", "sqlalchemy.sql.schema.ColumnDefault", "sqlalchemy.sql.schema.DefaultGenerator", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "arg", "for_update"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ScalarElementColumnDefault.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "arg", "for_update"], "arg_types": ["sqlalchemy.sql.schema.ScalarElementColumnDefault", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ScalarElementColumnDefault", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.ScalarElementColumnDefault._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.ScalarElementColumnDefault"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of ScalarElementColumnDefault", "ret_type": "sqlalchemy.sql.schema.ScalarElementColumnDefault", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.ScalarElementColumnDefault.has_arg", "name": "has_arg", "type": "builtins.bool"}}, "is_scalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.ScalarElementColumnDefault.is_scalar", "name": "is_scalar", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.ScalarElementColumnDefault.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.ScalarElementColumnDefault", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SchemaConst": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.SchemaConst", "name": "SchemaConst", "type_vars": []}, "deletable_attributes": [], "flags": ["is_enum"], "fullname": "sqlalchemy.sql.schema.SchemaConst", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.SchemaConst", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "BLANK_SCHEMA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.SchemaConst.BLANK_SCHEMA", "name": "BLANK_SCHEMA", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "NULL_UNSPECIFIED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.SchemaConst.NULL_UNSPECIFIED", "name": "NULL_UNSPECIFIED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "RETAIN_SCHEMA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.SchemaConst.RETAIN_SCHEMA", "name": "RETAIN_SCHEMA", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.SchemaConst.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.SchemaConst", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SchemaEventTarget": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.SchemaEventTarget", "kind": "Gdef"}, "SchemaItem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.sql.visitors.Visitable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.SchemaItem", "name": "SchemaItem", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.SchemaItem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.SchemaItem.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.schema.SchemaItem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of SchemaItem", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.SchemaItem.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_init_items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.SchemaItem._init_items", "name": "_init_items", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": ["sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.schema.SchemaItem", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_items of SchemaItem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_schema_item_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema_item"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.SchemaItem._schema_item_copy", "name": "_schema_item_copy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema_item"], "arg_types": ["sqlalchemy.sql.schema.SchemaItem", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._SI", "id": -1, "name": "_SI", "namespace": "sqlalchemy.sql.schema.SchemaItem._schema_item_copy", "upper_bound": "sqlalchemy.sql.schema.SchemaItem", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_schema_item_copy of SchemaItem", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._SI", "id": -1, "name": "_SI", "namespace": "sqlalchemy.sql.schema.SchemaItem._schema_item_copy", "upper_bound": "sqlalchemy.sql.schema.SchemaItem", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._SI", "id": -1, "name": "_SI", "namespace": "sqlalchemy.sql.schema.SchemaItem._schema_item_copy", "upper_bound": "sqlalchemy.sql.schema.SchemaItem", "values": [], "variance": 0}]}}}, "_use_schema_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.SchemaItem._use_schema_map", "name": "_use_schema_map", "type": "builtins.bool"}}, "create_drop_stringify_dialect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.SchemaItem.create_drop_stringify_dialect", "name": "create_drop_stringify_dialect", "type": "builtins.str"}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.SchemaItem.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.SchemaItem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of SchemaItem", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.SchemaItem.info", "name": "info", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.SchemaItem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.SchemaItem", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.HasSchemaAttr", "sqlalchemy.sql.schema.IdentityOptions", "sqlalchemy.sql.schema.DefaultGenerator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.Sequence", "name": "Sequence", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.Sequence", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.Sequence", "sqlalchemy.sql.schema.HasSchemaAttr", "sqlalchemy.sql.schema.IdentityOptions", "sqlalchemy.sql.schema.DefaultGenerator", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "start", "increment", "minvalue", "maxvalue", "nominvalue", "nomaxvalue", "cycle", "schema", "cache", "order", "data_type", "optional", "quote", "metadata", "quote_schema", "for_update"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Sequence.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "start", "increment", "minvalue", "maxvalue", "nominvalue", "nomaxvalue", "cycle", "schema", "cache", "order", "data_type", "optional", "quote", "metadata", "quote_schema", "for_update"], "arg_types": ["sqlalchemy.sql.schema.Sequence", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "sqlalchemy.sql.schema.SchemaConst", "value": "BLANK_SCHEMA"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.int"], "type_ref": "sqlalchemy.sql._typing._TypeEngineArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Sequence", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.Sequence.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Sequence._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Sequence"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of Sequence", "ret_type": "sqlalchemy.sql.schema.Sequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Sequence._key", "name": "_key", "type": "builtins.str"}}, "_not_a_column_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Sequence._not_a_column_expr", "name": "_not_a_column_expr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Sequence"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_not_a_column_expr of Sequence", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metadata"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Sequence._set_metadata", "name": "_set_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "metadata"], "arg_types": ["sqlalchemy.sql.schema.Sequence", "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_metadata of Sequence", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Sequence._set_parent", "name": "_set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "arg_types": ["sqlalchemy.sql.schema.Sequence", "sqlalchemy.sql.base.SchemaEventTarget", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent of Sequence", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "column", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Sequence._set_table", "name": "_set_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "column", "table"], "arg_types": ["sqlalchemy.sql.schema.Sequence", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_table of Sequence", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Sequence.column", "name": "column", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "bind", "checkfirst"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Sequence.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "bind", "checkfirst"], "arg_types": ["sqlalchemy.sql.schema.Sequence", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._CreateDropBind"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of Sequence", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "data_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Sequence.data_type", "name": "data_type", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "drop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "bind", "checkfirst"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Sequence.drop", "name": "drop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "bind", "checkfirst"], "arg_types": ["sqlalchemy.sql.schema.Sequence", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._CreateDropBind"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop of Sequence", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.Sequence.is_sequence", "name": "is_sequence", "type": "builtins.bool"}}, "metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Sequence.metadata", "name": "metadata", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Sequence.name", "name": "name", "type": "sqlalchemy.sql.elements.quoted_name"}}, "next_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.Sequence.next_value", "name": "next_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Sequence"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next_value of Sequence", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.functions.Function"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Sequence.next_value", "name": "next_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Sequence"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next_value of Sequence", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.functions.Function"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "optional": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Sequence.optional", "name": "optional", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Sequence.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.Sequence", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.schema.HasSchemaAttr", "sqlalchemy.sql.selectable.TableClause", {".class": "Instance", "args": ["sqlalchemy.sql.schema.Table"], "extra_attrs": null, "type_ref": "sqlalchemy.inspection.Inspectable"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.Table", "name": "Table", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.Table", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.Table", "sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.schema.HasSchemaAttr", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.selectable.TableClause", "sqlalchemy.sql.roles.DMLTableRole", "sqlalchemy.sql.base.Immutable", "sqlalchemy.sql.selectable.NamedFromClause", "sqlalchemy.sql.selectable.FromClause", "sqlalchemy.sql.roles.AnonymizedFromClauseRole", "sqlalchemy.sql.roles.StrictFromClauseRole", "sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.selectable.Selectable", "sqlalchemy.sql.selectable.ReturnsRows", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "sqlalchemy.inspection.Inspectable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "name", "metadata", "args", "schema", "quote", "quote_schema", "autoload_with", "autoload_replace", "keep_existing", "extend_existing", "resolve_fks", "include_columns", "implicit_returning", "comment", "info", "listeners", "prefixes", "_extend_on", "_no_init", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "name", "metadata", "args", "schema", "quote", "quote_schema", "autoload_with", "autoload_replace", "keep_existing", "extend_existing", "resolve_fks", "include_columns", "implicit_returning", "comment", "info", "listeners", "prefixes", "_extend_on", "_no_init", "kw"], "arg_types": ["sqlalchemy.sql.schema.Table", "builtins.str", "sqlalchemy.sql.schema.MetaData", "sqlalchemy.sql.schema.SchemaItem", {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "sqlalchemy.sql.schema.SchemaConst", "value": "BLANK_SCHEMA"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.sql.schema.Table"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Table", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Table", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of Table", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.Table.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_autoincrement_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.Table._autoincrement_column", "name": "_autoincrement_column", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_autoincrement_column of Table", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Table._autoincrement_column", "name": "_autoincrement_column", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_autoincrement_column of Table", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_autoload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "metadata", "autoload_with", "include_columns", "exclude_columns", "resolve_fks", "_extend_on", "_reflect_info"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table._autoload", "name": "_autoload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "metadata", "autoload_with", "include_columns", "exclude_columns", "resolve_fks", "_extend_on", "_reflect_info"], "arg_types": ["sqlalchemy.sql.schema.Table", "sqlalchemy.sql.schema.MetaData", {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.sql.schema.Table"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.engine.reflection._ReflectionInfo", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_autoload of Table", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Table._columns", "name": "_columns", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.base.DedupeColumnCollection"}}}, "_extra_dependencies": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Table._extra_dependencies", "name": "_extra_dependencies", "type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.Table"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_extra_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table._extra_kwargs", "name": "_extra_kwargs", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["sqlalchemy.sql.schema.Table", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extra_kwargs of Table", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_gen_cache_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "anon_map", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table._gen_cache_key", "name": "_gen_cache_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "anon_map", "<PERSON><PERSON><PERSON>"], "arg_types": ["sqlalchemy.sql.schema.Table", "sqlalchemy.sql._py_util.cache_anon_map", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_gen_cache_key of Table", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_collections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table._init_collections", "name": "_init_collections", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_collections of Table", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_existing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table._init_existing", "name": "_init_existing", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["sqlalchemy.sql.schema.Table", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_existing of Table", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_new": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.sql.schema.Table._new", "name": "_new", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.Table"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_new of Table", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Table._new", "name": "_new", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.Table"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_new of Table", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_prefixes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Table._prefixes", "name": "_prefixes", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "_reset_exported": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table._reset_exported", "name": "_reset_exported", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reset_exported of Table", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sentinel_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Table._sentinel_column", "name": "_sentinel_column", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_sentinel_column_characteristics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.Table._sentinel_column_characteristics", "name": "_sentinel_column_characteristics", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sentinel_column_characteristics of Table", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.base._SentinelColumnCharacterization"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Table._sentinel_column_characteristics", "name": "_sentinel_column_characteristics", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sentinel_column_characteristics of Table", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.base._SentinelColumnCharacterization"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table._set_parent", "name": "_set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "arg_types": ["sqlalchemy.sql.schema.Table", "sqlalchemy.sql.base.SchemaEventTarget", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent of Table", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sorted_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.Table._sorted_constraints", "name": "_sorted_constraints", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sorted_constraints of Table", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.Constraint"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Table._sorted_constraints", "name": "_sorted_constraints", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sorted_constraints of Table", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.Constraint"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "add_is_dependent_on": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table.add_is_dependent_on", "name": "add_is_dependent_on", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "table"], "arg_types": ["sqlalchemy.sql.schema.Table", "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_is_dependent_on of Table", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "column", "replace_existing"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table.append_column", "name": "append_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "column", "replace_existing"], "arg_types": ["sqlalchemy.sql.schema.Table", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnClause"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append_column of Table", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "constraint"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table.append_constraint", "name": "append_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "constraint"], "arg_types": ["sqlalchemy.sql.schema.Table", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Index", "sqlalchemy.sql.schema.Constraint"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append_constraint of Table", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "autoincrement_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.Table.autoincrement_column", "name": "autoincrement_column", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "autoincrement_column of Table", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Table.autoincrement_column", "name": "autoincrement_column", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "autoincrement_column of Table", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "c": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.sql.schema.Table.c", "name": "c", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "c of Table", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Table.c", "name": "c", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "c of Table", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.sql.schema.Table.columns", "name": "columns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "columns of Table", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Table.columns", "name": "columns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "columns of Table", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Table.comment", "name": "comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Table.constraints", "name": "constraints", "type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.Constraint"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "bind", "checkfirst"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "bind", "checkfirst"], "arg_types": ["sqlalchemy.sql.schema.Table", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._CreateDropBind"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of Table", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "drop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "bind", "checkfirst"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table.drop", "name": "drop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "bind", "checkfirst"], "arg_types": ["sqlalchemy.sql.schema.Table", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._CreateDropBind"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop of Table", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exported_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.sql.schema.Table.exported_columns", "name": "exported_columns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exported_columns of Table", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Table.exported_columns", "name": "exported_columns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exported_columns of Table", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "foreign_key_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.Table.foreign_key_constraints", "name": "foreign_key_constraints", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "foreign_key_constraints of Table", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.ForeignKeyConstraint"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Table.foreign_key_constraints", "name": "foreign_key_constraints", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "foreign_key_constraints of Table", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.ForeignKeyConstraint"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "foreign_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.sql.schema.Table.foreign_keys", "name": "foreign_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "foreign_keys of Table", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.ForeignKey"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Table.foreign_keys", "name": "foreign_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "foreign_keys of Table", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.ForeignKey"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "indexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.schema.Table.indexes", "name": "indexes", "type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.Index"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.schema.Table.key", "name": "key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key of Table", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Table.key", "name": "key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key of Table", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.schema.Table.metadata", "name": "metadata", "type": "sqlalchemy.sql.schema.MetaData"}}, "primary_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.sql.schema.Table.primary_key", "name": "primary_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "primary_key of Table", "ret_type": "sqlalchemy.sql.schema.PrimaryKeyConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Table.primary_key", "name": "primary_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "primary_key of Table", "ret_type": "sqlalchemy.sql.schema.PrimaryKeyConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "to_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "metadata", "schema", "referred_schema_fn", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.Table.to_metadata", "name": "to_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "metadata", "schema", "referred_schema_fn", "name"], "arg_types": ["sqlalchemy.sql.schema.Table", "sqlalchemy.sql.schema.MetaData", {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "sqlalchemy.sql.schema.SchemaConst", "value": "RETAIN_SCHEMA"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["sqlalchemy.sql.schema.Table", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "sqlalchemy.sql.schema.ForeignKeyConstraint", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_metadata of Table", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tometadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "metadata", "schema", "referred_schema_fn", "name"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.schema.Table.tometadata", "name": "to<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "metadata", "schema", "referred_schema_fn", "name"], "arg_types": ["sqlalchemy.sql.schema.Table", "sqlalchemy.sql.schema.MetaData", {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "sqlalchemy.sql.schema.SchemaConst", "value": "RETAIN_SCHEMA"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["sqlalchemy.sql.schema.Table", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "sqlalchemy.sql.schema.ForeignKeyConstraint", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to<PERSON><PERSON>ta of Table", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.schema.Table.tometadata", "name": "to<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "metadata", "schema", "referred_schema_fn", "name"], "arg_types": ["sqlalchemy.sql.schema.Table", "sqlalchemy.sql.schema.MetaData", {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "sqlalchemy.sql.schema.SchemaConst", "value": "RETAIN_SCHEMA"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["sqlalchemy.sql.schema.Table", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "sqlalchemy.sql.schema.ForeignKeyConstraint", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to<PERSON><PERSON>ta of Table", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.Table.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.Table", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TableClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TableClause", "kind": "Gdef"}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing.TypeGuard", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypedDict", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UniqueConstraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.ColumnCollectionConstraint"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema.UniqueConstraint", "name": "UniqueConstraint", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema.UniqueConstraint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema.UniqueConstraint", "sqlalchemy.sql.schema.ColumnCollectionConstraint", "sqlalchemy.sql.schema.ColumnCollectionMixin", "sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.base.DialectKWArgs", "sqlalchemy.sql.schema.HasConditionalDDL", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema.UniqueConstraint.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema.UniqueConstraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema.UniqueConstraint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AutoIncrementType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._AutoIncrementType", "kind": "Gdef"}, "_CallableColumnDefaultProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema._CallableColumnDefaultProtocol", "name": "_CallableColumnDefaultProtocol", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.sql.schema._CallableColumnDefaultProtocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema._CallableColumnDefaultProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.sql.schema._CallableColumnDefaultProtocol.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["sqlalchemy.sql.schema._CallableColumnDefaultProtocol", "sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _CallableColumnDefaultProtocol", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._CallableColumnDefaultProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema._CallableColumnDefaultProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ConstraintNameArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql.schema._ConstraintNameArgument", "line": 120, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.base._NoneName", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_CoreMultiExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams", "kind": "Gdef"}, "_CreateDropBind": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql.schema._CreateDropBind", "line": 118, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection", "sqlalchemy.engine.mock.MockConnection"], "uses_pep604_syntax": false}}}, "_DDLColumnArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._DDLColumnArgument", "kind": "Gdef"}, "_DefaultDescriptionTuple": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._DefaultDescriptionTuple", "kind": "Gdef"}, "_InfoType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._InfoType", "kind": "Gdef"}, "_InsertSentinelColumnDefault": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.schema.ColumnDefault"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema._InsertSentinelColumnDefault", "name": "_InsertSentinelColumnDefault", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema._InsertSentinelColumnDefault", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema._InsertSentinelColumnDefault", "sqlalchemy.sql.schema.ColumnDefault", "sqlalchemy.sql.schema.DefaultGenerator", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.schema.SchemaItem", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.visitors.Visitable", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema._InsertSentinelColumnDefault.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema._InsertSentinelColumnDefault"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _InsertSentinelColumnDefault", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "sqlalchemy.sql.schema._InsertSentinelColumnDefault.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.schema._InsertSentinelColumnDefault"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _InsertSentinelColumnDefault", "ret_type": "sqlalchemy.sql.schema._InsertSentinelColumnDefault", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema._InsertSentinelColumnDefault._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.schema._InsertSentinelColumnDefault"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy of _InsertSentinelColumnDefault", "ret_type": "sqlalchemy.sql.schema._InsertSentinelColumnDefault", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema._InsertSentinelColumnDefault._set_parent", "name": "_set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "parent", "kw"], "arg_types": ["sqlalchemy.sql.schema._InsertSentinelColumnDefault", "sqlalchemy.sql.base.SchemaEventTarget", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent of _InsertSentinelColumnDefault", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema._InsertSentinelColumnDefault.arg", "name": "arg", "type": {".class": "NoneType"}}}, "for_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema._InsertSentinelColumnDefault.for_update", "name": "for_update", "type": "builtins.bool"}}, "is_sentinel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.schema._InsertSentinelColumnDefault.is_sentinel", "name": "is_sentinel", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._InsertSentinelColumnDefault.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.schema._InsertSentinelColumnDefault", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_NamingSchemaCallable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql.schema._NamingSchemaCallable", "line": 5304, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_NamingSchemaDirective": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql.schema._NamingSchemaDirective", "line": 5305, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaCallable"}], "uses_pep604_syntax": false}}}, "_NamingSchemaParameter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql.schema._NamingSchemaParameter", "line": 5316, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaTD"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaDirective"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}}}, "_NamingSchemaTD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.schema._NamingSchemaTD", "name": "_NamingSchemaTD", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.schema._NamingSchemaTD", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.schema", "mro": ["sqlalchemy.sql.schema._NamingSchemaTD", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["fk", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaDirective"}], ["pk", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaDirective"}], ["ix", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaDirective"}], ["ck", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaDirective"}], ["uq", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._NamingSchemaDirective"}]], "readonly_keys": [], "required_keys": []}}}, "_NoArg": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._NoArg", "kind": "Gdef"}, "_NoneName": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._NoneName", "kind": "Gdef"}, "_ReflectionInfo": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection._ReflectionInfo", "kind": "Gdef"}, "_SI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._SI", "name": "_SI", "upper_bound": "sqlalchemy.sql.schema.SchemaItem", "values": [], "variance": 0}}, "_SQLExprDefault": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql.schema._SQLExprDefault", "line": 3522, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.elements.TextClause"], "uses_pep604_syntax": false}}}, "_SentinelColumnCharacterization": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._SentinelColumnCharacterization", "kind": "Gdef"}, "_SentinelDefaultCharacterization": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._SentinelDefaultCharacterization", "kind": "Gdef"}, "_ServerDefaultArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql.schema._ServerDefaultArgument", "line": 122, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.FetchedValue", "builtins.str", "sqlalchemy.sql.elements.TextClause", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "uses_pep604_syntax": false}}}, "_ServerOnUpdateArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql.schema._ServerOnUpdateArgument", "line": 126, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.schema._ServerDefaultArgument"}}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_TAB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._TAB", "name": "_TAB", "upper_bound": "sqlalchemy.sql.schema.Table", "values": [], "variance": 0}}, "_TextCoercedExpressionArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._TextCoercedExpressionArgument", "kind": "Gdef"}, "_TypeEngineArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._TypeEngineArgument", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.schema.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.schema.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.schema.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.schema.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.schema.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.schema.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_copy_expression": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["expression", "source_table", "target_table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema._copy_expression", "name": "_copy_expression", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["expression", "source_table", "target_table"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy_expression", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_document_text_coercion": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.coercions._document_text_coercion", "kind": "Gdef"}, "_get_table_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["name", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema._get_table_key", "name": "_get_table_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["name", "schema"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_table_key", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_typing_Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "anon_map": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._py_util.cache_anon_map", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "coercions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.coercions", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "ddl": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl", "kind": "Gdef"}, "default_is_clause_element": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql.schema.default_is_clause_element", "name": "default_is_clause_element", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.sql.schema.DefaultGenerator", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_is_clause_element", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.schema.ColumnElementColumnDefault", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_is_scalar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql.schema.default_is_scalar", "name": "default_is_scalar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.sql.schema.DefaultGenerator", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_is_scalar", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.schema.ScalarElementColumnDefault", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_is_sequence": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql.schema.default_is_sequence", "name": "default_is_sequence", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.sql.schema.DefaultGenerator", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_is_sequence", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.sql.schema.Sequence", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "event": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "insert_sentinel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 5, 5], "arg_names": ["name", "type_", "default", "omit_from_statements"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.schema.insert_sentinel", "name": "insert_sentinel", "type": {".class": "CallableType", "arg_kinds": [1, 1, 5, 5], "arg_names": ["name", "type_", "default", "omit_from_statements"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql.schema.insert_sentinel", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypeEngineArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert_sentinel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.schema._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql.schema.insert_sentinel", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "inspection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "quoted_name": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.quoted_name", "kind": "Gdef"}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef"}, "to_instance": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.to_instance", "kind": "Gdef"}, "type_api": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "visitors": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/sql/schema.py"}