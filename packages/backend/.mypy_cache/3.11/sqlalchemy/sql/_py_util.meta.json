{"data_mtime": 1751259990, "dep_lines": [16, 19, 8, 10, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util.typing", "sqlalchemy.sql.cache_key", "__future__", "typing", "builtins", "abc", "enum", "importlib", "importlib.machinery"], "hash": "11c0319fb428500ac67e04580dc83a65cc3bf36e", "id": "sqlalchemy.sql._py_util", "ignore_all": true, "interface_hash": "9a0871c69bea5c44c2568243f78dd78fc4b564e5", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/sql/_py_util.py", "plugin_data": null, "size": 2173, "suppressed": [], "version_id": "1.13.0"}