{"data_mtime": 1751259990, "dep_lines": [35, 36, 37, 41, 45, 35, 40, 18, 20, 40, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 25, 20, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.operators", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.sql.base", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.sql._py_util", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "8e91d8b61457d3a6fba5311839eab4e73d6db2d6", "id": "sqlalchemy.sql.annotation", "ignore_all": true, "interface_hash": "104ba7d63026fa80c329a1a03261ec12d00e8130", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/sql/annotation.py", "plugin_data": null, "size": 18245, "suppressed": [], "version_id": "1.13.0"}