{".class": "MypyFile", "_fullname": "sqlalchemy.sql.expression", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Alias": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.<PERSON><PERSON>", "kind": "Gdef"}, "AliasedReturnsRows": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.AliasedReturnsRows", "kind": "Gdef"}, "BinaryExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BinaryExpression", "kind": "Gdef"}, "BindParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BindParameter", "kind": "Gdef"}, "BooleanClauseList": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BooleanClauseList", "kind": "Gdef"}, "CTE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.CTE", "kind": "Gdef"}, "CacheKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.cache_key.CacheKey", "kind": "Gdef"}, "Case": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Case", "kind": "Gdef"}, "Cast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Cast", "kind": "Gdef"}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef"}, "ClauseList": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseList", "kind": "Gdef"}, "CollectionAggregate": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.CollectionAggregate", "kind": "Gdef"}, "ColumnClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnClause", "kind": "Gdef"}, "ColumnCollection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.ColumnCollection", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "ColumnExpressionArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing.ColumnExpressionArgument", "kind": "Gdef"}, "ColumnOperators": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators.ColumnOperators", "kind": "Gdef"}, "CompoundSelect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.CompoundSelect", "kind": "Gdef"}, "Delete": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.Delete", "kind": "Gdef"}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef"}, "Exists": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Exists", "kind": "Gdef"}, "ExpressionClauseList": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ExpressionClauseList", "kind": "Gdef"}, "Extract": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Extract", "kind": "Gdef"}, "False_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.False_", "kind": "Gdef"}, "FromClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.FromClause", "kind": "Gdef"}, "FromGrouping": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.FromGrouping", "kind": "Gdef"}, "Function": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.Function", "kind": "Gdef"}, "FunctionElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.FunctionElement", "kind": "Gdef"}, "FunctionFilter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.FunctionFilter", "kind": "Gdef"}, "GenerativeSelect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.GenerativeSelect", "kind": "Gdef"}, "Grouping": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Grouping", "kind": "Gdef"}, "HasCTE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.HasCTE", "kind": "Gdef"}, "HasPrefixes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.HasPrefixes", "kind": "Gdef"}, "HasSuffixes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.HasSuffixes", "kind": "Gdef"}, "Insert": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.Insert", "kind": "Gdef"}, "Join": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Join", "kind": "Gdef"}, "LABEL_STYLE_DEFAULT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_DEFAULT", "kind": "Gdef"}, "LABEL_STYLE_DISAMBIGUATE_ONLY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_DISAMBIGUATE_ONLY", "kind": "Gdef"}, "LABEL_STYLE_NONE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_NONE", "kind": "Gdef"}, "LABEL_STYLE_TABLENAME_PLUS_COL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_TABLENAME_PLUS_COL", "kind": "Gdef"}, "Label": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Label", "kind": "Gdef"}, "LambdaElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.lambdas.LambdaElement", "kind": "Gdef"}, "Lateral": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Lateral", "kind": "Gdef"}, "Null": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Null", "kind": "Gdef"}, "Operators": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators.Operators", "kind": "Gdef"}, "Over": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Over", "kind": "Gdef"}, "ReleaseSavepointClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ReleaseSavepointClause", "kind": "Gdef"}, "ReturnsRows": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.ReturnsRows", "kind": "Gdef"}, "RollbackToSavepointClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.RollbackToSavepointClause", "kind": "Gdef"}, "SQLColumnExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.SQLColumnExpression", "kind": "Gdef"}, "SavepointClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.SavepointClause", "kind": "Gdef"}, "ScalarSelect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.ScalarSelect", "kind": "Gdef"}, "ScalarValues": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.ScalarValues", "kind": "Gdef"}, "Select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Select", "kind": "Gdef"}, "SelectBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.SelectBase", "kind": "Gdef"}, "SelectLabelStyle": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.SelectLabelStyle", "kind": "Gdef"}, "Selectable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Selectable", "kind": "Gdef"}, "StatementLambdaElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.lambdas.StatementLambdaElement", "kind": "Gdef"}, "Subquery": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Subquery", "kind": "Gdef"}, "TableClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TableClause", "kind": "Gdef"}, "TableSample": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TableSample", "kind": "Gdef"}, "TableValuedAlias": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TableValuedAlias", "kind": "Gdef"}, "TextAsFrom": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TextAsFrom", "kind": "Gdef"}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef"}, "TextualSelect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TextualSelect", "kind": "Gdef"}, "True_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.True_", "kind": "Gdef"}, "TryCast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TryCast", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.<PERSON>", "kind": "Gdef"}, "TypeClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TypeClause", "kind": "Gdef"}, "TypeCoerce": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TypeCoerce", "kind": "Gdef"}, "UnaryExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.UnaryExpression", "kind": "Gdef"}, "Update": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.Update", "kind": "Gdef"}, "UpdateBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.UpdateBase", "kind": "Gdef"}, "Values": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Values", "kind": "Gdef"}, "ValuesBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.ValuesBase", "kind": "Gdef"}, "Visitable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors.Visitable", "kind": "Gdef"}, "WithinGroup": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.WithinGroup", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.expression.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.expression.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.expression.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.expression.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.expression.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.expression.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_from_objects": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._from_objects", "kind": "Gdef"}, "_select_iterables": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._select_iterables", "kind": "Gdef"}, "_truncated_label": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements._truncated_label", "kind": "Gdef"}, "alias": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.alias", "kind": "Gdef"}, "all_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.all_", "kind": "Gdef"}, "and_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.and_", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "any_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.any_", "kind": "Gdef"}, "asc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.asc", "kind": "Gdef"}, "between": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.between", "kind": "Gdef"}, "bindparam": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.bindparam", "kind": "Gdef"}, "bitwise_not": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.bitwise_not", "kind": "Gdef"}, "case": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.case", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.cast", "kind": "Gdef"}, "collate": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.collate", "kind": "Gdef"}, "column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.column", "kind": "Gdef"}, "cte": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.cte", "kind": "Gdef"}, "custom_op": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators.custom_op", "kind": "Gdef"}, "delete": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._dml_constructors.delete", "kind": "Gdef"}, "desc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.desc", "kind": "Gdef"}, "distinct": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.distinct", "kind": "Gdef"}, "except_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.except_", "kind": "Gdef"}, "except_all": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.except_all", "kind": "Gdef"}, "exists": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.exists", "kind": "Gdef"}, "extract": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.extract", "kind": "Gdef"}, "false": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.false", "kind": "Gdef"}, "func": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.func", "kind": "Gdef"}, "funcfilter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.funcfilter", "kind": "Gdef"}, "insert": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._dml_constructors.insert", "kind": "Gdef"}, "intersect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.intersect", "kind": "Gdef"}, "intersect_all": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.intersect_all", "kind": "Gdef"}, "join": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.join", "kind": "Gdef"}, "label": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.label", "kind": "Gdef"}, "lambda_stmt": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.lambdas.lambda_stmt", "kind": "Gdef"}, "lateral": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.lateral", "kind": "Gdef"}, "literal": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.literal", "kind": "Gdef"}, "literal_column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.literal_column", "kind": "Gdef"}, "modifier": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.modifier", "kind": "Gdef"}, "not_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.not_", "kind": "Gdef"}, "null": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.null", "kind": "Gdef"}, "nulls_first": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.nulls_first", "kind": "Gdef"}, "nulls_last": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.nulls_last", "kind": "Gdef"}, "nullsfirst": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.expression.nullsfirst", "name": "nullsfirst", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["column"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.nulls_first", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.nulls_first", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.UnaryExpression"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.nulls_first", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "nullslast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.expression.nullslast", "name": "nullslast", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["column"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.nulls_last", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.nulls_last", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.UnaryExpression"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.nulls_last", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "or_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.or_", "kind": "Gdef"}, "outerjoin": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.outerjoin", "kind": "Gdef"}, "outparam": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.outparam", "kind": "Gdef"}, "over": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.over", "kind": "Gdef"}, "quoted_name": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.quoted_name", "kind": "Gdef"}, "select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.select", "kind": "Gdef"}, "table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.table", "kind": "Gdef"}, "tablesample": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.tablesample", "kind": "Gdef"}, "text": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.text", "kind": "Gdef"}, "true": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.true", "kind": "Gdef"}, "try_cast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.try_cast", "kind": "Gdef"}, "tuple_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.tuple_", "kind": "Gdef"}, "type_coerce": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.type_coerce", "kind": "Gdef"}, "union": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.union", "kind": "Gdef"}, "union_all": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.union_all", "kind": "Gdef"}, "update": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._dml_constructors.update", "kind": "Gdef"}, "values": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.values", "kind": "Gdef"}, "within_group": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.within_group", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/sql/expression.py"}