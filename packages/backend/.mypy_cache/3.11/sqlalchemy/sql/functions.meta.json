{"data_mtime": 1751259990, "dep_lines": [31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 45, 58, 63, 79, 80, 81, 83, 31, 64, 13, 15, 16, 17, 64, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 10, 5, 5, 5, 5, 5, 25, 25, 25, 25, 20, 10, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.annotation", "sqlalchemy.sql.coercions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "sqlalchemy.sql.util", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.sql.visitors", "sqlalchemy.engine.base", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.util.typing", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "datetime", "decimal", "typing", "sqlalchemy", "builtins", "_decimal", "_typeshed", "abc", "collections", "enum", "importlib", "importlib.machinery", "sqlalchemy.engine", "sqlalchemy.engine.result", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.traversals", "sqlalchemy.util.langhelpers", "types"], "hash": "dc6090d903294f72ef634ec4398b62988455845a", "id": "sqlalchemy.sql.functions", "ignore_all": true, "interface_hash": "87da8ebb0f90c5ac8afd84be5b9e335fab0d7c74", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/sql/functions.py", "plugin_data": null, "size": 63762, "suppressed": [], "version_id": "1.13.0"}