{"data_mtime": 1751259990, "dep_lines": [35, 36, 37, 38, 39, 40, 43, 54, 59, 60, 62, 67, 70, 75, 14, 35, 55, 56, 57, 58, 59, 12, 14, 15, 16, 17, 18, 19, 20, 33, 55, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 5, 5, 5, 5, 10, 10, 5, 25, 25, 25, 10, 20, 10, 10, 10, 5, 20, 5, 20, 10, 10, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.coercions", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.type_api", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.visitors", "sqlalchemy.engine.processors", "sqlalchemy.util.langhelpers", "sqlalchemy.util.typing", "sqlalchemy.sql._typing", "sqlalchemy.sql.schema", "sqlalchemy.engine.interfaces", "collections.abc", "sqlalchemy.sql", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "collections", "datetime", "decimal", "enum", "json", "pickle", "typing", "uuid", "sqlalchemy", "builtins", "_decimal", "_typeshed", "abc", "importlib", "importlib.machinery", "sqlalchemy.event.api", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "3da3636f18a2f17f12e84c5c9a233df87ca4c0ab", "id": "sqlalchemy.sql.sqltypes", "ignore_all": true, "interface_hash": "4269a4a8750d5689a6bddff6a8ef4bcf3e2850bf", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/sql/sqltypes.py", "plugin_data": null, "size": 127469, "suppressed": [], "version_id": "1.13.0"}