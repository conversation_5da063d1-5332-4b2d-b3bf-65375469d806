{"data_mtime": 1751259990, "dep_lines": [57, 58, 64, 65, 70, 57, 62, 63, 31, 33, 34, 35, 36, 37, 55, 62, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 25, 20, 10, 10, 5, 10, 5, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.event.legacy", "sqlalchemy.event.registry", "sqlalchemy.util.concurrency", "sqlalchemy.util.typing", "sqlalchemy.event.base", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "itertools", "threading", "types", "typing", "weakref", "sqlalchemy", "builtins", "_typeshed", "_weakref", "abc", "importlib", "importlib.machinery", "sqlalchemy.util.compat", "sqlalchemy.util.langhelpers"], "hash": "579393a28d32ac190de4420f2838bd151930505b", "id": "sqlalchemy.event.attr", "ignore_all": true, "interface_hash": "0458f042665714069cf97b45a1da6d42a0fc367a", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/event/attr.py", "plugin_data": null, "size": 20751, "suppressed": [], "version_id": "1.13.0"}