{".class": "MypyFile", "_fullname": "sqlalchemy.event", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CANCEL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.api.CANCEL", "kind": "Gdef"}, "EventTarget": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry.EventTarget", "kind": "Gdef"}, "Events": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.base.Events", "kind": "Gdef"}, "NO_RETVAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.api.NO_RETVAL", "kind": "Gdef"}, "RefCollection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.attr.RefCollection", "kind": "Gdef"}, "_Dispatch": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.base._Dispatch", "kind": "Gdef"}, "_DispatchCommon": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.base._DispatchCommon", "kind": "Gdef"}, "_EventKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry._EventKey", "kind": "Gdef"}, "_InstanceLevelDispatch": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.attr._InstanceLevelDispatch", "kind": "Gdef"}, "_ListenerFnType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry._ListenerFnType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_legacy_signature": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.legacy._legacy_signature", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "contains": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.api.contains", "kind": "Gdef"}, "dispatcher": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.base.dispatcher", "kind": "Gdef"}, "listen": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.api.listen", "kind": "Gdef"}, "listens_for": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.api.listens_for", "kind": "Gdef"}, "remove": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.api.remove", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/event/__init__.py"}