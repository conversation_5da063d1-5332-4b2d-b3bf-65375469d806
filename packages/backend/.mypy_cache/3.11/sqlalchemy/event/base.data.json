{".class": "MypyFile", "_fullname": "sqlalchemy.event.base", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Events": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.base.Events", "name": "Events", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.base.Events", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.event.base", "mro": ["sqlalchemy.event.base.Events", "sqlalchemy.event.base._HasEventsDispatch", "builtins.object"], "names": {".class": "SymbolTable", "_accept_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.event.base.Events._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.Events"}}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of Events", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.event.base.Events._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.Events"}}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of Events", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.event.base.Events._clear", "name": "_clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.Events"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear of Events", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.event.base.Events._clear", "name": "_clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.Events"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear of Events", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "event_key", "propagate", "insert", "named", "asyncio"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.event.base.Events._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "event_key", "propagate", "insert", "named", "asyncio"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.Events"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of Events", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.event.base.Events._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "event_key", "propagate", "insert", "named", "asyncio"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.Events"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of Events", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "event_key"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.event.base.Events._remove", "name": "_remove", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "event_key"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.Events"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove of Events", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.event.base.Events._remove", "name": "_remove", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "event_key"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.Events"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove of Events", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.base.Events.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.Events", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.Events"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_ClsLevelDispatch": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.attr._ClsLevelDispatch", "kind": "Gdef"}, "_Dispatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.base._Dispatch", "name": "_Dispatch", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.base._Dispatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.event.base", "mro": ["sqlalchemy.event.base._Dispatch", "sqlalchemy.event.base._DispatchCommon", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._Dispatch.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of _Dispatch", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "parent", "instance_cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._Dispatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "parent", "instance_cls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _Dispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._Dispatch.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of _Dispatch", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.event.base._Dispatch.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_active_history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.base._Dispatch._active_history", "name": "_active_history", "type": "builtins.bool"}}, "_clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._Dispatch._clear", "name": "_clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear of _Dispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_empty_listener_reg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.event.base._Dispatch._empty_listener_reg", "name": "_empty_listener_reg", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr._EmptyListener"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "_empty_listeners": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.base._Dispatch._empty_listeners", "name": "_empty_listeners", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr._EmptyListener"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_event_descriptors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.event.base._Dispatch._event_descriptors", "name": "_event_descriptors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_event_descriptors of _Dispatch", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.event.base._Dispatch._event_descriptors", "name": "_event_descriptors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_event_descriptors of _Dispatch", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_event_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.base._Dispatch._event_names", "name": "_event_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.base._Dispatch._events", "name": "_events", "type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}}}, "_for_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "instance_cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._Dispatch._for_class", "name": "_for_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "instance_cls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_for_class of _Dispatch", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_for_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._Dispatch._for_instance", "name": "_for_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_for_instance of _Dispatch", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_instance_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.base._Dispatch._instance_cls", "name": "_instance_cls", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._Dispatch._join", "name": "_join", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_join of _Dispatch", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_joined_dispatch_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.base._Dispatch._joined_dispatch_cls", "name": "_joined_dispatch_cls", "type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}}, "_listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "event_key", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._Dispatch._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "event_key", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of _<PERSON>sp<PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.event.base._Dispatch._parent", "name": "_parent", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "only_propagate"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._Dispatch._update", "name": "_update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "only_propagate"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update of _Dispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.base._Dispatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._Dispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "_DispatchCommon": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.base._DispatchCommon", "name": "_DispatchCommon", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._DispatchCommon", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.base._DispatchCommon", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.event.base", "mro": ["sqlalchemy.event.base._DispatchCommon", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._DispatchCommon.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._DispatchCommon", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of _DispatchCommon", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._DispatchCommon", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.event.base._DispatchCommon.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.event.base._DispatchCommon._events", "name": "_events", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._DispatchCommon", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_events of _<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._DispatchCommon", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.event.base._DispatchCommon._events", "name": "_events", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._DispatchCommon", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_events of _<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._DispatchCommon", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_instance_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.base._DispatchCommon._instance_cls", "name": "_instance_cls", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._DispatchCommon", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._DispatchCommon._join", "name": "_join", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._DispatchCommon", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._DispatchCommon", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_join of _DispatchCommon", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._DispatchCommon", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.base._DispatchCommon.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._DispatchCommon", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "_ET": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry._ET", "kind": "Gdef"}, "_EmptyListener": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.attr._EmptyListener", "kind": "Gdef"}, "_EventKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry._EventKey", "kind": "Gdef"}, "_HasEventsDispatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.base._HasEventsDispatch", "name": "_HasEventsDispatch", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.base._HasEventsDispatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.event.base", "mro": ["sqlalchemy.event.base._HasEventsDispatch", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.event.base._HasEventsDispatch.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of _HasEventsDispatch", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init_subclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class"], "fullname": "sqlalchemy.event.base._HasEventsDispatch.__init_subclass__", "name": "__init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init_subclass__ of _HasEventsDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_accept_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.event.base._HasEventsDispatch._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of _HasEventsDispatch", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.event.base._HasEventsDispatch._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of _HasEventsDispatch", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_create_dispatcher_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "classname", "bases", "dict_"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.event.base._HasEventsDispatch._create_dispatcher_class", "name": "_create_dispatcher_class", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "classname", "bases", "dict_"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, "builtins.str", {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_dispatcher_class of _HasEventsDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.event.base._HasEventsDispatch._create_dispatcher_class", "name": "_create_dispatcher_class", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "classname", "bases", "dict_"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, "builtins.str", {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_dispatcher_class of _HasEventsDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_dispatch_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.base._HasEventsDispatch._dispatch_target", "name": "_dispatch_target", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "event_key", "propagate", "insert", "named", "asyncio"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.event.base._HasEventsDispatch._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "event_key", "propagate", "insert", "named", "asyncio"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of _HasEventsDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.event.base._HasEventsDispatch._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "event_key", "propagate", "insert", "named", "asyncio"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of _HasEventsDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_set_dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["klass", "dispatch_cls"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "sqlalchemy.event.base._HasEventsDispatch._set_dispatch", "name": "_set_dispatch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["klass", "dispatch_cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_dispatch of _HasEventsDispatch", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.event.base._HasEventsDispatch._set_dispatch", "name": "_set_dispatch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["klass", "dispatch_cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_dispatch of _HasEventsDispatch", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.base._HasEventsDispatch.dispatch", "name": "dispatch", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.base._HasEventsDispatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._HasEventsDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "_InstanceLevelDispatch": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.attr._InstanceLevelDispatch", "kind": "Gdef"}, "_JoinedDispatcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "_Jo<PERSON><PERSON><PERSON><PERSON><PERSON>er", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.event.base", "mro": ["sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sqlalchemy.event.base._DispatchCommon", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._JoinedDispatcher.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of _<PERSON><PERSON><PERSON><PERSON><PERSON>tcher", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr._JoinedListener"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "local", "parent"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._JoinedDispatcher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "local", "parent"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _<PERSON><PERSON><PERSON><PERSON><PERSON>tcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._JoinedDispatcher.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of _<PERSON><PERSON><PERSON><PERSON><PERSON>tcher", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.event.base._JoinedDispatcher.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.event.base._JoinedDispatcher._events", "name": "_events", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_events of _<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.event.base._JoinedDispatcher._events", "name": "_events", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_events of _<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_instance_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.base._JoinedDispatcher._instance_cls", "name": "_instance_cls", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "event_key", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._Joined<PERSON><PERSON><PERSON>tcher._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "event_key", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of _<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "local": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.base._JoinedDispatcher.local", "name": "local", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.base._JoinedDispatcher.parent", "name": "parent", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.base._JoinedDispatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "values": [], "variance": 0}, "slots": ["_instance_cls", "local", "parent"], "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "_JoinedListener": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.attr._JoinedListener", "kind": "Gdef"}, "_UnpickleDispatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.base._UnpickleDispatch", "name": "_UnpickleDispatch", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.base._UnpickleDispatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.event.base", "mro": ["sqlalchemy.event.base._UnpickleDispatch", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_instance_cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._UnpickleDispatch.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_instance_cls"], "arg_types": ["sqlalchemy.event.base._UnpickleDispatch", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.base._UnpickleDispatch.__call__", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnpickleDispatch", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.base._UnpickleDispatch.__call__", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.base._UnpickleDispatch.__call__", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.base._UnpickleDispatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.event.base._UnpickleDispatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.base.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_is_event_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._is_event_name", "name": "_is_event_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_event_name", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_registrars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.event.base._registrars", "name": "_registrars", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "_remove_dispatcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base._remove_dispatcher", "name": "_remove_dispatcher", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.base._remove_dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_dispatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.base._remove_dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "dispatcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.base.dispatcher", "name": "dispatcher", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.base.dispatcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.event.base", "mro": ["sqlalchemy.event.base.dispatcher", "builtins.object"], "names": {".class": "SymbolTable", "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.event.base.dispatcher.__get__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.event.base.dispatcher.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.dispatcher"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of dispatcher", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.event.base.dispatcher.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.dispatcher"}, {".class": "NoneType"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of dispatcher", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.event.base.dispatcher.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.dispatcher"}, {".class": "NoneType"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of dispatcher", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.event.base.dispatcher.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.dispatcher"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of dispatcher", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.event.base.dispatcher.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.dispatcher"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of dispatcher", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.dispatcher"}, {".class": "NoneType"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of dispatcher", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.dispatcher"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of dispatcher", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "events"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base.dispatcher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "events"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.dispatcher"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of dispatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dispatch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.event.base.dispatcher.dispatch", "name": "dispatch", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._Dispatch"}}}, "events": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.event.base.dispatcher.events", "name": "events", "type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.base.dispatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.dispatcher"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "slots_dispatcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.slots_dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.dispatcher"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.base.slots_dispatcher", "name": "slots_dispatcher", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.slots_dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.base.slots_dispatcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.event.base", "mro": ["sqlalchemy.event.base.slots_dispatcher", "sqlalchemy.event.base.dispatcher", "builtins.object"], "names": {".class": "SymbolTable", "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.base.slots_dispatcher.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.slots_dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.slots_dispatcher"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of slots_dispatcher", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.base.slots_dispatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.base.slots_dispatcher", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.slots_dispatcher"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/event/base.py"}