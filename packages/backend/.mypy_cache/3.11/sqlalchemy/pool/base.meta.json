{"data_mtime": 1751259990, "dep_lines": [37, 41, 47, 33, 34, 35, 36, 13, 15, 16, 17, 18, 19, 20, 31, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 10, 10, 10, 10, 5, 5, 10, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util.typing", "sqlalchemy.engine.interfaces", "sqlalchemy.sql._typing", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.log", "sqlalchemy.util", "__future__", "collections", "dataclasses", "enum", "threading", "time", "typing", "weakref", "sqlalchemy", "builtins", "_thread", "_weakref", "abc", "importlib", "importlib.machinery", "logging", "sqlalchemy.engine", "sqlalchemy.event.api", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "types"], "hash": "986605264678f1747063c28c893f6dcba694521a", "id": "sqlalchemy.pool.base", "ignore_all": true, "interface_hash": "1716aede271c36b44332bf3fb65714c939301185", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/pool/base.py", "plugin_data": null, "size": 52236, "suppressed": [], "version_id": "1.13.0"}