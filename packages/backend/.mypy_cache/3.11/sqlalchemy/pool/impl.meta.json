{"data_mtime": 1751259990, "dep_lines": [27, 38, 39, 42, 35, 36, 12, 14, 15, 16, 25, 35, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 25, 10, 5, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.pool.base", "sqlalchemy.util.queue", "sqlalchemy.util.typing", "sqlalchemy.engine.interfaces", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "threading", "traceback", "typing", "weakref", "sqlalchemy", "builtins", "_thread", "_weakref", "abc", "enum", "importlib", "importlib.machinery", "logging", "re", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.util.langhelpers", "types"], "hash": "0c983282cb0a46fb97fb396e2fb9a65e8607dcc2", "id": "sqlalchemy.pool.impl", "ignore_all": true, "interface_hash": "a28f48e66887d82c8b45b0914149aa9581fdaa5b", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/pool/impl.py", "plugin_data": null, "size": 18944, "suppressed": [], "version_id": "1.13.0"}