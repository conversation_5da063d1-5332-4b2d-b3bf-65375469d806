{"data_mtime": 1751259991, "dep_lines": [532, 533, 532, 555, 558, 562, 563, 568, 569, 570, 551, 552, 553, 554, 555, 572, 525, 527, 528, 530, 550, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 5, 10, 10, 10, 10, 5, 10, 10, 5, 10, 5, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.oracle.dictionary", "sqlalchemy.dialects.oracle.types", "sqlalchemy.dialects.oracle", "sqlalchemy.engine.default", "sqlalchemy.engine.reflection", "sqlalchemy.sql.compiler", "sqlalchemy.sql.expression", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.util", "sqlalchemy.sql.visitors", "sqlalchemy.exc", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.types", "__future__", "collections", "functools", "re", "sqlalchemy", "builtins", "_decimal", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql._selectable_constructors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "types", "typing", "typing_extensions"], "hash": "7cd3129ce40ab5f12e9ef694a8765117f8d0d6b8", "id": "sqlalchemy.dialects.oracle.base", "ignore_all": true, "interface_hash": "2a22241663e8f00c59822f11c756dad939e333a1", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/base.py", "plugin_data": null, "size": 119676, "suppressed": [], "version_id": "1.13.0"}