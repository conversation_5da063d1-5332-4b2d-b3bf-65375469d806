{"data_mtime": 1751259991, "dep_lines": [434, 438, 434, 441, 442, 443, 444, 445, 439, 440, 441, 444, 428, 430, 431, 432, 439, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1104], "dep_prios": [5, 5, 20, 10, 10, 10, 10, 5, 10, 10, 20, 20, 5, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["sqlalchemy.dialects.oracle.base", "sqlalchemy.dialects.oracle.types", "sqlalchemy.dialects.oracle", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.processors", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql._typing", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.sql", "__future__", "decimal", "random", "re", "sqlalchemy", "builtins", "_decimal", "_typeshed", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.engine.default", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.pool", "sqlalchemy.pool.base", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "types", "typing", "typing_extensions", "uuid"], "hash": "54caa5f4a038376fb00841a853fdf8aa666ccb82", "id": "sqlalchemy.dialects.oracle.cx_oracle", "ignore_all": true, "interface_hash": "b00cc4cff90673bea10e0023804d916b9c366422", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/cx_oracle.py", "plugin_data": null, "size": 55235, "suppressed": ["cx_Oracle"], "version_id": "1.13.0"}