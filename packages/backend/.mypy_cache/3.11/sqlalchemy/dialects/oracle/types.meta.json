{"data_mtime": 1751259990, "dep_lines": [16, 21, 22, 15, 16, 17, 8, 10, 11, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 25, 25, 10, 20, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.sqltypes", "sqlalchemy.engine.interfaces", "sqlalchemy.sql.type_api", "sqlalchemy.exc", "sqlalchemy.sql", "sqlalchemy.types", "__future__", "datetime", "typing", "sqlalchemy", "builtins", "abc", "importlib", "importlib.machinery", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.base", "sqlalchemy.sql.visitors"], "hash": "0fffff13f49029eee80eb25e0e24ffc1f1af159c", "id": "sqlalchemy.dialects.oracle.types", "ignore_all": true, "interface_hash": "30c1eace35b234545600255e518cd0c5ff2d933a", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/types.py", "plugin_data": null, "size": 8231, "suppressed": [], "version_id": "1.13.0"}