{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.oracle", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BFILE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.BFILE", "kind": "Gdef"}, "BINARY_DOUBLE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.BINARY_DOUBLE", "kind": "Gdef"}, "BINARY_FLOAT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.BINARY_FLOAT", "kind": "Gdef"}, "BLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BLOB", "kind": "Gdef"}, "CHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.CHAR", "kind": "Gdef"}, "CLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.CLOB", "kind": "Gdef"}, "DATE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.DATE", "kind": "Gdef"}, "DOUBLE_PRECISION": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DOUBLE_PRECISION", "kind": "Gdef"}, "FLOAT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.FLOAT", "kind": "Gdef"}, "INTERVAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.INTERVAL", "kind": "Gdef"}, "LONG": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.LONG", "kind": "Gdef"}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef", "module_public": false}, "NCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NCHAR", "kind": "Gdef"}, "NCLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.NCLOB", "kind": "Gdef"}, "NUMBER": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.NUMBER", "kind": "Gdef"}, "NVARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NVARCHAR", "kind": "Gdef"}, "NVARCHAR2": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.NVARCHAR2", "kind": "Gdef"}, "RAW": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.RAW", "kind": "Gdef"}, "REAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.REAL", "kind": "Gdef"}, "ROWID": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.ROWID", "kind": "Gdef"}, "TIMESTAMP": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.TIMESTAMP", "kind": "Gdef"}, "VARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.VARCHAR", "kind": "Gdef"}, "VARCHAR2": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.VARCHAR2", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "base": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.base", "kind": "Gdef", "module_public": false}, "cx_oracle": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.cx_oracle", "kind": "Gdef", "module_public": false}, "dialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dialect", "name": "dialect", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 4], "arg_names": ["auto_convert_lobs", "coerce_to_decimal", "arraysize", "encoding_errors", "threaded", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["sqlalchemy.dialects.oracle.cx_oracle.OracleDialect_cx_oracle"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.oracle.cx_oracle.OracleDialect_cx_oracle", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "oracledb": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.oracledb", "kind": "Gdef", "module_public": false}, "oracledb_async": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.oracledb_async", "name": "oracledb_async", "type": "builtins.type"}}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/__init__.py"}