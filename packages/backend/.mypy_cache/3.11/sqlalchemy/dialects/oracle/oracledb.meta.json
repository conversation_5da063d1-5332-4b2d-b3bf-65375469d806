{"data_mtime": 1751259991, "dep_lines": [97, 97, 100, 104, 98, 99, 104, 105, 90, 92, 93, 94, 98, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 110], "dep_prios": [10, 20, 5, 10, 10, 10, 20, 5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["sqlalchemy.dialects.oracle.cx_oracle", "sqlalchemy.dialects.oracle", "sqlalchemy.connectors.asyncio", "sqlalchemy.engine.default", "sqlalchemy.exc", "sqlalchemy.pool", "sqlalchemy.engine", "sqlalchemy.util", "__future__", "collections", "re", "typing", "sqlalchemy", "builtins", "_typeshed", "abc", "importlib", "importlib.machinery", "sqlalchemy.connectors", "sqlalchemy.dialects.oracle.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.pool.base", "typing_extensions"], "hash": "a1bb93bc94bb2eedc18528ff02e28f42d4f860c0", "id": "sqlalchemy.dialects.oracle.oracledb", "ignore_all": true, "interface_hash": "67d588d48b845dd987dcffc367573637bc0086ac", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/oracledb.py", "plugin_data": null, "size": 13619, "suppressed": ["oracledb"], "version_id": "1.13.0"}