{"data_mtime": 1751259991, "dep_lines": [492, 493, 495, 496, 492, 487, 498, 499, 485, 487, 488, 489, 490, 498, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 690], "dep_prios": [10, 5, 5, 5, 20, 10, 10, 5, 5, 20, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["sqlalchemy.dialects.postgresql.ranges", "sqlalchemy.dialects.postgresql._psycopg_common", "sqlalchemy.dialects.postgresql.base", "sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql", "collections.abc", "sqlalchemy.types", "sqlalchemy.util", "__future__", "collections", "logging", "re", "typing", "sqlalchemy", "builtins", "_typeshed", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.engine", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql", "sqlalchemy.sql.compiler", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util.langhelpers", "types", "typing_extensions"], "hash": "d45a7ea3e03fe513374b952e2de8656b04173c35", "id": "sqlalchemy.dialects.postgresql.psycopg2", "ignore_all": true, "interface_hash": "e0f5232d016aa1c052b6bd7020a2003c938153d4", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/psycopg2.py", "plugin_data": null, "size": 31998, "suppressed": ["psycopg2"], "version_id": "1.13.0"}