{"data_mtime": 1751259991, "dep_lines": [1467, 1468, 1469, 1470, 1472, 1473, 1480, 1466, 1510, 1511, 1512, 1515, 1519, 1520, 1521, 1522, 1523, 1524, 1525, 1527, 1541, 1505, 1506, 1508, 1509, 1510, 1528, 1453, 1455, 1456, 1457, 1458, 1505, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 10, 10, 10, 5, 10, 5, 10, 10, 10, 10, 10, 5, 5, 10, 10, 5, 10, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql.pg_catalog", "sqlalchemy.dialects.postgresql.ranges", "sqlalchemy.dialects.postgresql.ext", "sqlalchemy.dialects.postgresql.hstore", "sqlalchemy.dialects.postgresql.named_types", "sqlalchemy.dialects.postgresql.types", "sqlalchemy.dialects.postgresql", "sqlalchemy.engine.characteristics", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.reflection", "sqlalchemy.sql.coercions", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.expression", "sqlalchemy.sql.roles", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.util", "sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.exc", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.types", "__future__", "collections", "functools", "re", "typing", "sqlalchemy", "builtins", "_collections_abc", "_decimal", "_typeshed", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.dialects.postgresql.array", "sqlalchemy.engine.base", "sqlalchemy.engine.url", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types", "typing_extensions", "uuid"], "hash": "1aee11a238d7f33d4cf236bdb18be079665499ae", "id": "sqlalchemy.dialects.postgresql.base", "ignore_all": true, "interface_hash": "135144e2d219f42d3431568f289c45ed3ffdd755", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/base.py", "plugin_data": null, "size": 179083, "suppressed": [], "version_id": "1.13.0"}