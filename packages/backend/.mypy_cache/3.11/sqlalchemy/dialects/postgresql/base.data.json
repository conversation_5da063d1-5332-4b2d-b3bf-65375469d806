{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.postgresql.base", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BIGINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BIGINT", "kind": "Gdef"}, "BIT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.BIT", "kind": "Gdef"}, "BOOLEAN": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BOOLEAN", "kind": "Gdef"}, "BYTEA": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.BYTEA", "kind": "Gdef"}, "CHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.CHAR", "kind": "Gdef"}, "CIDR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.CIDR", "kind": "Gdef"}, "CITEXT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.CITEXT", "kind": "Gdef"}, "CreateDomainType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.named_types.CreateDomainType", "kind": "Gdef"}, "CreateEnumType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.named_types.CreateEnumType", "kind": "Gdef"}, "DATE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DATE", "kind": "Gdef"}, "DOMAIN": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.named_types.DOMAIN", "kind": "Gdef"}, "DOUBLE_PRECISION": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DOUBLE_PRECISION", "kind": "Gdef"}, "DropDomainType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.named_types.DropDomainType", "kind": "Gdef"}, "DropEnumType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.named_types.DropEnumType", "kind": "Gdef"}, "ENUM": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.named_types.ENUM", "kind": "Gdef"}, "FLOAT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.FLOAT", "kind": "Gdef"}, "HSTORE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.hstore.HSTORE", "kind": "Gdef"}, "IDX_USING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.IDX_USING", "name": "IDX_USING", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "INET": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.INET", "kind": "Gdef"}, "INTEGER": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.INTEGER", "kind": "Gdef"}, "INTERVAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.INTERVAL", "kind": "Gdef"}, "InsertmanyvaluesSentinelOpts": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts", "kind": "Gdef"}, "InternalTraversal": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors.InternalTraversal", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MACADDR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.MACADDR", "kind": "Gdef"}, "MACADDR8": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.MACADDR8", "kind": "Gdef"}, "MONEY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.MONEY", "kind": "Gdef"}, "NUMERIC": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NUMERIC", "kind": "Gdef"}, "NamedType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.named_types.NamedType", "kind": "Gdef"}, "OID": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.OID", "kind": "Gdef"}, "ObjectKind": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.ObjectKind", "kind": "Gdef"}, "ObjectScope": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.ObjectScope", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PGBit": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.PGBit", "kind": "Gdef"}, "PGCidr": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.PGCidr", "kind": "Gdef"}, "PGCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.SQLCompiler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler", "name": "PGCompiler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.base", "mro": ["sqlalchemy.dialects.postgresql.base.PGCompiler", "sqlalchemy.sql.compiler.SQLCompiler", "sqlalchemy.sql.compiler.Compiled", "builtins.object"], "names": {".class": "SymbolTable", "_assert_pg_ts_ext": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler._assert_pg_ts_ext", "name": "_assert_pg_ts_ext", "type": null}}, "_on_conflict_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "clause", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler._on_conflict_target", "name": "_on_conflict_target", "type": null}}, "_regexp_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "base_op", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler._regexp_match", "name": "_regexp_match", "type": null}}, "delete_extra_from_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "delete_stmt", "from_table", "extra_froms", "from_hints", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.delete_extra_from_clause", "name": "delete_extra_from_clause", "type": null}}, "fetch_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.fetch_clause", "name": "fetch_clause", "type": null}}, "for_update_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.for_update_clause", "name": "for_update_clause", "type": null}}, "format_from_hint_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "sqltext", "table", "hint", "iscrud"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.format_from_hint_text", "name": "format_from_hint_text", "type": null}}, "get_select_precolumns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.get_select_precolumns", "name": "get_select_precolumns", "type": null}}, "limit_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.limit_clause", "name": "limit_clause", "type": null}}, "render_bind_cast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "type_", "dbapi_type", "sqltext"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.render_bind_cast", "name": "render_bind_cast", "type": null}}, "render_literal_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "type_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.render_literal_value", "name": "render_literal_value", "type": null}}, "update_from_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "update_stmt", "from_table", "extra_froms", "from_hints", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.update_from_clause", "name": "update_from_clause", "type": null}}, "visit_aggregate_order_by": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_aggregate_order_by", "name": "visit_aggregate_order_by", "type": null}}, "visit_aggregate_strings_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "fn", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_aggregate_strings_func", "name": "visit_aggregate_strings_func", "type": null}}, "visit_array": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_array", "name": "visit_array", "type": null}}, "visit_bitwise_xor_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_bitwise_xor_op_binary", "name": "visit_bitwise_xor_op_binary", "type": null}}, "visit_empty_set_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element_types", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_empty_set_expr", "name": "visit_empty_set_expr", "type": null}}, "visit_getitem_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_getitem_binary", "name": "visit_getitem_binary", "type": null}}, "visit_ilike_case_insensitive_operand": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_ilike_case_insensitive_operand", "name": "visit_ilike_case_insensitive_operand", "type": null}}, "visit_ilike_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_ilike_op_binary", "name": "visit_ilike_op_binary", "type": null}}, "visit_json_getitem_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "binary", "operator", "_cast_applied", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_json_getitem_op_binary", "name": "visit_json_getitem_op_binary", "type": null}}, "visit_json_path_getitem_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "binary", "operator", "_cast_applied", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_json_path_getitem_op_binary", "name": "visit_json_path_getitem_op_binary", "type": null}}, "visit_match_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_match_op_binary", "name": "visit_match_op_binary", "type": null}}, "visit_not_ilike_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_not_ilike_op_binary", "name": "visit_not_ilike_op_binary", "type": null}}, "visit_not_regexp_match_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_not_regexp_match_op_binary", "name": "visit_not_regexp_match_op_binary", "type": null}}, "visit_on_conflict_do_nothing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "on_conflict", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_on_conflict_do_nothing", "name": "visit_on_conflict_do_nothing", "type": null}}, "visit_on_conflict_do_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "on_conflict", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_on_conflict_do_update", "name": "visit_on_conflict_do_update", "type": null}}, "visit_phraseto_tsquery_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_phraseto_tsquery_func", "name": "visit_phraseto_tsquery_func", "type": null}}, "visit_plainto_tsquery_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_plainto_tsquery_func", "name": "visit_plainto_tsquery_func", "type": null}}, "visit_regexp_match_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_regexp_match_op_binary", "name": "visit_regexp_match_op_binary", "type": null}}, "visit_regexp_replace_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_regexp_replace_op_binary", "name": "visit_regexp_replace_op_binary", "type": null}}, "visit_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "seq", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_sequence", "name": "visit_sequence", "type": null}}, "visit_slice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_slice", "name": "visit_slice", "type": null}}, "visit_substring_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "func", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_substring_func", "name": "visit_substring_func", "type": null}}, "visit_to_tsquery_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_to_tsquery_func", "name": "visit_to_tsquery_func", "type": null}}, "visit_to_tsvector_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_to_tsvector_func", "name": "visit_to_tsvector_func", "type": null}}, "visit_ts_headline_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_ts_headline_func", "name": "visit_ts_headline_func", "type": null}}, "visit_websearch_to_tsquery_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.visit_websearch_to_tsquery_func", "name": "visit_websearch_to_tsquery_func", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.base.PGCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.base.PGCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PGDDLCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.DDLCompiler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler", "name": "PGDDLCompiler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.base", "mro": ["sqlalchemy.dialects.postgresql.base.PGDDLCompiler", "sqlalchemy.sql.compiler.DDLCompiler", "sqlalchemy.sql.compiler.Compiled", "builtins.object"], "names": {".class": "SymbolTable", "_can_comment_on_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ddl_instance"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler._can_comment_on_constraint", "name": "_can_comment_on_constraint", "type": null}}, "_define_constraint_validity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "constraint"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler._define_constraint_validity", "name": "_define_constraint_validity", "type": null}}, "define_unique_constraint_distinct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "constraint", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.define_unique_constraint_distinct", "name": "define_unique_constraint_distinct", "type": null}}, "get_column_specification": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "column", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.get_column_specification", "name": "get_column_specification", "type": null}}, "post_create_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.post_create_table", "name": "post_create_table", "type": null}}, "visit_check_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "constraint", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.visit_check_constraint", "name": "visit_check_constraint", "type": null}}, "visit_computed_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "generated", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.visit_computed_column", "name": "visit_computed_column", "type": null}}, "visit_create_domain_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.visit_create_domain_type", "name": "visit_create_domain_type", "type": null}}, "visit_create_enum_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.visit_create_enum_type", "name": "visit_create_enum_type", "type": null}}, "visit_create_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.visit_create_index", "name": "visit_create_index", "type": null}}, "visit_create_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.visit_create_sequence", "name": "visit_create_sequence", "type": null}}, "visit_drop_constraint_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.visit_drop_constraint_comment", "name": "visit_drop_constraint_comment", "type": null}}, "visit_drop_domain_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.visit_drop_domain_type", "name": "visit_drop_domain_type", "type": null}}, "visit_drop_enum_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.visit_drop_enum_type", "name": "visit_drop_enum_type", "type": null}}, "visit_drop_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.visit_drop_index", "name": "visit_drop_index", "type": null}}, "visit_exclude_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "constraint", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.visit_exclude_constraint", "name": "visit_exclude_constraint", "type": null}}, "visit_foreign_key_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "constraint", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.visit_foreign_key_constraint", "name": "visit_foreign_key_constraint", "type": null}}, "visit_set_constraint_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.visit_set_constraint_comment", "name": "visit_set_constraint_comment", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PGDeferrableConnectionCharacteristic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.characteristics.ConnectionCharacteristic"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.base.PGDeferrableConnectionCharacteristic", "name": "PGDeferrableConnectionCharacteristic", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDeferrableConnectionCharacteristic", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.base", "mro": ["sqlalchemy.dialects.postgresql.base.PGDeferrableConnectionCharacteristic", "sqlalchemy.engine.characteristics.ConnectionCharacteristic", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "get_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDeferrableConnectionCharacteristic.get_characteristic", "name": "get_characteristic", "type": null}}, "reset_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDeferrableConnectionCharacteristic.reset_characteristic", "name": "reset_characteristic", "type": null}}, "set_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDeferrableConnectionCharacteristic.set_characteristic", "name": "set_characteristic", "type": null}}, "transactional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDeferrableConnectionCharacteristic.transactional", "name": "transactional", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.base.PGDeferrableConnectionCharacteristic.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.base.PGDeferrableConnectionCharacteristic", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PGDialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.default.DefaultDialect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect", "name": "PGDialect", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.base", "mro": ["sqlalchemy.dialects.postgresql.base.PGDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "native_inet_types", "json_serializer", "json_deserializer", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.__init__", "name": "__init__", "type": null}}, "_backslash_escapes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._backslash_escapes", "name": "_backslash_escapes", "type": "builtins.bool"}}, "_check_constraint_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "schema", "has_filter_names", "scope", "kind"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._check_constraint_query", "name": "_check_constraint_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._check_constraint_query", "name": "_check_constraint_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_columns_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "schema", "has_filter_names", "scope", "kind"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._columns_query", "name": "_columns_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._columns_query", "name": "_columns_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_comment_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "schema", "has_filter_names", "scope", "kind"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._comment_query", "name": "_comment_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._comment_query", "name": "_comment_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_constraint_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "is_unique"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._constraint_query", "name": "_constraint_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._constraint_query", "name": "_constraint_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_domain_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._domain_query", "name": "_domain_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._domain_query", "name": "_domain_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_enum_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._enum_query", "name": "_enum_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._enum_query", "name": "_enum_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_fk_regex_pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._fk_regex_pattern", "name": "_fk_regex_pattern", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._fk_regex_pattern", "name": "_fk_regex_pattern", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_foreing_key_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "schema", "has_filter_names", "scope", "kind"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._foreing_key_query", "name": "_foreing_key_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._foreing_key_query", "name": "_foreing_key_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_format_array_spec_pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._format_array_spec_pattern", "name": "_format_array_spec_pattern", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_format_type_args_delim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._format_type_args_delim", "name": "_format_type_args_delim", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_format_type_args_pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._format_type_args_pattern", "name": "_format_type_args_pattern", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_get_columns_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "rows", "domains", "enums", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._get_columns_info", "name": "_get_columns_info", "type": null}}, "_get_default_schema_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._get_default_schema_name", "name": "_get_default_schema_name", "type": null}}, "_get_foreign_table_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._get_foreign_table_names", "name": "_get_foreign_table_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._get_foreign_table_names", "name": "_get_foreign_table_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_foreign_table_names of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_relnames_for_relkinds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "connection", "schema", "relkinds", "scope"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._get_relnames_for_relkinds", "name": "_get_relnames_for_relk<PERSON>s", "type": null}}, "_get_server_version_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._get_server_version_info", "name": "_get_server_version_info", "type": null}}, "_get_table_oids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "connection", "schema", "filter_names", "scope", "kind", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._get_table_oids", "name": "_get_table_oids", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._get_table_oids", "name": "_get_table_oids", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "_get_table_oids of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_has_table_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._has_table_query", "name": "_has_table_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._has_table_query", "name": "_has_table_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_index_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._index_query", "name": "_index_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._index_query", "name": "_index_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_json_deserializer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._json_deserializer", "name": "_json_deserializer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_json_serializer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._json_serializer", "name": "_json_serializer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_kind_to_relkinds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kind"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._kind_to_relk<PERSON>s", "name": "_kind_to_re<PERSON><PERSON>s", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kind"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", "sqlalchemy.engine.reflection.ObjectKind"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_kind_to_relkinds of PGDialect", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_domains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._load_domains", "name": "_load_domains", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._load_domains", "name": "_load_domains", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_domains of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_load_enums": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._load_enums", "name": "_load_enums", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._load_enums", "name": "_load_enums", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_enums of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_native_inet_types": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._native_inet_types", "name": "_native_inet_types", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_pg_class_filter_scope_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "query", "schema", "scope", "pg_class_table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._pg_class_filter_scope_schema", "name": "_pg_class_filter_scope_schema", "type": null}}, "_pg_class_relkind_condition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "relkinds", "pg_class_table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._pg_class_relkind_condition", "name": "_pg_class_relkind_condition", "type": null}}, "_pg_type_filter_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "query", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._pg_type_filter_schema", "name": "_pg_type_filter_schema", "type": null}}, "_prepare_filter_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filter_names"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._prepare_filter_names", "name": "_prepare_filter_names", "type": null}}, "_reflect_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "connection", "contype", "schema", "filter_names", "scope", "kind", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._reflect_constraint", "name": "_reflect_constraint", "type": null}}, "_reflect_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "format_type", "domains", "enums", "type_description"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._reflect_type", "name": "_reflect_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "format_type", "domains", "enums", "type_description"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects.postgresql.base.ReflectedDomain"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects.postgresql.base.ReflectedEnum"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reflect_type of PGDialect", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_backslash_escapes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._set_backslash_escapes", "name": "_set_backslash_escapes", "type": null}}, "_split_multihost_from_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._split_multihost_from_url", "name": "_split_multihost_from_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_split_multihost_from_url of PGDialect", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "NoneType"}, {".class": "NoneType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_supports_create_index_concurrently": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._supports_create_index_concurrently", "name": "_supports_create_index_concurrently", "type": "builtins.bool"}}, "_supports_drop_index_concurrently": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._supports_drop_index_concurrently", "name": "_supports_drop_index_concurrently", "type": "builtins.bool"}}, "_table_oids_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "schema", "has_filter_names", "scope", "kind"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._table_oids_query", "name": "_table_oids_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._table_oids_query", "name": "_table_oids_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_value_or_raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "data", "table", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect._value_or_raise", "name": "_value_or_raise", "type": null}}, "bind_typing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.bind_typing", "name": "bind_typing", "type": "sqlalchemy.engine.interfaces.BindTyping"}}, "colspecs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.colspecs", "name": "colspecs", "type": {".class": "Instance", "args": ["builtins.type", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "connection_characteristics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.connection_characteristics", "name": "connection_characteristics", "type": {".class": "Instance", "args": ["builtins.str", "sqlalchemy.engine.characteristics.ConnectionCharacteristic"], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "construct_arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.construct_arguments", "name": "construct_arguments", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.SchemaItem"}, {".class": "TypeType", "item": "sqlalchemy.sql.elements.ClauseElement"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ddl_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.ddl_compiler", "name": "ddl_compiler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["dialect", "statement", "schema_translate_map", "render_schema_translate", "compile_kwargs"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.sql.ddl.ExecutableDDLElement", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.SchemaTranslateMapType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": ["sqlalchemy.dialects.postgresql.base.PGDDLCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_paramstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.default_paramstyle", "name": "default_paramstyle", "type": "builtins.str"}}, "delete_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.delete_returning", "name": "delete_returning", "type": "builtins.bool"}}, "delete_returning_multifrom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.delete_returning_multifrom", "name": "delete_returning_multifrom", "type": "builtins.bool"}}, "do_begin_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "xid"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.do_begin_twophase", "name": "do_begin_twophase", "type": null}}, "do_commit_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "connection", "xid", "is_prepared", "recover"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.do_commit_twophase", "name": "do_commit_twophase", "type": null}}, "do_prepare_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "xid"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.do_prepare_twophase", "name": "do_prepare_twophase", "type": null}}, "do_recover_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.do_recover_twophase", "name": "do_recover_twophase", "type": null}}, "do_rollback_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "connection", "xid", "is_prepared", "recover"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.do_rollback_twophase", "name": "do_rollback_twophase", "type": null}}, "execution_ctx_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.execution_ctx_cls", "name": "execution_ctx_cls", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["sqlalchemy.dialects.postgresql.base.PGExecutionContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.postgresql.base.PGExecutionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_check_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_check_constraints", "name": "get_check_constraints", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_check_constraints", "name": "get_check_constraints", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_check_constraints of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_columns", "name": "get_columns", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_columns", "name": "get_columns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_columns of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_deferrable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_deferrable", "name": "get_deferrable", "type": null}}, "get_foreign_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "postgresql_ignore_search_path", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_foreign_keys", "name": "get_foreign_keys", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_foreign_keys", "name": "get_foreign_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "postgresql_ignore_search_path", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_foreign_keys of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_indexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_indexes", "name": "get_indexes", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_indexes", "name": "get_indexes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_indexes of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_isolation_level", "name": "get_isolation_level", "type": null}}, "get_isolation_level_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_isolation_level_values", "name": "get_isolation_level_values", "type": null}}, "get_materialized_view_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_materialized_view_names", "name": "get_materialized_view_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_materialized_view_names", "name": "get_materialized_view_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_materialized_view_names of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_multi_check_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "connection", "schema", "filter_names", "scope", "kind", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_multi_check_constraints", "name": "get_multi_check_constraints", "type": null}}, "get_multi_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "connection", "schema", "filter_names", "scope", "kind", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_multi_columns", "name": "get_multi_columns", "type": null}}, "get_multi_foreign_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "filter_names", "scope", "kind", "postgresql_ignore_search_path", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_multi_foreign_keys", "name": "get_multi_foreign_keys", "type": null}}, "get_multi_indexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "connection", "schema", "filter_names", "scope", "kind", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_multi_indexes", "name": "get_multi_indexes", "type": null}}, "get_multi_pk_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "connection", "schema", "filter_names", "scope", "kind", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_multi_pk_constraint", "name": "get_multi_pk_constraint", "type": null}}, "get_multi_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "connection", "schema", "filter_names", "scope", "kind", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_multi_table_comment", "name": "get_multi_table_comment", "type": null}}, "get_multi_unique_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "connection", "schema", "filter_names", "scope", "kind", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_multi_unique_constraints", "name": "get_multi_unique_constraints", "type": null}}, "get_pk_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_pk_constraint", "name": "get_pk_constraint", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_pk_constraint", "name": "get_pk_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_pk_constraint of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_readonly": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_readonly", "name": "get_readonly", "type": null}}, "get_schema_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_schema_names", "name": "get_schema_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_schema_names", "name": "get_schema_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_schema_names of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_sequence_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_sequence_names", "name": "get_sequence_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_sequence_names", "name": "get_sequence_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sequence_names of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_table_comment", "name": "get_table_comment", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_table_comment", "name": "get_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_table_comment of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_table_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_table_names", "name": "get_table_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_table_names", "name": "get_table_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_table_names of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_table_oid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_table_oid", "name": "get_table_oid", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_table_oid", "name": "get_table_oid", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_table_oid of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_temp_table_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_temp_table_names", "name": "get_temp_table_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_temp_table_names", "name": "get_temp_table_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_temp_table_names of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_temp_view_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_temp_view_names", "name": "get_temp_view_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_temp_view_names", "name": "get_temp_view_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_temp_view_names of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_unique_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_unique_constraints", "name": "get_unique_constraints", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_unique_constraints", "name": "get_unique_constraints", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unique_constraints of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_view_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "view_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_view_definition", "name": "get_view_definition", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_view_definition", "name": "get_view_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "view_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_view_definition of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_view_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_view_names", "name": "get_view_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.get_view_names", "name": "get_view_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_view_names of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.has_schema", "name": "has_schema", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.has_schema", "name": "has_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_schema of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "sequence_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.has_sequence", "name": "has_sequence", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.has_sequence", "name": "has_sequence", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "sequence_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_sequence of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.has_table", "name": "has_table", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.has_table", "name": "has_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_table of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "type_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.has_type", "name": "has_type", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.has_type", "name": "has_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "type_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_type of PGDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.initialize", "name": "initialize", "type": null}}, "insert_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.insert_returning", "name": "insert_returning", "type": "builtins.bool"}}, "insertmanyvalues_implicit_sentinel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.insertmanyvalues_implicit_sentinel", "name": "insertmanyvalues_implicit_sentinel", "type": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts"}}, "inspector": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.inspector", "name": "inspector", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["bind"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection"], "uses_pep604_syntax": false}], "bound_args": ["sqlalchemy.dialects.postgresql.base.PGInspector"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.postgresql.base.PGInspector", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ischema_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.ischema_names", "name": "ischema_names", "type": {".class": "Instance", "args": ["builtins.str", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "max_identifier_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.max_identifier_length", "name": "max_identifier_length", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.name", "name": "name", "type": "builtins.str"}}, "postfetch_lastrowid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.postfetch_lastrowid", "name": "postfetch_lastrowid", "type": "builtins.bool"}}, "preexecute_autoincrement_sequences": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.preexecute_autoincrement_sequences", "name": "preexecute_autoincrement_sequences", "type": "builtins.bool"}}, "preparer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.preparer", "name": "preparer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["dialect", "initial_quote", "final_quote", "escape_quote", "quote_case_sensitive_collations", "omit_schema"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["sqlalchemy.dialects.postgresql.base.PGIdentifierPreparer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.postgresql.base.PGIdentifierPreparer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reflection_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.reflection_options", "name": "reflection_options", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "returns_native_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.returns_native_bytes", "name": "returns_native_bytes", "type": "builtins.bool"}}, "sequences_optional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.sequences_optional", "name": "sequences_optional", "type": "builtins.bool"}}, "set_deferrable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.set_deferrable", "name": "set_deferrable", "type": null}}, "set_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "level"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.set_isolation_level", "name": "set_isolation_level", "type": null}}, "set_readonly": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.set_readonly", "name": "set_readonly", "type": null}}, "statement_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.statement_compiler", "name": "statement_compiler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["dialect", "statement", "cache_key", "column_keys", "for_executemany", "linting", "_supporting_against", "kwargs"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.cache_key.CacheKey"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "sqlalchemy.sql.compiler.<PERSON>", {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": ["sqlalchemy.dialects.postgresql.base.PGCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.postgresql.base.PGCompiler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_alter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.supports_alter", "name": "supports_alter", "type": "builtins.bool"}}, "supports_comments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.supports_comments", "name": "supports_comments", "type": "builtins.bool"}}, "supports_constraint_comments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.supports_constraint_comments", "name": "supports_constraint_comments", "type": "builtins.bool"}}, "supports_default_metavalue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.supports_default_metavalue", "name": "supports_default_metavalue", "type": "builtins.bool"}}, "supports_default_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.supports_default_values", "name": "supports_default_values", "type": "builtins.bool"}}, "supports_empty_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.supports_empty_insert", "name": "supports_empty_insert", "type": "builtins.bool"}}, "supports_identity_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.supports_identity_columns", "name": "supports_identity_columns", "type": "builtins.bool"}}, "supports_multivalues_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.supports_multivalues_insert", "name": "supports_multivalues_insert", "type": "builtins.bool"}}, "supports_native_boolean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.supports_native_boolean", "name": "supports_native_boolean", "type": "builtins.bool"}}, "supports_native_enum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.supports_native_enum", "name": "supports_native_enum", "type": "builtins.bool"}}, "supports_native_uuid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.supports_native_uuid", "name": "supports_native_uuid", "type": "builtins.bool"}}, "supports_sane_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.supports_sane_rowcount", "name": "supports_sane_rowcount", "type": "builtins.bool"}}, "supports_sequences": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.supports_sequences", "name": "supports_sequences", "type": "builtins.bool"}}, "supports_smallserial": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.supports_smallserial", "name": "supports_smallserial", "type": "builtins.bool"}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}, "type_compiler_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.type_compiler_cls", "name": "type_compiler_cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dialect"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect"], "bound_args": ["sqlalchemy.dialects.postgresql.base.PGTypeCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.update_returning", "name": "update_returning", "type": "builtins.bool"}}, "update_returning_multifrom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.update_returning_multifrom", "name": "update_returning_multifrom", "type": "builtins.bool"}}, "use_insertmanyvalues": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.use_insertmanyvalues", "name": "use_insertmanyvalues", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.base.PGDialect.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.base.PGDialect", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PGExecutionContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.default.DefaultExecutionContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.base.PGExecutionContext", "name": "PGExecutionContext", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGExecutionContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.base", "mro": ["sqlalchemy.dialects.postgresql.base.PGExecutionContext", "sqlalchemy.engine.default.DefaultExecutionContext", "sqlalchemy.engine.interfaces.ExecutionContext", "builtins.object"], "names": {".class": "SymbolTable", "fire_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "seq", "type_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGExecutionContext.fire_sequence", "name": "fire_sequence", "type": null}}, "get_insert_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "column"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGExecutionContext.get_insert_default", "name": "get_insert_default", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.base.PGExecutionContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.base.PGExecutionContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PGIdentifierPreparer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.IdentifierPreparer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.base.PGIdentifierPreparer", "name": "PGIdentifierPreparer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGIdentifierPreparer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.base", "mro": ["sqlalchemy.dialects.postgresql.base.PGIdentifierPreparer", "sqlalchemy.sql.compiler.IdentifierPreparer", "builtins.object"], "names": {".class": "SymbolTable", "_unquote_identifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGIdentifierPreparer._unquote_identifier", "name": "_unquote_identifier", "type": null}}, "format_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "type_", "use_schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGIdentifierPreparer.format_type", "name": "format_type", "type": null}}, "reserved_words": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGIdentifierPreparer.reserved_words", "name": "reserved_words", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.base.PGIdentifierPreparer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.base.PGIdentifierPreparer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PGInet": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.PGInet", "kind": "Gdef"}, "PGInspector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.reflection.Inspector"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.base.PGInspector", "name": "PGInspector", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGInspector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.base", "mro": ["sqlalchemy.dialects.postgresql.base.PGInspector", "sqlalchemy.engine.reflection.Inspector", "sqlalchemy.inspection.Inspectable", "builtins.object"], "names": {".class": "SymbolTable", "dialect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.dialects.postgresql.base.PGInspector.dialect", "name": "dialect", "type": "sqlalchemy.dialects.postgresql.base.PGDialect"}}, "get_domains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGInspector.get_domains", "name": "get_domains", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "schema"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGInspector", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_domains of PGInspector", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects.postgresql.base.ReflectedDomain"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_enums": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGInspector.get_enums", "name": "get_enums", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "schema"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGInspector", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_enums of PGInspector", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects.postgresql.base.ReflectedEnum"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_foreign_table_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGInspector.get_foreign_table_names", "name": "get_foreign_table_names", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "schema"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGInspector", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_foreign_table_names of PGInspector", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_table_oid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "table_name", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGInspector.get_table_oid", "name": "get_table_oid", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "table_name", "schema"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGInspector", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_table_oid of PGInspector", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "type_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGInspector.has_type", "name": "has_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "type_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.postgresql.base.PGInspector", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_type of PGInspector", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.base.PGInspector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.base.PGInspector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PGInterval": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.PGInterval", "kind": "Gdef"}, "PGMacAddr": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.PGMacAddr", "kind": "Gdef"}, "PGMacAddr8": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.PGMacAddr8", "kind": "Gdef"}, "PGReadOnlyConnectionCharacteristic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.characteristics.ConnectionCharacteristic"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.base.PGReadOnlyConnectionCharacteristic", "name": "PGReadOnlyConnectionCharacteristic", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGReadOnlyConnectionCharacteristic", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.base", "mro": ["sqlalchemy.dialects.postgresql.base.PGReadOnlyConnectionCharacteristic", "sqlalchemy.engine.characteristics.ConnectionCharacteristic", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "get_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGReadOnlyConnectionCharacteristic.get_characteristic", "name": "get_characteristic", "type": null}}, "reset_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGReadOnlyConnectionCharacteristic.reset_characteristic", "name": "reset_characteristic", "type": null}}, "set_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGReadOnlyConnectionCharacteristic.set_characteristic", "name": "set_characteristic", "type": null}}, "transactional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.PGReadOnlyConnectionCharacteristic.transactional", "name": "transactional", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.base.PGReadOnlyConnectionCharacteristic.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.base.PGReadOnlyConnectionCharacteristic", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PGTypeCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.GenericTypeCompiler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler", "name": "PGTypeCompiler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.base", "mro": ["sqlalchemy.dialects.postgresql.base.PGTypeCompiler", "sqlalchemy.sql.compiler.GenericTypeCompiler", "sqlalchemy.sql.compiler.TypeCompiler", "sqlalchemy.util.langhelpers.EnsureKWArg", "builtins.object"], "names": {".class": "SymbolTable", "visit_ARRAY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_ARRAY", "name": "visit_ARRAY", "type": null}}, "visit_BIGINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_BIGINT", "name": "visit_BIGINT", "type": null}}, "visit_BIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_BIT", "name": "visit_BIT", "type": null}}, "visit_BYTEA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_BYTEA", "name": "visit_BYTEA", "type": null}}, "visit_CIDR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_CIDR", "name": "visit_CIDR", "type": null}}, "visit_CITEXT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_CITEXT", "name": "visit_CITEXT", "type": null}}, "visit_DATEMULTIRANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_DATEMULTIRANGE", "name": "visit_DATEMULTIRANGE", "type": null}}, "visit_DATERANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_DATERANGE", "name": "visit_DATERANGE", "type": null}}, "visit_DOMAIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "type_", "identifier_preparer", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_DOMAIN", "name": "visit_DOMAIN", "type": null}}, "visit_ENUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "type_", "identifier_preparer", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_ENUM", "name": "visit_ENUM", "type": null}}, "visit_FLOAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_FLOAT", "name": "visit_FLOAT", "type": null}}, "visit_HSTORE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_HSTORE", "name": "visit_HSTORE", "type": null}}, "visit_INET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_INET", "name": "visit_INET", "type": null}}, "visit_INT4MULTIRANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_INT4MULTIRANGE", "name": "visit_INT4MULTIRANGE", "type": null}}, "visit_INT4RANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_INT4RANGE", "name": "visit_INT4RANGE", "type": null}}, "visit_INT8MULTIRANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_INT8MULTIRANGE", "name": "visit_INT8MULTIRANGE", "type": null}}, "visit_INT8RANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_INT8RANGE", "name": "visit_INT8RANGE", "type": null}}, "visit_INTERVAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_INTERVAL", "name": "visit_INTERVAL", "type": null}}, "visit_JSON": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_JSON", "name": "visit_JSON", "type": null}}, "visit_JSONB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_JSONB", "name": "visit_JSONB", "type": null}}, "visit_JSONPATH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_JSONPATH", "name": "visit_JSONPATH", "type": null}}, "visit_MACADDR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_MACADDR", "name": "visit_MACADDR", "type": null}}, "visit_MACADDR8": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_MACADDR8", "name": "visit_MACADDR8", "type": null}}, "visit_MONEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_MONEY", "name": "visit_MONEY", "type": null}}, "visit_NUMMULTIRANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_NUMMULTIRANGE", "name": "visit_NUMMULTIRANGE", "type": null}}, "visit_NUMRANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_NUMRANGE", "name": "visit_NUMRANGE", "type": null}}, "visit_OID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_OID", "name": "visit_OID", "type": null}}, "visit_REGCLASS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_REGCLASS", "name": "visit_REGCLASS", "type": null}}, "visit_REGCONFIG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_REGCONFIG", "name": "visit_REGCONFIG", "type": null}}, "visit_TIME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_TIME", "name": "visit_TIME", "type": null}}, "visit_TIMESTAMP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_TIMESTAMP", "name": "visit_TIMESTAMP", "type": null}}, "visit_TSMULTIRANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_TSMULTIRANGE", "name": "visit_TSMULTIRANGE", "type": null}}, "visit_TSQUERY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_TSQUERY", "name": "visit_TSQUERY", "type": null}}, "visit_TSRANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_TSRANGE", "name": "visit_TSRANGE", "type": null}}, "visit_TSTZMULTIRANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_TSTZMULTIRANGE", "name": "visit_TSTZMULTIRANGE", "type": null}}, "visit_TSTZRANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_TSTZRANGE", "name": "visit_TSTZRANGE", "type": null}}, "visit_TSVECTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_TSVECTOR", "name": "visit_TSVECTOR", "type": null}}, "visit_UUID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_UUID", "name": "visit_UUID", "type": null}}, "visit_datetime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_datetime", "name": "visit_datetime", "type": null}}, "visit_double": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_double", "name": "visit_double", "type": null}}, "visit_enum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_enum", "name": "visit_enum", "type": null}}, "visit_json_int_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_json_int_index", "name": "visit_json_int_index", "type": null}}, "visit_json_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_json_path", "name": "visit_json_path", "type": null}}, "visit_json_str_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_json_str_index", "name": "visit_json_str_index", "type": null}}, "visit_large_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_large_binary", "name": "visit_large_binary", "type": null}}, "visit_uuid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.visit_uuid", "name": "visit_uuid", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.base.PGTypeCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PGUuid": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.PGUuid", "kind": "Gdef"}, "REAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.REAL", "kind": "Gdef"}, "REGCLASS": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.REGCLASS", "kind": "Gdef"}, "REGCONFIG": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.REGCONFIG", "kind": "Gdef"}, "RESERVED_WORDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.RESERVED_WORDS", "name": "RESERVED_WORDS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "ReflectedDomain": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.base.ReflectedDomain", "name": "ReflectedDomain", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.ReflectedDomain", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.base", "mro": ["sqlalchemy.dialects.postgresql.base.ReflectedDomain", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", "builtins.str"], ["schema", "builtins.str"], ["visible", "builtins.bool"], ["type", "builtins.str"], ["nullable", "builtins.bool"], ["default", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["constraints", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.dialects.postgresql.base.ReflectedDomainConstraint"}], "extra_attrs": null, "type_ref": "builtins.list"}], ["collation", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": ["collation", "constraints", "default", "name", "nullable", "schema", "type", "visible"]}}}, "ReflectedDomainConstraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.base.ReflectedDomainConstraint", "name": "ReflectedDomainConstraint", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.ReflectedDomainConstraint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.base", "mro": ["sqlalchemy.dialects.postgresql.base.ReflectedDomainConstraint", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", "builtins.str"], ["check", "builtins.str"]], "readonly_keys": [], "required_keys": ["check", "name"]}}}, "ReflectedEnum": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.base.ReflectedEnum", "name": "ReflectedEnum", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.ReflectedEnum", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.base", "mro": ["sqlalchemy.dialects.postgresql.base.ReflectedEnum", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", "builtins.str"], ["schema", "builtins.str"], ["visible", "builtins.bool"], ["labels", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}]], "readonly_keys": [], "required_keys": ["labels", "name", "schema", "visible"]}}}, "ReflectedNamedType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.base.ReflectedNamedType", "name": "ReflectedNamedType", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.base.ReflectedNamedType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.base", "mro": ["sqlalchemy.dialects.postgresql.base.ReflectedNamedType", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", "builtins.str"], ["schema", "builtins.str"], ["visible", "builtins.bool"]], "readonly_keys": [], "required_keys": ["name", "schema", "visible"]}}}, "ReflectionDefaults": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.ReflectionDefaults", "kind": "Gdef"}, "SMALLINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.SMALLINT", "kind": "Gdef"}, "TEXT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.TEXT", "kind": "Gdef"}, "TIME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.TIME", "kind": "Gdef"}, "TIMESTAMP": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.TIMESTAMP", "kind": "Gdef"}, "TSVECTOR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.TSVECTOR", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypedDict", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.URL", "kind": "Gdef"}, "UUID": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.UUID", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "VARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.VARCHAR", "kind": "Gdef"}, "_DECIMAL_TYPES": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types._DECIMAL_TYPES", "kind": "Gdef"}, "_FLOAT_TYPES": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types._FLOAT_TYPES", "kind": "Gdef"}, "_INT_TYPES": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types._INT_TYPES", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.base.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_array": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.array", "kind": "Gdef"}, "_json": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.json", "kind": "Gdef"}, "_ranges": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges", "kind": "Gdef"}, "_regconfig_fn": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ext._regconfig_fn", "kind": "Gdef"}, "aggregate_order_by": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ext.aggregate_order_by", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "bindparam": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.bindparam", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "characteristics": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.characteristics", "kind": "Gdef"}, "coercions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.coercions", "kind": "Gdef"}, "colspecs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.colspecs", "name": "colspecs", "type": {".class": "Instance", "args": ["builtins.type", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "compiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler", "kind": "Gdef"}, "default": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.default", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "elements": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "expression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.expression", "kind": "Gdef"}, "interfaces": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces", "kind": "Gdef"}, "ischema_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.base.ischema_names", "name": "ischema_names", "type": {".class": "Instance", "args": ["builtins.str", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "lru_cache": {".class": "SymbolTableNode", "cross_ref": "functools.lru_cache", "kind": "Gdef"}, "pg_catalog": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.pg_catalog", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "reflection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection", "kind": "Gdef"}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef"}, "schema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.schema", "kind": "Gdef"}, "select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.select", "kind": "Gdef"}, "sql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql", "kind": "Gdef"}, "sql_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.util", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/base.py"}