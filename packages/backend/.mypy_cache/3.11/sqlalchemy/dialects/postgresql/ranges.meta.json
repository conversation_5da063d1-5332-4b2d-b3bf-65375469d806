{"data_mtime": 1751259991, "dep_lines": [28, 37, 38, 40, 43, 36, 37, 39, 8, 10, 11, 14, 15, 36, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 25, 10, 20, 5, 5, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.operators", "sqlalchemy.sql.operators", "sqlalchemy.sql.type_api", "sqlalchemy.util.typing", "sqlalchemy.sql.elements", "sqlalchemy.types", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "dataclasses", "datetime", "decimal", "typing", "sqlalchemy", "builtins", "_decimal", "abc", "importlib", "importlib.machinery", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util.compat", "sqlalchemy.util.langhelpers", "types"], "hash": "954389aba1defb5eebd924ebbe0cd280786ba2ac", "id": "sqlalchemy.dialects.postgresql.ranges", "ignore_all": true, "interface_hash": "5219b91c17fc4639841c9c4f94e5c5586ca8f820", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/ranges.py", "plugin_data": null, "size": 32949, "suppressed": [], "version_id": "1.13.0"}