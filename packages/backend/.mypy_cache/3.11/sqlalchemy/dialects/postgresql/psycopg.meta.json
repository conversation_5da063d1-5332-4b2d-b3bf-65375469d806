{"data_mtime": 1751259991, "dep_lines": [94, 95, 97, 101, 104, 94, 108, 109, 105, 106, 107, 108, 86, 88, 89, 90, 91, 105, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 347, 357, 396, 444, 450, 340, 347, 438, 115], "dep_prios": [10, 5, 5, 5, 5, 20, 10, 5, 10, 10, 5, 20, 5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20, 20, 20, 20, 20, 20, 20], "dependencies": ["sqlalchemy.dialects.postgresql.ranges", "sqlalchemy.dialects.postgresql._psycopg_common", "sqlalchemy.dialects.postgresql.base", "sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql.types", "sqlalchemy.dialects.postgresql", "sqlalchemy.sql.sqltypes", "sqlalchemy.util.concurrency", "sqlalchemy.pool", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.sql", "__future__", "collections", "logging", "re", "typing", "sqlalchemy", "builtins", "_typeshed", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._concurrency_py3k", "sqlalchemy.util.langhelpers", "types", "typing_extensions"], "hash": "252bb866a1c80d3ad637974faf0f5a49be5f07e0", "id": "sqlalchemy.dialects.postgresql.psycopg", "ignore_all": true, "interface_hash": "4c3329ee0215c70ccd3685535c950dce46e208f4", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/psycopg.py", "plugin_data": null, "size": 23168, "suppressed": ["psycopg.types.string", "psycopg.types.json", "psycopg.types.hstore", "psycopg.types.range", "psycopg.types.multirange", "psycopg.adapt", "psycopg.types", "psycopg.pq", "psycopg"], "version_id": "1.13.0"}