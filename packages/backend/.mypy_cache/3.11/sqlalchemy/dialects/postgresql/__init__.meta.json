{"data_mtime": 1751259991, "dep_lines": [11, 12, 13, 14, 15, 16, 17, 36, 38, 47, 49, 52, 58, 75, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.array", "sqlalchemy.dialects.postgresql.asyncpg", "sqlalchemy.dialects.postgresql.base", "sqlalchemy.dialects.postgresql.pg8000", "sqlalchemy.dialects.postgresql.psycopg", "sqlalchemy.dialects.postgresql.psycopg2", "sqlalchemy.dialects.postgresql.psycopg2cffi", "sqlalchemy.dialects.postgresql.dml", "sqlalchemy.dialects.postgresql.ext", "sqlalchemy.dialects.postgresql.hstore", "sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql.named_types", "sqlalchemy.dialects.postgresql.ranges", "sqlalchemy.dialects.postgresql.types", "types", "builtins", "abc", "importlib", "importlib.machinery", "sqlalchemy.dialects.postgresql._psycopg_common", "sqlalchemy.engine", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "typing"], "hash": "b46e52656a76754c8412012302e7d123ac121944", "id": "sqlalchemy.dialects.postgresql", "ignore_all": true, "interface_hash": "789d43186694c60a2de7eda69ae7fce0d99b1708", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/__init__.py", "plugin_data": null, "size": 3892, "suppressed": [], "version_id": "1.13.0"}