{"data_mtime": 1751259991, "dep_lines": [14, 15, 14, 16, 17, 18, 19, 20, 21, 23, 24, 16, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 10, 10, 10, 10, 5, 5, 5, 20, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.types", "sqlalchemy.dialects.postgresql.array", "sqlalchemy.dialects.postgresql", "sqlalchemy.sql.coercions", "sqlalchemy.sql.elements", "sqlalchemy.sql.expression", "sqlalchemy.sql.functions", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.visitors", "sqlalchemy.sql", "__future__", "typing", "builtins", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util", "sqlalchemy.util.langhelpers", "typing_extensions"], "hash": "d01db355cec04b948b689fea76a834efd4f00af4", "id": "sqlalchemy.dialects.postgresql.ext", "ignore_all": true, "interface_hash": "354951e133ec881391286b5dbccc07d318916d2a", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/ext.py", "plugin_data": null, "size": 16262, "suppressed": [], "version_id": "1.13.0"}