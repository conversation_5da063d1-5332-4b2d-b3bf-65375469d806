{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.postgresql.pg_catalog", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ARRAY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.array.ARRAY", "kind": "Gdef"}, "BigInteger": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BigInteger", "kind": "Gdef"}, "Boolean": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Boolean", "kind": "Gdef"}, "CHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.CHAR", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "Float": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Float", "kind": "Gdef"}, "INT2VECTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeDecorator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.INT2VECTOR", "name": "INT2VECTOR", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.INT2VECTOR", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.pg_catalog", "mro": ["sqlalchemy.dialects.postgresql.pg_catalog.INT2VECTOR", "sqlalchemy.sql.type_api.TypeDecorator", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.type_api.ExternalType", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "cache_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.INT2VECTOR.cache_ok", "name": "cache_ok", "type": "builtins.bool"}}, "impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.INT2VECTOR.impl", "name": "impl", "type": "sqlalchemy.dialects.postgresql.array.ARRAY"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.INT2VECTOR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.pg_catalog.INT2VECTOR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Integer": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Integer", "kind": "Gdef"}, "MetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.MetaData", "kind": "Gdef"}, "NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeDecorator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.NAME", "name": "NAME", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.NAME", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.pg_catalog", "mro": ["sqlalchemy.dialects.postgresql.pg_catalog.NAME", "sqlalchemy.sql.type_api.TypeDecorator", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.type_api.ExternalType", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "cache_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.NAME.cache_ok", "name": "cache_ok", "type": "builtins.bool"}}, "impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.NAME.impl", "name": "impl", "type": "sqlalchemy.sql.sqltypes.String"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.NAME.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.pg_catalog.NAME", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OID": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.OID", "kind": "Gdef"}, "OIDVECTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeDecorator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.OIDVECTOR", "name": "OIDVECTOR", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.OIDVECTOR", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.pg_catalog", "mro": ["sqlalchemy.dialects.postgresql.pg_catalog.OIDVECTOR", "sqlalchemy.sql.type_api.TypeDecorator", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.type_api.ExternalType", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "cache_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.OIDVECTOR.cache_ok", "name": "cache_ok", "type": "builtins.bool"}}, "impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.OIDVECTOR.impl", "name": "impl", "type": "sqlalchemy.dialects.postgresql.array.ARRAY"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.OIDVECTOR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.pg_catalog.OIDVECTOR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PG_NODE_TREE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeDecorator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.PG_NODE_TREE", "name": "PG_NODE_TREE", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.PG_NODE_TREE", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.pg_catalog", "mro": ["sqlalchemy.dialects.postgresql.pg_catalog.PG_NODE_TREE", "sqlalchemy.sql.type_api.TypeDecorator", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.type_api.ExternalType", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "cache_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.PG_NODE_TREE.cache_ok", "name": "cache_ok", "type": "builtins.bool"}}, "impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.PG_NODE_TREE.impl", "name": "impl", "type": "sqlalchemy.sql.sqltypes.Text"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.PG_NODE_TREE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.pg_catalog.PG_NODE_TREE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "REGCLASS": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.REGCLASS", "kind": "Gdef"}, "REGPROC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.REGPROC", "line": 58, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.postgresql.types.REGCLASS"}}, "RELKINDS_ALL_TABLE_LIKE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.RELKINDS_ALL_TABLE_LIKE", "name": "RELKINDS_ALL_TABLE_LIKE", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "RELKINDS_MAT_VIEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.RELKINDS_MAT_VIEW", "name": "RELKINDS_MAT_VIEW", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "RELKINDS_TABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.RELKINDS_TABLE", "name": "RELKINDS_TABLE", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "RELKINDS_TABLE_NO_FOREIGN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.RELKINDS_TABLE_NO_FOREIGN", "name": "RELKINDS_TABLE_NO_FOREIGN", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "RELKINDS_VIEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.RELKINDS_VIEW", "name": "RELKINDS_VIEW", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "SmallInteger": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.SmallInteger", "kind": "Gdef"}, "String": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.String", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "Text": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Text", "kind": "Gdef"}, "TypeDecorator": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeDecorator", "kind": "Gdef"}, "_SpaceVector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql.pg_catalog._SpaceVector", "name": "_SpaceVector", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog._SpaceVector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql.pg_catalog", "mro": ["sqlalchemy.dialects.postgresql.pg_catalog._SpaceVector", "builtins.object"], "names": {".class": "SymbolTable", "result_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "coltype"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog._SpaceVector.result_processor", "name": "result_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql.pg_catalog._SpaceVector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql.pg_catalog._SpaceVector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_pg_cat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog._pg_cat", "name": "_pg_cat", "type": "sqlalchemy.sql.functions._FunctionGenerator"}}, "format_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.format_type", "name": "format_type", "type": "sqlalchemy.sql.functions._FunctionGenerator"}}, "func": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.func", "kind": "Gdef"}, "pg_am": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_am", "name": "pg_am", "type": "sqlalchemy.sql.schema.Table"}}, "pg_attrdef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_attrdef", "name": "pg_attrdef", "type": "sqlalchemy.sql.schema.Table"}}, "pg_attribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_attribute", "name": "pg_attribute", "type": "sqlalchemy.sql.schema.Table"}}, "pg_catalog_meta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_catalog_meta", "name": "pg_catalog_meta", "type": "sqlalchemy.sql.schema.MetaData"}}, "pg_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_class", "name": "pg_class", "type": "sqlalchemy.sql.schema.Table"}}, "pg_collation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_collation", "name": "pg_collation", "type": "sqlalchemy.sql.schema.Table"}}, "pg_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_constraint", "name": "pg_constraint", "type": "sqlalchemy.sql.schema.Table"}}, "pg_description": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_description", "name": "pg_description", "type": "sqlalchemy.sql.schema.Table"}}, "pg_enum": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_enum", "name": "pg_enum", "type": "sqlalchemy.sql.schema.Table"}}, "pg_get_constraintdef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_get_constraintdef", "name": "pg_get_constraintdef", "type": "sqlalchemy.sql.functions._FunctionGenerator"}}, "pg_get_expr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_get_expr", "name": "pg_get_expr", "type": "sqlalchemy.sql.functions._FunctionGenerator"}}, "pg_get_indexdef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_get_indexdef", "name": "pg_get_indexdef", "type": "sqlalchemy.sql.functions._FunctionGenerator"}}, "pg_get_serial_sequence": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_get_serial_sequence", "name": "pg_get_serial_sequence", "type": "sqlalchemy.sql.functions._FunctionGenerator"}}, "pg_get_viewdef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_get_viewdef", "name": "pg_get_viewdef", "type": "sqlalchemy.sql.functions._FunctionGenerator"}}, "pg_index": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_index", "name": "pg_index", "type": "sqlalchemy.sql.schema.Table"}}, "pg_namespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_namespace", "name": "pg_namespace", "type": "sqlalchemy.sql.schema.Table"}}, "pg_sequence": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_sequence", "name": "pg_sequence", "type": "sqlalchemy.sql.schema.Table"}}, "pg_table_is_visible": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_table_is_visible", "name": "pg_table_is_visible", "type": "sqlalchemy.sql.functions._FunctionGenerator"}}, "pg_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_type", "name": "pg_type", "type": "sqlalchemy.sql.schema.Table"}}, "pg_type_is_visible": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.pg_type_is_visible", "name": "pg_type_is_visible", "type": "sqlalchemy.sql.functions._FunctionGenerator"}}, "quote_ident": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.pg_catalog.quote_ident", "name": "quote_ident", "type": "sqlalchemy.sql.functions._FunctionGenerator"}}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/pg_catalog.py"}