{"data_mtime": 1751259991, "dep_lines": [1009, 1010, 1012, 1015, 1017, 1009, 1054, 1055, 1056, 1058, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 1066, 1069, 1076, 1048, 1050, 1051, 1052, 1053, 1054, 1070, 1001, 1003, 1004, 1005, 1006, 1007, 1048, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 20, 10, 10, 5, 10, 5, 10, 10, 10, 10, 10, 10, 10, 5, 10, 10, 10, 10, 10, 10, 20, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.mysql.reflection", "sqlalchemy.dialects.mysql.enumerated", "sqlalchemy.dialects.mysql.json", "sqlalchemy.dialects.mysql.reserved_words", "sqlalchemy.dialects.mysql.types", "sqlalchemy.dialects.mysql", "sqlalchemy.engine.cursor", "sqlalchemy.engine.default", "sqlalchemy.engine.reflection", "sqlalchemy.sql.coercions", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.util", "sqlalchemy.sql.visitors", "sqlalchemy.sql.schema", "sqlalchemy.util.topological", "sqlalchemy.exc", "sqlalchemy.log", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.types", "__future__", "array", "collections", "itertools", "re", "typing", "sqlalchemy", "builtins", "_decimal", "_typeshed", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql._selectable_constructors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.dml", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util.langhelpers", "types", "typing_extensions", "uuid"], "hash": "9b0c0d7ce3706d7aba2c338c9beb7bc4edb13f4d", "id": "sqlalchemy.dialects.mysql.base", "ignore_all": true, "interface_hash": "c79c37c26c02e44bda706ed781b65084853c71b3", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/base.py", "plugin_data": null, "size": 122755, "suppressed": [], "version_id": "1.13.0"}