{"data_mtime": 1751259991, "dep_lines": [10, 11, 12, 13, 14, 15, 16, 17, 18, 53, 55, 56, 56, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 10, 10, 10, 10, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.mysql.aiomysql", "sqlalchemy.dialects.mysql.asyncmy", "sqlalchemy.dialects.mysql.base", "sqlalchemy.dialects.mysql.cymysql", "sqlalchemy.dialects.mysql.mariadbconnector", "sqlalchemy.dialects.mysql.mysqlconnector", "sqlalchemy.dialects.mysql.mysqldb", "sqlalchemy.dialects.mysql.pymysql", "sqlalchemy.dialects.mysql.pyodbc", "sqlalchemy.dialects.mysql.dml", "sqlalchemy.dialects.mysql.expression", "sqlalchemy.util.compat", "sqlalchemy.util", "builtins", "abc", "importlib", "importlib.machinery", "sqlalchemy.engine", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "types", "typing"], "hash": "bb3df6d0147bdb19aad5e763782c664cd8849204", "id": "sqlalchemy.dialects.mysql", "ignore_all": true, "interface_hash": "d6554695644ec934b0d90b6ef539f356fe77cdb3", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/__init__.py", "plugin_data": null, "size": 2153, "suppressed": [], "version_id": "1.13.0"}