{"data_mtime": 1751259991, "dep_lines": [12, 16, 13, 14, 15, 10, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.mysql.types", "sqlalchemy.sql.sqltypes", "sqlalchemy.exc", "sqlalchemy.sql", "sqlalchemy.util", "re", "sqlalchemy", "builtins", "_typeshed", "abc", "importlib", "importlib.machinery", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.base", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "typing"], "hash": "6f1ade3a16af2695504a17fd5a201b627206eaea", "id": "sqlalchemy.dialects.mysql.enumerated", "ignore_all": true, "interface_hash": "319f32eaa4e44f70d1fa577f8ac03e5d2470a885", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/enumerated.py", "plugin_data": null, "size": 8448, "suppressed": [], "version_id": "1.13.0"}