{"data_mtime": 1751259991, "dep_lines": [32, 36, 33, 34, 35, 30, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 325], "dep_prios": [5, 5, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["sqlalchemy.dialects.mysql.pymysql", "sqlalchemy.util.concurrency", "sqlalchemy.pool", "sqlalchemy.util", "sqlalchemy.engine", "collections", "sqlalchemy", "builtins", "abc", "asyncio", "asyncio.locks", "asyncio.mixins", "importlib", "importlib.machinery", "sqlalchemy.connectors", "sqlalchemy.connectors.aioodbc", "sqlalchemy.dialects.mysql.base", "sqlalchemy.dialects.mysql.mysqldb", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.util._concurrency_py3k", "typing"], "hash": "2e9447b2d260f3e21791f0cf3f08575e3fd1eab3", "id": "sqlalchemy.dialects.mysql.aiomysql", "ignore_all": true, "interface_hash": "503386f5a5b101cb6040c8ccd1d795beea2f8917", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/aiomysql.py", "plugin_data": null, "size": 9999, "suppressed": ["pymysql.constants"], "version_id": "1.13.0"}