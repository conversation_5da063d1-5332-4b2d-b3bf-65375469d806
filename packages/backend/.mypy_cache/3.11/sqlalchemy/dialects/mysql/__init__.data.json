{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.mysql", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BIGINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.BIGINT", "kind": "Gdef"}, "BINARY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BINARY", "kind": "Gdef"}, "BIT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.BIT", "kind": "Gdef"}, "BLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BLOB", "kind": "Gdef"}, "BOOLEAN": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BOOLEAN", "kind": "Gdef"}, "CHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.CHAR", "kind": "Gdef"}, "DATE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DATE", "kind": "Gdef"}, "DATETIME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.DATETIME", "kind": "Gdef"}, "DECIMAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.DECIMAL", "kind": "Gdef"}, "DOUBLE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.DOUBLE", "kind": "Gdef"}, "ENUM": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.enumerated.ENUM", "kind": "Gdef"}, "FLOAT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.FLOAT", "kind": "Gdef"}, "INTEGER": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.INTEGER", "kind": "Gdef"}, "Insert": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.dml.Insert", "kind": "Gdef"}, "JSON": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.json.JSON", "kind": "Gdef"}, "LONGBLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.LONGBLOB", "kind": "Gdef"}, "LONGTEXT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.LONGTEXT", "kind": "Gdef"}, "MEDIUMBLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.MEDIUMBLOB", "kind": "Gdef"}, "MEDIUMINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.MEDIUMINT", "kind": "Gdef"}, "MEDIUMTEXT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.MEDIUMTEXT", "kind": "Gdef"}, "NCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.NCHAR", "kind": "Gdef"}, "NUMERIC": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.NUMERIC", "kind": "Gdef"}, "NVARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.NVARCHAR", "kind": "Gdef"}, "REAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.REAL", "kind": "Gdef"}, "SET": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.enumerated.SET", "kind": "Gdef"}, "SMALLINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.SMALLINT", "kind": "Gdef"}, "TEXT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.TEXT", "kind": "Gdef"}, "TIME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.TIME", "kind": "Gdef"}, "TIMESTAMP": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.TIMESTAMP", "kind": "Gdef"}, "TINYBLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.TINYBLOB", "kind": "Gdef"}, "TINYINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.TINYINT", "kind": "Gdef"}, "TINYTEXT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.TINYTEXT", "kind": "Gdef"}, "VARBINARY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.VARBINARY", "kind": "Gdef"}, "VARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.VARCHAR", "kind": "Gdef"}, "YEAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.YEAR", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "aiomysql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.aiomysql", "kind": "Gdef", "module_public": false}, "asyncmy": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.asyncmy", "kind": "Gdef", "module_public": false}, "base": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.base", "kind": "Gdef", "module_public": false}, "compat": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat", "kind": "Gdef", "module_public": false}, "cymysql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.cymysql", "kind": "Gdef", "module_public": false}, "dialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.dialect", "name": "dialect", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["sqlalchemy.dialects.mysql.mysqldb.MySQLDialect_mysqldb"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.mysql.mysqldb.MySQLDialect_mysqldb", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "insert": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.dml.insert", "kind": "Gdef"}, "mariadbconnector": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.mariadbconnector", "kind": "Gdef", "module_public": false}, "match": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.expression.match", "kind": "Gdef"}, "mysqlconnector": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.mysqlconnector", "kind": "Gdef", "module_public": false}, "mysqldb": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.mysqldb", "kind": "Gdef", "module_public": false}, "pymysql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.pymysql", "kind": "Gdef", "module_public": false}, "pyodbc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.pyodbc", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/__init__.py"}