{"data_mtime": 1751259991, "dep_lines": [12, 14, 17, 18, 19, 10, 17, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.mysql.enumerated", "sqlalchemy.dialects.mysql.types", "sqlalchemy.log", "sqlalchemy.types", "sqlalchemy.util", "re", "sqlalchemy", "builtins", "_typeshed", "abc", "importlib", "importlib.machinery", "sqlalchemy.util.langhelpers", "typing", "typing_extensions"], "hash": "e419a7773079177f71d18a79e3f5c9206deb50b7", "id": "sqlalchemy.dialects.mysql.reflection", "ignore_all": true, "interface_hash": "f93c279ed0eafb5d76a19715bf25c8c3ce778de2", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/reflection.py", "plugin_data": null, "size": 22834, "suppressed": [], "version_id": "1.13.0"}