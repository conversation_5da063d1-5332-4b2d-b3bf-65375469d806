{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.mysql.base", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BIGINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.BIGINT", "kind": "Gdef"}, "BINARY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BINARY", "kind": "Gdef"}, "BIT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.BIT", "kind": "Gdef"}, "BLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BLOB", "kind": "Gdef"}, "BOOLEAN": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BOOLEAN", "kind": "Gdef"}, "CHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.CHAR", "kind": "Gdef"}, "DATE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DATE", "kind": "Gdef"}, "DATETIME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.DATETIME", "kind": "Gdef"}, "DECIMAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.DECIMAL", "kind": "Gdef"}, "DOUBLE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.DOUBLE", "kind": "Gdef"}, "ENUM": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.enumerated.ENUM", "kind": "Gdef"}, "FLOAT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.FLOAT", "kind": "Gdef"}, "INTEGER": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.INTEGER", "kind": "Gdef"}, "InsertmanyvaluesSentinelOpts": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts", "kind": "Gdef"}, "JSON": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.json.JSON", "kind": "Gdef"}, "JSONIndexType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.json.JSONIndexType", "kind": "Gdef"}, "JSONPathType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.json.JSONPathType", "kind": "Gdef"}, "LONGBLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.LONGBLOB", "kind": "Gdef"}, "LONGTEXT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.LONGTEXT", "kind": "Gdef"}, "MEDIUMBLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.MEDIUMBLOB", "kind": "Gdef"}, "MEDIUMINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.MEDIUMINT", "kind": "Gdef"}, "MEDIUMTEXT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.MEDIUMTEXT", "kind": "Gdef"}, "MSBigInteger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSBigInteger", "line": 1107, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.BIGINT"}}, "MSBinary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSBinary", "line": 1091, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.sql.sqltypes.BINARY"}}, "MSBit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSBit", "line": 1103, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.BIT"}}, "MSBlob": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSBlob", "line": 1090, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.sql.sqltypes.BLOB"}}, "MSChar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSChar", "line": 1095, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.CHAR"}}, "MSDecimal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSDecimal", "line": 1109, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.DECIMAL"}}, "MSDouble": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSDouble", "line": 1110, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.DOUBLE"}}, "MSEnum": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSEnum", "line": 1086, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.enumerated.ENUM"}}, "MSFloat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSFloat", "line": 1112, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.FLOAT"}}, "MSInteger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSInteger", "line": 1113, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.INTEGER"}}, "MSLongBlob": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSLongBlob", "line": 1087, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.LONGBLOB"}}, "MSLongText": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSLongText", "line": 1097, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.LONGTEXT"}}, "MSMediumBlob": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSMediumBlob", "line": 1088, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.MEDIUMBLOB"}}, "MSMediumInteger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSMediumInteger", "line": 1106, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.MEDIUMINT"}}, "MSMediumText": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSMediumText", "line": 1098, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.MEDIUMTEXT"}}, "MSNChar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSNChar", "line": 1093, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.NCHAR"}}, "MSNVarChar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSNVarChar", "line": 1094, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.NVARCHAR"}}, "MSNumeric": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSNumeric", "line": 1108, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.NUMERIC"}}, "MSReal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSReal", "line": 1111, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.REAL"}}, "MSSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSSet", "line": 1085, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.enumerated.SET"}}, "MSSmallInteger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSSmallInteger", "line": 1104, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.SMALLINT"}}, "MSString": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSString", "line": 1096, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.VARCHAR"}}, "MSText": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSText", "line": 1100, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.TEXT"}}, "MSTime": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSTime", "line": 1084, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.TIME"}}, "MSTimeStamp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSTimeStamp", "line": 1102, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.TIMESTAMP"}}, "MSTinyBlob": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSTinyBlob", "line": 1089, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.TINYBLOB"}}, "MSTinyInteger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSTinyInteger", "line": 1105, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.TINYINT"}}, "MSTinyText": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSTinyText", "line": 1099, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.TINYTEXT"}}, "MSVarBinary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSVarBinary", "line": 1092, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.sql.sqltypes.VARBINARY"}}, "MSYear": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.base.MSYear", "line": 1101, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.types.YEAR"}}, "MariaDBIdentifierPreparer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.base.MySQLIdentifierPreparer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.base.MariaDBIdentifierPreparer", "name": "MariaDBIdentifierPreparer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MariaDBIdentifierPreparer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.base", "mro": ["sqlalchemy.dialects.mysql.base.MariaDBIdentifierPreparer", "sqlalchemy.dialects.mysql.base.MySQLIdentifierPreparer", "sqlalchemy.sql.compiler.IdentifierPreparer", "builtins.object"], "names": {".class": "SymbolTable", "reserved_words": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MariaDBIdentifierPreparer.reserved_words", "name": "reserved_words", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.base.MariaDBIdentifierPreparer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.base.MariaDBIdentifierPreparer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MySQLCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.SQLCompiler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler", "name": "MySQLCompiler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.base", "mro": ["sqlalchemy.dialects.mysql.base.MySQLCompiler", "sqlalchemy.sql.compiler.SQLCompiler", "sqlalchemy.sql.compiler.Compiled", "builtins.object"], "names": {".class": "SymbolTable", "_mariadb_regexp_flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "flags", "pattern", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler._mariadb_regexp_flags", "name": "_mariadb_regexp_flags", "type": null}}, "_match_flag_expressions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler._match_flag_expressions", "name": "_match_flag_expressions", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_match_valid_flag_combinations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler._match_valid_flag_combinations", "name": "_match_valid_flag_combinations", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "_regexp_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "op_string", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler._regexp_match", "name": "_regexp_match", "type": null}}, "_render_json_extract_from_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler._render_json_extract_from_binary", "name": "_render_json_extract_from_binary", "type": null}}, "default_from": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.default_from", "name": "default_from", "type": null}}, "delete_extra_from_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "delete_stmt", "from_table", "extra_froms", "from_hints", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.delete_extra_from_clause", "name": "delete_extra_from_clause", "type": null}}, "delete_table_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "delete_stmt", "from_table", "extra_froms", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.delete_table_clause", "name": "delete_table_clause", "type": null}}, "extract_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.extract_map", "name": "extract_map", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "for_update_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.for_update_clause", "name": "for_update_clause", "type": null}}, "get_from_hint_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "table", "text"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.get_from_hint_text", "name": "get_from_hint_text", "type": null}}, "get_select_precolumns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.get_select_precolumns", "name": "get_select_precolumns", "type": null}}, "limit_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.limit_clause", "name": "limit_clause", "type": null}}, "render_literal_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "type_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.render_literal_value", "name": "render_literal_value", "type": null}}, "render_table_with_column_in_update_from": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.render_table_with_column_in_update_from", "name": "render_table_with_column_in_update_from", "type": "builtins.bool"}}, "update_from_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "update_stmt", "from_table", "extra_froms", "from_hints", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.update_from_clause", "name": "update_from_clause", "type": null}}, "update_limit_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "update_stmt"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.update_limit_clause", "name": "update_limit_clause", "type": null}}, "update_tables_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "update_stmt", "from_table", "extra_froms", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.update_tables_clause", "name": "update_tables_clause", "type": null}}, "visit_aggregate_strings_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "fn", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_aggregate_strings_func", "name": "visit_aggregate_strings_func", "type": null}}, "visit_cast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "cast", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_cast", "name": "visit_cast", "type": null}}, "visit_concat_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_concat_op_binary", "name": "visit_concat_op_binary", "type": null}}, "visit_concat_op_expression_clauselist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "clauselist", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_concat_op_expression_clauselist", "name": "visit_concat_op_expression_clauselist", "type": null}}, "visit_empty_set_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element_types", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_empty_set_expr", "name": "visit_empty_set_expr", "type": null}}, "visit_false": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_false", "name": "visit_false", "type": null}}, "visit_is_distinct_from_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_is_distinct_from_binary", "name": "visit_is_distinct_from_binary", "type": null}}, "visit_is_not_distinct_from_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_is_not_distinct_from_binary", "name": "visit_is_not_distinct_from_binary", "type": null}}, "visit_join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "join", "asfrom", "from_linter", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_join", "name": "visit_join", "type": null}}, "visit_json_getitem_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_json_getitem_op_binary", "name": "visit_json_getitem_op_binary", "type": null}}, "visit_json_path_getitem_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_json_path_getitem_op_binary", "name": "visit_json_path_getitem_op_binary", "type": null}}, "visit_match_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_match_op_binary", "name": "visit_match_op_binary", "type": null}}, "visit_mysql_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_mysql_match", "name": "visit_mysql_match", "type": null}}, "visit_not_regexp_match_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_not_regexp_match_op_binary", "name": "visit_not_regexp_match_op_binary", "type": null}}, "visit_on_duplicate_key_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "on_duplicate", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_on_duplicate_key_update", "name": "visit_on_duplicate_key_update", "type": null}}, "visit_random_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "fn", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_random_func", "name": "visit_random_func", "type": null}}, "visit_regexp_match_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_regexp_match_op_binary", "name": "visit_regexp_match_op_binary", "type": null}}, "visit_regexp_replace_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_regexp_replace_op_binary", "name": "visit_regexp_replace_op_binary", "type": null}}, "visit_rollup_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "fn", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_rollup_func", "name": "visit_rollup_func", "type": null}}, "visit_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "seq", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_sequence", "name": "visit_sequence", "type": null}}, "visit_sysdate_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "fn", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_sysdate_func", "name": "visit_sysdate_func", "type": null}}, "visit_true": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_true", "name": "visit_true", "type": null}}, "visit_typeclause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "type<PERSON>lause", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.visit_typeclause", "name": "visit_typeclause", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.base.MySQLCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.base.MySQLCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MySQLDDLCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.DDLCompiler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.base.MySQLDDLCompiler", "name": "MySQLDDLCompiler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDDLCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.base", "mro": ["sqlalchemy.dialects.mysql.base.MySQLDDLCompiler", "sqlalchemy.sql.compiler.DDLCompiler", "sqlalchemy.sql.compiler.Compiled", "builtins.object"], "names": {".class": "SymbolTable", "define_constraint_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "constraint"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDDLCompiler.define_constraint_match", "name": "define_constraint_match", "type": null}}, "get_column_specification": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "column", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDDLCompiler.get_column_specification", "name": "get_column_specification", "type": null}}, "post_create_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDDLCompiler.post_create_table", "name": "post_create_table", "type": null}}, "visit_create_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDDLCompiler.visit_create_index", "name": "visit_create_index", "type": null}}, "visit_drop_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDDLCompiler.visit_drop_constraint", "name": "visit_drop_constraint", "type": null}}, "visit_drop_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDDLCompiler.visit_drop_index", "name": "visit_drop_index", "type": null}}, "visit_drop_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDDLCompiler.visit_drop_table_comment", "name": "visit_drop_table_comment", "type": null}}, "visit_primary_key_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "constraint", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDDLCompiler.visit_primary_key_constraint", "name": "visit_primary_key_constraint", "type": null}}, "visit_set_column_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDDLCompiler.visit_set_column_comment", "name": "visit_set_column_comment", "type": null}}, "visit_set_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDDLCompiler.visit_set_table_comment", "name": "visit_set_table_comment", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.base.MySQLDDLCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.base.MySQLDDLCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MySQLDialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.default.DefaultDialect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect", "name": "MySQLDialect", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.base", "mro": ["sqlalchemy.dialects.mysql.base.MySQLDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "json_serializer", "json_deserializer", "is_mariadb", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.__init__", "name": "__init__", "type": null}}, "_backslash_escapes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._backslash_escapes", "name": "_backslash_escapes", "type": "builtins.bool"}}, "_casing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._casing", "name": "_casing", "type": "builtins.int"}}, "_compat_fetchall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "rp", "charset"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._compat_fetchall", "name": "_compat_fetchall", "type": null}}, "_compat_fetchone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "rp", "charset"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._compat_fetchone", "name": "_compat_fetchone", "type": null}}, "_compat_first": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "rp", "charset"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._compat_first", "name": "_compat_first", "type": null}}, "_connection_charset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._connection_charset", "name": "_connection_charset", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_correct_for_mysql_bugs_88718_96365": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fkeys", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._correct_for_mysql_bugs_88718_96365", "name": "_correct_for_mysql_bugs_88718_96365", "type": null}}, "_describe_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "connection", "table", "charset", "full_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._describe_table", "name": "_describe_table", "type": null}}, "_detect_ansiquotes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._detect_ansiquotes", "name": "_detect_ansiquotes", "type": null}}, "_detect_casing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._detect_casing", "name": "_detect_casing", "type": null}}, "_detect_charset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._detect_charset", "name": "_detect_charset", "type": null}}, "_detect_collations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._detect_collations", "name": "_detect_collations", "type": null}}, "_detect_sql_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._detect_sql_mode", "name": "_detect_sql_mode", "type": null}}, "_extract_error_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._extract_error_code", "name": "_extract_error_code", "type": null}}, "_fetch_setting": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "setting_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._fetch_setting", "name": "_fetch_setting", "type": null}}, "_get_default_schema_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._get_default_schema_name", "name": "_get_default_schema_name", "type": null}}, "_get_server_version_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._get_server_version_info", "name": "_get_server_version_info", "type": null}}, "_is_mariadb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._is_mariadb", "name": "_is_mariadb", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._is_mariadb", "name": "_is_mariadb", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_mariadb of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_is_mariadb_102": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._is_mariadb_102", "name": "_is_mariadb_102", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._is_mariadb_102", "name": "_is_mariadb_102", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_mariadb_102 of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_is_mariadb_from_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._is_mariadb_from_url", "name": "_is_mariadb_from_url", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._is_mariadb_from_url", "name": "_is_mariadb_from_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.mysql.base.MySQLDialect"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_mariadb_from_url of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_is_mysql": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._is_mysql", "name": "_is_mysql", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._is_mysql", "name": "_is_mysql", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_mysql of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_json_deserializer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._json_deserializer", "name": "_json_deserializer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_json_serializer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._json_serializer", "name": "_json_serializer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_mariadb_normalized_version_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._mariadb_normalized_version_info", "name": "_mariadb_normalized_version_info", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_needs_correct_for_88718_96365": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._needs_correct_for_88718_96365", "name": "_needs_correct_for_88718_96365", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}, "builtins.bool"], "uses_pep604_syntax": false}}}, "_parse_server_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._parse_server_version", "name": "_parse_server_version", "type": null}}, "_parsed_state_or_create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._parsed_state_or_create", "name": "_parsed_state_or_create", "type": null}}, "_requires_alias_for_on_duplicate_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._requires_alias_for_on_duplicate_key", "name": "_requires_alias_for_on_duplicate_key", "type": "builtins.bool"}}, "_sequences_not_supported": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._sequences_not_supported", "name": "_sequences_not_supported", "type": null}}, "_server_ansiquotes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._server_ansiquotes", "name": "_server_ansiquotes", "type": "builtins.bool"}}, "_set_mariadb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "is_mariadb", "server_version_info"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._set_mariadb", "name": "_set_mariadb", "type": null}}, "_setup_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._setup_parser", "name": "_setup_parser", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._setup_parser", "name": "_setup_parser", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_parser of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_show_create_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "connection", "table", "charset", "full_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._show_create_table", "name": "_show_create_table", "type": null}}, "_sql_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._sql_mode", "name": "_sql_mode", "type": "builtins.str"}}, "_support_default_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._support_default_function", "name": "_support_default_function", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._support_default_function", "name": "_support_default_function", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_support_default_function of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_support_float_cast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._support_float_cast", "name": "_support_float_cast", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._support_float_cast", "name": "_support_float_cast", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_support_float_cast of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_tabledef_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._tabledef_parser", "name": "_tabledef_parser", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._tabledef_parser", "name": "_tabledef_parser", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_warn_for_known_db_issues": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect._warn_for_known_db_issues", "name": "_warn_for_known_db_issues", "type": null}}, "colspecs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.colspecs", "name": "colspecs", "type": {".class": "Instance", "args": ["builtins.type", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "construct_arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.construct_arguments", "name": "construct_arguments", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.SchemaItem"}, {".class": "TypeType", "item": "sqlalchemy.sql.elements.ClauseElement"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "cte_follows_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.cte_follows_insert", "name": "cte_follows_insert", "type": "builtins.bool"}}, "ddl_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.ddl_compiler", "name": "ddl_compiler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["dialect", "statement", "schema_translate_map", "render_schema_translate", "compile_kwargs"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.sql.ddl.ExecutableDDLElement", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.SchemaTranslateMapType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": ["sqlalchemy.dialects.mysql.base.MySQLDDLCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.mysql.base.MySQLDDLCompiler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_paramstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.default_paramstyle", "name": "default_paramstyle", "type": "builtins.str"}}, "div_is_floordiv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.div_is_floordiv", "name": "div_is_floordiv", "type": "builtins.bool"}}, "do_begin_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "xid"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.do_begin_twophase", "name": "do_begin_twophase", "type": null}}, "do_commit_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "connection", "xid", "is_prepared", "recover"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.do_commit_twophase", "name": "do_commit_twophase", "type": null}}, "do_prepare_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "xid"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.do_prepare_twophase", "name": "do_prepare_twophase", "type": null}}, "do_recover_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.do_recover_twophase", "name": "do_recover_twophase", "type": null}}, "do_rollback_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "connection", "xid", "is_prepared", "recover"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.do_rollback_twophase", "name": "do_rollback_twophase", "type": null}}, "get_check_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_check_constraints", "name": "get_check_constraints", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_check_constraints", "name": "get_check_constraints", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_check_constraints of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_columns", "name": "get_columns", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_columns", "name": "get_columns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_columns of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_foreign_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_foreign_keys", "name": "get_foreign_keys", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_foreign_keys", "name": "get_foreign_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_foreign_keys of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_indexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_indexes", "name": "get_indexes", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_indexes", "name": "get_indexes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_indexes of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_isolation_level", "name": "get_isolation_level", "type": null}}, "get_isolation_level_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_isolation_level_values", "name": "get_isolation_level_values", "type": null}}, "get_pk_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_pk_constraint", "name": "get_pk_constraint", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_pk_constraint", "name": "get_pk_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_pk_constraint of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_schema_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_schema_names", "name": "get_schema_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_schema_names", "name": "get_schema_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_schema_names of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_sequence_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_sequence_names", "name": "get_sequence_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_sequence_names", "name": "get_sequence_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sequence_names of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_table_comment", "name": "get_table_comment", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_table_comment", "name": "get_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_table_comment of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_table_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_table_names", "name": "get_table_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_table_names", "name": "get_table_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_table_names of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_table_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_table_options", "name": "get_table_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_table_options", "name": "get_table_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_table_options of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_unique_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_unique_constraints", "name": "get_unique_constraints", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_unique_constraints", "name": "get_unique_constraints", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unique_constraints of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_view_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "view_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_view_definition", "name": "get_view_definition", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_view_definition", "name": "get_view_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "view_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_view_definition of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_view_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_view_names", "name": "get_view_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.get_view_names", "name": "get_view_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_view_names of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "sequence_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.has_sequence", "name": "has_sequence", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.has_sequence", "name": "has_sequence", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "sequence_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_sequence of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.has_table", "name": "has_table", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.has_table", "name": "has_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.mysql.base.MySQLDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_table of MySQLDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.initialize", "name": "initialize", "type": null}}, "inline_comments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.inline_comments", "name": "inline_comments", "type": "builtins.bool"}}, "insert_null_pk_still_autoincrements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.insert_null_pk_still_autoincrements", "name": "insert_null_pk_still_autoincrements", "type": "builtins.bool"}}, "insertmanyvalues_implicit_sentinel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.insertmanyvalues_implicit_sentinel", "name": "insertmanyvalues_implicit_sentinel", "type": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts"}}, "is_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "e", "connection", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.is_disconnect", "name": "is_disconnect", "type": null}}, "is_mariadb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.is_mariadb", "name": "is_mariadb", "type": "builtins.bool"}}, "ischema_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.ischema_names", "name": "ischema_names", "type": {".class": "Instance", "args": ["builtins.str", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "max_constraint_name_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.max_constraint_name_length", "name": "max_constraint_name_length", "type": "builtins.int"}}, "max_identifier_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.max_identifier_length", "name": "max_identifier_length", "type": "builtins.int"}}, "max_index_name_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.max_index_name_length", "name": "max_index_name_length", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.name", "name": "name", "type": "builtins.str"}}, "preparer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.preparer", "name": "preparer", "type": null}}, "returns_native_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.returns_native_bytes", "name": "returns_native_bytes", "type": "builtins.bool"}}, "sequences_optional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.sequences_optional", "name": "sequences_optional", "type": "builtins.bool"}}, "set_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "level"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.set_isolation_level", "name": "set_isolation_level", "type": null}}, "statement_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.statement_compiler", "name": "statement_compiler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["dialect", "statement", "cache_key", "column_keys", "for_executemany", "linting", "_supporting_against", "kwargs"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.cache_key.CacheKey"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "sqlalchemy.sql.compiler.<PERSON>", {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": ["sqlalchemy.dialects.mysql.base.MySQLCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.mysql.base.MySQLCompiler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_alter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.supports_alter", "name": "supports_alter", "type": "builtins.bool"}}, "supports_comments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.supports_comments", "name": "supports_comments", "type": "builtins.bool"}}, "supports_default_metavalue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.supports_default_metavalue", "name": "supports_default_metavalue", "type": "builtins.bool"}}, "supports_default_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.supports_default_values", "name": "supports_default_values", "type": "builtins.bool"}}, "supports_for_update_of": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.supports_for_update_of", "name": "supports_for_update_of", "type": "builtins.bool"}}, "supports_multivalues_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.supports_multivalues_insert", "name": "supports_multivalues_insert", "type": "builtins.bool"}}, "supports_native_boolean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.supports_native_boolean", "name": "supports_native_boolean", "type": "builtins.bool"}}, "supports_native_enum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.supports_native_enum", "name": "supports_native_enum", "type": "builtins.bool"}}, "supports_sane_multi_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.supports_sane_multi_rowcount", "name": "supports_sane_multi_rowcount", "type": "builtins.bool"}}, "supports_sane_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.supports_sane_rowcount", "name": "supports_sane_rowcount", "type": "builtins.bool"}}, "supports_sequences": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.supports_sequences", "name": "supports_sequences", "type": "builtins.bool"}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}, "type_compiler_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.type_compiler_cls", "name": "type_compiler_cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dialect"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect"], "bound_args": ["sqlalchemy.dialects.mysql.base.MySQLTypeCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "use_insertmanyvalues": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.use_insertmanyvalues", "name": "use_insertmanyvalues", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.base.MySQLDialect.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.base.MySQLDialect", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MySQLExecutionContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.default.DefaultExecutionContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.base.MySQLExecutionContext", "name": "MySQLExecutionContext", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLExecutionContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.base", "mro": ["sqlalchemy.dialects.mysql.base.MySQLExecutionContext", "sqlalchemy.engine.default.DefaultExecutionContext", "sqlalchemy.engine.interfaces.ExecutionContext", "builtins.object"], "names": {".class": "SymbolTable", "create_server_side_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLExecutionContext.create_server_side_cursor", "name": "create_server_side_cursor", "type": null}}, "fire_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "seq", "type_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLExecutionContext.fire_sequence", "name": "fire_sequence", "type": null}}, "post_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLExecutionContext.post_exec", "name": "post_exec", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.base.MySQLExecutionContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.base.MySQLExecutionContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MySQLIdentifierPreparer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.IdentifierPreparer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.base.MySQLIdentifierPreparer", "name": "MySQLIdentifierPreparer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLIdentifierPreparer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.base", "mro": ["sqlalchemy.dialects.mysql.base.MySQLIdentifierPreparer", "sqlalchemy.sql.compiler.IdentifierPreparer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "dialect", "server_ansiquotes", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLIdentifierPreparer.__init__", "name": "__init__", "type": null}}, "_quote_free_identifiers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "ids"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLIdentifierPreparer._quote_free_identifiers", "name": "_quote_free_identifiers", "type": null}}, "reserved_words": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.MySQLIdentifierPreparer.reserved_words", "name": "reserved_words", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.base.MySQLIdentifierPreparer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.base.MySQLIdentifierPreparer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MySQLTypeCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.GenericTypeCompiler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler", "name": "MySQLTypeCompiler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.base", "mro": ["sqlalchemy.dialects.mysql.base.MySQLTypeCompiler", "sqlalchemy.sql.compiler.GenericTypeCompiler", "sqlalchemy.sql.compiler.TypeCompiler", "sqlalchemy.util.langhelpers.EnsureKWArg", "builtins.object"], "names": {".class": "SymbolTable", "_extend_numeric": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "type_", "spec"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler._extend_numeric", "name": "_extend_numeric", "type": null}}, "_extend_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "type_", "defaults", "spec"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler._extend_string", "name": "_extend_string", "type": null}}, "_mysql_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "type_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler._mysql_type", "name": "_mysql_type", "type": null}}, "_visit_enumerated_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "type_", "enumerated_values"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler._visit_enumerated_values", "name": "_visit_enumerated_values", "type": null}}, "visit_BIGINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_BIGINT", "name": "visit_BIGINT", "type": null}}, "visit_BIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_BIT", "name": "visit_BIT", "type": null}}, "visit_BLOB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_BLOB", "name": "visit_BLOB", "type": null}}, "visit_BOOLEAN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_BOOLEAN", "name": "visit_BOOLEAN", "type": null}}, "visit_CHAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_CHAR", "name": "visit_CHAR", "type": null}}, "visit_DATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_DATE", "name": "visit_DATE", "type": null}}, "visit_DATETIME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_DATETIME", "name": "visit_DATETIME", "type": null}}, "visit_DECIMAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_DECIMAL", "name": "visit_DECIMAL", "type": null}}, "visit_DOUBLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_DOUBLE", "name": "visit_DOUBLE", "type": null}}, "visit_ENUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_ENUM", "name": "visit_ENUM", "type": null}}, "visit_FLOAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_FLOAT", "name": "visit_FLOAT", "type": null}}, "visit_INTEGER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_INTEGER", "name": "visit_INTEGER", "type": null}}, "visit_JSON": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_JSON", "name": "visit_JSON", "type": null}}, "visit_LONGBLOB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_LONGBLOB", "name": "visit_LONGBLOB", "type": null}}, "visit_LONGTEXT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_LONGTEXT", "name": "visit_LONGTEXT", "type": null}}, "visit_MEDIUMBLOB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_MEDIUMBLOB", "name": "visit_MEDIUMBLOB", "type": null}}, "visit_MEDIUMINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_MEDIUMINT", "name": "visit_MEDIUMINT", "type": null}}, "visit_MEDIUMTEXT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_MEDIUMTEXT", "name": "visit_MEDIUMTEXT", "type": null}}, "visit_NCHAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_NCHAR", "name": "visit_NCHAR", "type": null}}, "visit_NUMERIC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_NUMERIC", "name": "visit_NUMERIC", "type": null}}, "visit_NVARCHAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_NVARCHAR", "name": "visit_NVARCHAR", "type": null}}, "visit_REAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_REAL", "name": "visit_REAL", "type": null}}, "visit_SET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_SET", "name": "visit_SET", "type": null}}, "visit_SMALLINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_SMALLINT", "name": "visit_SMALLINT", "type": null}}, "visit_TEXT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_TEXT", "name": "visit_TEXT", "type": null}}, "visit_TIME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_TIME", "name": "visit_TIME", "type": null}}, "visit_TIMESTAMP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_TIMESTAMP", "name": "visit_TIMESTAMP", "type": null}}, "visit_TINYBLOB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_TINYBLOB", "name": "visit_TINYBLOB", "type": null}}, "visit_TINYINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_TINYINT", "name": "visit_TINYINT", "type": null}}, "visit_TINYTEXT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_TINYTEXT", "name": "visit_TINYTEXT", "type": null}}, "visit_UUID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_UUID", "name": "visit_UUID", "type": null}}, "visit_VARBINARY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_VARBINARY", "name": "visit_VARBINARY", "type": null}}, "visit_VARCHAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_VARCHAR", "name": "visit_VARCHAR", "type": null}}, "visit_YEAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_YEAR", "name": "visit_YEAR", "type": null}}, "visit_enum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_enum", "name": "visit_enum", "type": null}}, "visit_large_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.visit_large_binary", "name": "visit_large_binary", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.base.MySQLTypeCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.NCHAR", "kind": "Gdef"}, "NUMERIC": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.NUMERIC", "kind": "Gdef"}, "NVARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.NVARCHAR", "kind": "Gdef"}, "REAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.REAL", "kind": "Gdef"}, "RESERVED_WORDS_MARIADB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.reserved_words.RESERVED_WORDS_MARIADB", "kind": "Gdef"}, "RESERVED_WORDS_MYSQL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.reserved_words.RESERVED_WORDS_MYSQL", "kind": "Gdef"}, "ReflectionDefaults": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.ReflectionDefaults", "kind": "Gdef"}, "SET": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.enumerated.SET", "kind": "Gdef"}, "SET_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.SET_RE", "name": "SET_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "SMALLINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.SMALLINT", "kind": "Gdef"}, "SQLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.SQLCompiler", "kind": "Gdef"}, "SchemaConst": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.SchemaConst", "kind": "Gdef"}, "TEXT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.TEXT", "kind": "Gdef"}, "TIME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.TIME", "kind": "Gdef"}, "TIMESTAMP": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.TIMESTAMP", "kind": "Gdef"}, "TINYBLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.TINYBLOB", "kind": "Gdef"}, "TINYINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.TINYINT", "kind": "Gdef"}, "TINYTEXT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.TINYTEXT", "kind": "Gdef"}, "UUID": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.UUID", "kind": "Gdef"}, "VARBINARY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.VARBINARY", "kind": "Gdef"}, "VARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.VARCHAR", "kind": "Gdef"}, "YEAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.YEAR", "kind": "Gdef"}, "_DecodingRow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.base._DecodingRow", "name": "_DecodingRow", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.base._DecodingRow", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.base", "mro": ["sqlalchemy.dialects.mysql.base._DecodingRow", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base._DecodingRow.__getattr__", "name": "__getattr__", "type": null}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base._DecodingRow.__getitem__", "name": "__getitem__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "rowproxy", "charset"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.base._DecodingRow.__init__", "name": "__init__", "type": null}}, "_encoding_compat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base._DecodingRow._encoding_compat", "name": "_encoding_compat", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "charset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base._DecodingRow.charset", "name": "charset", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}, "rowproxy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.base._DecodingRow.rowproxy", "name": "rowproxy", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.base._DecodingRow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.base._DecodingRow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FloatType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types._FloatType", "kind": "Gdef"}, "_IntegerType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types._IntegerType", "kind": "Gdef"}, "_MatchType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types._MatchType", "kind": "Gdef"}, "_NumericType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types._NumericType", "kind": "Gdef"}, "_StringType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types._StringType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.base.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_array": {".class": "SymbolTableNode", "cross_ref": "array.array", "kind": "Gdef"}, "_cursor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor", "kind": "Gdef"}, "_info_columns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base._info_columns", "name": "_info_columns", "type": "sqlalchemy.sql.selectable.TableClause"}}, "_reflection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.reflection", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "coercions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.coercions", "kind": "Gdef"}, "colspecs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.colspecs", "name": "colspecs", "type": {".class": "Instance", "args": ["builtins.type", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "compiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler", "kind": "Gdef"}, "compress": {".class": "SymbolTableNode", "cross_ref": "itertools.compress", "kind": "Gdef"}, "default": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.default", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "elements": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "functions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions", "kind": "Gdef"}, "ischema_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.base.ischema_names", "name": "ischema_names", "type": {".class": "Instance", "args": ["builtins.str", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "literal_column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.literal_column", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.log", "kind": "Gdef"}, "operators": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "reflection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection", "kind": "Gdef"}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef"}, "sa_schema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.schema", "kind": "Gdef"}, "sql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql", "kind": "Gdef"}, "sql_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.util", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes", "kind": "Gdef"}, "topological": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.topological", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "visitors": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/base.py"}