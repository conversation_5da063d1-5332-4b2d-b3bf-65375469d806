{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.mysql.types", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BIGINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._IntegerType", "sqlalchemy.sql.sqltypes.BIGINT"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.BIGINT", "name": "BIGINT", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.BIGINT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.BIGINT", "sqlalchemy.dialects.mysql.types._IntegerType", "sqlalchemy.dialects.mysql.types._NumericType", "sqlalchemy.sql.sqltypes.BIGINT", "sqlalchemy.sql.sqltypes.BigInteger", "sqlalchemy.sql.sqltypes.Integer", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "display_width", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.BIGINT.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.BIGINT.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.BIGINT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.BIGINT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.BIT", "name": "BIT", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.BIT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.BIT", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "length"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.BIT.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.BIT.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.types.BIT.length", "name": "length", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "result_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "coltype"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.BIT.result_processor", "name": "result_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.BIT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.BIT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CHAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._StringType", "sqlalchemy.sql.sqltypes.CHAR"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.CHAR", "name": "CHAR", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.CHAR", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.CHAR", "sqlalchemy.dialects.mysql.types._StringType", "sqlalchemy.sql.sqltypes.CHAR", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "length", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.CHAR.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.CHAR.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_adapt_string_for_cast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "type_"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.types.CHAR._adapt_string_for_cast", "name": "_adapt_string_for_cast", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.types.CHAR._adapt_string_for_cast", "name": "_adapt_string_for_cast", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "type_"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.mysql.types.CHAR"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_adapt_string_for_cast of CHAR", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.CHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.CHAR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DATETIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.DATETIME"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.DATETIME", "name": "DATETIME", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.DATETIME", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.DATETIME", "sqlalchemy.sql.sqltypes.DATETIME", "sqlalchemy.sql.sqltypes.DateTime", "sqlalchemy.sql.sqltypes._RenderISO8601NoT", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "timezone", "fsp"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.DATETIME.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.DATETIME.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "fsp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.types.DATETIME.fsp", "name": "fsp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.DATETIME.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.DATETIME", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DECIMAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._NumericType", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.sqltypes.DECIMAL"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.DECIMAL", "name": "DECIMAL", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.DECIMAL", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.DECIMAL", "sqlalchemy.dialects.mysql.types._NumericType", "sqlalchemy.sql.sqltypes.DECIMAL", "sqlalchemy.sql.sqltypes.Numeric", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "precision", "scale", "asdecimal", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.DECIMAL.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.DECIMAL.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.DECIMAL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.DECIMAL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DOUBLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._FloatType", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.sqltypes.DOUBLE"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.DOUBLE", "name": "DOUBLE", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.DOUBLE", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.DOUBLE", "sqlalchemy.dialects.mysql.types._FloatType", "sqlalchemy.dialects.mysql.types._NumericType", "sqlalchemy.sql.sqltypes.DOUBLE", "sqlalchemy.sql.sqltypes.Double", "sqlalchemy.sql.sqltypes.Float", "sqlalchemy.sql.sqltypes.Numeric", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "precision", "scale", "asdecimal", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.DOUBLE.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.DOUBLE.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.DOUBLE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.DOUBLE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FLOAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._FloatType", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.sqltypes.FLOAT"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.FLOAT", "name": "FLOAT", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.FLOAT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.FLOAT", "sqlalchemy.dialects.mysql.types._FloatType", "sqlalchemy.dialects.mysql.types._NumericType", "sqlalchemy.sql.sqltypes.FLOAT", "sqlalchemy.sql.sqltypes.Float", "sqlalchemy.sql.sqltypes.Numeric", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "precision", "scale", "asdecimal", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.FLOAT.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.FLOAT.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "bind_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.FLOAT.bind_processor", "name": "bind_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.FLOAT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.FLOAT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "INTEGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._IntegerType", "sqlalchemy.sql.sqltypes.INTEGER"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.INTEGER", "name": "INTEGER", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.INTEGER", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.INTEGER", "sqlalchemy.dialects.mysql.types._IntegerType", "sqlalchemy.dialects.mysql.types._NumericType", "sqlalchemy.sql.sqltypes.INTEGER", "sqlalchemy.sql.sqltypes.Integer", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "display_width", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.INTEGER.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.INTEGER.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.INTEGER.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.INTEGER", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LONGBLOB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes._Binary"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.LONGBLOB", "name": "LONGBLOB", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.LONGBLOB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.LONGBLOB", "sqlalchemy.sql.sqltypes._Binary", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.LONGBLOB.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.LONGBLOB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.LONGBLOB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LONGTEXT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._StringType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.LONGTEXT", "name": "LONGTEXT", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.LONGTEXT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.LONGTEXT", "sqlalchemy.dialects.mysql.types._StringType", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.LONGTEXT.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.LONGTEXT.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.LONGTEXT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.LONGTEXT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MEDIUMBLOB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes._Binary"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.MEDIUMBLOB", "name": "MEDIUMBLOB", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.MEDIUMBLOB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.MEDIUMBLOB", "sqlalchemy.sql.sqltypes._Binary", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.MEDIUMBLOB.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.MEDIUMBLOB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.MEDIUMBLOB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MEDIUMINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._IntegerType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.MEDIUMINT", "name": "MEDIUMINT", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.MEDIUMINT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.MEDIUMINT", "sqlalchemy.dialects.mysql.types._IntegerType", "sqlalchemy.dialects.mysql.types._NumericType", "sqlalchemy.sql.sqltypes.Integer", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "display_width", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.MEDIUMINT.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.MEDIUMINT.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.MEDIUMINT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.MEDIUMINT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MEDIUMTEXT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._StringType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.MEDIUMTEXT", "name": "MEDIUMTEXT", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.MEDIUMTEXT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.MEDIUMTEXT", "sqlalchemy.dialects.mysql.types._StringType", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.MEDIUMTEXT.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.MEDIUMTEXT.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.MEDIUMTEXT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.MEDIUMTEXT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NCHAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._StringType", "sqlalchemy.sql.sqltypes.NCHAR"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.NCHAR", "name": "NCHAR", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.NCHAR", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.NCHAR", "sqlalchemy.dialects.mysql.types._StringType", "sqlalchemy.sql.sqltypes.NCHAR", "sqlalchemy.sql.sqltypes.Unicode", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "length", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.NCHAR.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.NCHAR.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.NCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.NCHAR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NUMERIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._NumericType", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.sqltypes.NUMERIC"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.NUMERIC", "name": "NUMERIC", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.NUMERIC", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.NUMERIC", "sqlalchemy.dialects.mysql.types._NumericType", "sqlalchemy.sql.sqltypes.NUMERIC", "sqlalchemy.sql.sqltypes.Numeric", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "precision", "scale", "asdecimal", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.NUMERIC.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.NUMERIC.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.NUMERIC.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.NUMERIC", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NVARCHAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._StringType", "sqlalchemy.sql.sqltypes.NVARCHAR"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.NVARCHAR", "name": "NVARCHAR", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.NVARCHAR", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.NVARCHAR", "sqlalchemy.dialects.mysql.types._StringType", "sqlalchemy.sql.sqltypes.NVARCHAR", "sqlalchemy.sql.sqltypes.Unicode", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "length", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.NVARCHAR.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.NVARCHAR.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.NVARCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.NVARCHAR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "REAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._FloatType", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.sqltypes.REAL"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.REAL", "name": "REAL", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.REAL", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.REAL", "sqlalchemy.dialects.mysql.types._FloatType", "sqlalchemy.dialects.mysql.types._NumericType", "sqlalchemy.sql.sqltypes.REAL", "sqlalchemy.sql.sqltypes.Float", "sqlalchemy.sql.sqltypes.Numeric", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "precision", "scale", "asdecimal", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.REAL.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.REAL.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.REAL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.REAL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SMALLINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._IntegerType", "sqlalchemy.sql.sqltypes.SMALLINT"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.SMALLINT", "name": "SMALLINT", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.SMALLINT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.SMALLINT", "sqlalchemy.dialects.mysql.types._IntegerType", "sqlalchemy.dialects.mysql.types._NumericType", "sqlalchemy.sql.sqltypes.SMALLINT", "sqlalchemy.sql.sqltypes.SmallInteger", "sqlalchemy.sql.sqltypes.Integer", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "display_width", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.SMALLINT.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.SMALLINT.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.SMALLINT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.SMALLINT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TEXT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._StringType", "sqlalchemy.sql.sqltypes.TEXT"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.TEXT", "name": "TEXT", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.TEXT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.TEXT", "sqlalchemy.dialects.mysql.types._StringType", "sqlalchemy.sql.sqltypes.TEXT", "sqlalchemy.sql.sqltypes.Text", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "length", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.TEXT.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.TEXT.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.TEXT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.TEXT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.TIME"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.TIME", "name": "TIME", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.TIME", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.TIME", "sqlalchemy.sql.sqltypes.TIME", "sqlalchemy.sql.sqltypes.Time", "sqlalchemy.sql.sqltypes._RenderISO8601NoT", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "timezone", "fsp"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.TIME.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.TIME.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "fsp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.types.TIME.fsp", "name": "fsp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "result_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "coltype"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.TIME.result_processor", "name": "result_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.TIME.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.TIME", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TIMESTAMP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.TIMESTAMP"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.TIMESTAMP", "name": "TIMESTAMP", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.TIMESTAMP", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.TIMESTAMP", "sqlalchemy.sql.sqltypes.TIMESTAMP", "sqlalchemy.sql.sqltypes.DateTime", "sqlalchemy.sql.sqltypes._RenderISO8601NoT", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "timezone", "fsp"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.TIMESTAMP.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.TIMESTAMP.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "fsp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.types.TIMESTAMP.fsp", "name": "fsp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.TIMESTAMP.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.TIMESTAMP", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TINYBLOB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes._Binary"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.TINYBLOB", "name": "TINYBLOB", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.TINYBLOB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.TINYBLOB", "sqlalchemy.sql.sqltypes._Binary", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.TINYBLOB.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.TINYBLOB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.TINYBLOB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TINYINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._IntegerType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.TINYINT", "name": "TINYINT", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.TINYINT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.TINYINT", "sqlalchemy.dialects.mysql.types._IntegerType", "sqlalchemy.dialects.mysql.types._NumericType", "sqlalchemy.sql.sqltypes.Integer", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "display_width", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.TINYINT.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.TINYINT.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.TINYINT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.TINYINT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TINYTEXT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._StringType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.TINYTEXT", "name": "TINYTEXT", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.TINYTEXT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.TINYTEXT", "sqlalchemy.dialects.mysql.types._StringType", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.TINYTEXT.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.TINYTEXT.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.TINYTEXT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.TINYTEXT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VARCHAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._StringType", "sqlalchemy.sql.sqltypes.VARCHAR"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.VARCHAR", "name": "VARCHAR", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.VARCHAR", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.VARCHAR", "sqlalchemy.dialects.mysql.types._StringType", "sqlalchemy.sql.sqltypes.VARCHAR", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "length", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.VARCHAR.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.VARCHAR.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.VARCHAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.VARCHAR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "YEAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types.YEAR", "name": "YEAR", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.YEAR", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types.YEAR", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "display_width"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types.YEAR.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.types.YEAR.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "display_width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.types.YEAR.display_width", "name": "display_width", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types.YEAR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types.YEAR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FloatType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._NumericType", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.sqltypes.Float"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types._FloatType", "name": "_FloatType", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types._FloatType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types._FloatType", "sqlalchemy.dialects.mysql.types._NumericType", "sqlalchemy.sql.sqltypes.Float", "sqlalchemy.sql.sqltypes.Numeric", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "precision", "scale", "asdecimal", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types._FloatType.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types._FloatType.__repr__", "name": "__repr__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types._FloatType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types._FloatType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_IntegerType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types._NumericType", "sqlalchemy.sql.sqltypes.Integer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types._IntegerType", "name": "_IntegerType", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types._IntegerType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types._IntegerType", "sqlalchemy.dialects.mysql.types._NumericType", "sqlalchemy.sql.sqltypes.Integer", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "display_width", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types._IntegerType.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types._IntegerType.__repr__", "name": "__repr__", "type": null}}, "display_width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.types._IntegerType.display_width", "name": "display_width", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types._IntegerType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types._IntegerType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MatchType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.sqltypes.Float"}, "sqlalchemy.sql.sqltypes.MatchType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types._MatchType", "name": "_MatchType", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types._MatchType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types._MatchType", "sqlalchemy.sql.sqltypes.Float", "sqlalchemy.sql.sqltypes.Numeric", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.sqltypes.MatchType", "sqlalchemy.sql.sqltypes.Boolean", "sqlalchemy.sql.sqltypes.SchemaType", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.type_api.Emulated", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types._MatchType.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types._MatchType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types._MatchType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_NumericType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types._NumericType", "name": "_NumericType", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types._NumericType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types._NumericType", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "unsigned", "zerofill", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types._NumericType.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types._NumericType.__repr__", "name": "__repr__", "type": null}}, "unsigned": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.types._NumericType.unsigned", "name": "unsigned", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "zerofill": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.types._NumericType.zerofill", "name": "zerofill", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types._NumericType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types._NumericType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_StringType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.String"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.types._StringType", "name": "_StringType", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mysql.types._StringType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.types", "mro": ["sqlalchemy.dialects.mysql.types._StringType", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "charset", "collation", "ascii", "binary", "unicode", "national", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types._StringType.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.types._StringType.__repr__", "name": "__repr__", "type": null}}, "ascii": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.types._StringType.ascii", "name": "ascii", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "binary": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.types._StringType.binary", "name": "binary", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "charset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.types._StringType.charset", "name": "charset", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "national": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.types._StringType.national", "name": "national", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "unicode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.types._StringType.unicode", "name": "unicode", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.types._StringType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.types._StringType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.types.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.types.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.types.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.types.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.types.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.types.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/types.py"}