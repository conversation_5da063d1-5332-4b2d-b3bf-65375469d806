{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.mssql.aioodbc", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "MSDialectAsync_aioodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.connectors.aioodbc.aiodbcConnector", "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.aioodbc.MSDialectAsync_aioodbc", "name": "MSDialectAsync_aioodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.aioodbc.MSDialectAsync_aioodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.aioodbc", "mro": ["sqlalchemy.dialects.mssql.aioodbc.MSDialectAsync_aioodbc", "sqlalchemy.connectors.aioodbc.aiodbcConnector", "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc", "sqlalchemy.connectors.pyodbc.PyODBCConnector", "sqlalchemy.connectors.Connector", "sqlalchemy.dialects.mssql.base.MSDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "driver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.aioodbc.MSDialectAsync_aioodbc.driver", "name": "driver", "type": "builtins.str"}}, "execution_ctx_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.aioodbc.MSDialectAsync_aioodbc.execution_ctx_cls", "name": "execution_ctx_cls", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["sqlalchemy.dialects.mssql.aioodbc.MSExecutionContext_aioodbc"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.mssql.aioodbc.MSExecutionContext_aioodbc", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.aioodbc.MSDialectAsync_aioodbc.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.aioodbc.MSDialectAsync_aioodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.aioodbc.MSDialectAsync_aioodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MSDialect_pyodbc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc", "kind": "Gdef"}, "MSExecutionContext_aioodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mssql.pyodbc.MSExecutionContext_pyodbc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.aioodbc.MSExecutionContext_aioodbc", "name": "MSExecutionContext_aioodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.aioodbc.MSExecutionContext_aioodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.aioodbc", "mro": ["sqlalchemy.dialects.mssql.aioodbc.MSExecutionContext_aioodbc", "sqlalchemy.dialects.mssql.pyodbc.MSExecutionContext_pyodbc", "sqlalchemy.dialects.mssql.base.MSExecutionContext", "sqlalchemy.engine.default.DefaultExecutionContext", "sqlalchemy.engine.interfaces.ExecutionContext", "builtins.object"], "names": {".class": "SymbolTable", "create_server_side_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.aioodbc.MSExecutionContext_aioodbc.create_server_side_cursor", "name": "create_server_side_cursor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.aioodbc.MSExecutionContext_aioodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.aioodbc.MSExecutionContext_aioodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MSExecutionContext_pyodbc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.pyodbc.MSExecutionContext_pyodbc", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.aioodbc.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.aioodbc.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.aioodbc.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.aioodbc.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.aioodbc.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.aioodbc.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "aiodbcConnector": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.connectors.aioodbc.aiodbcConnector", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "dialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mssql.aioodbc.dialect", "line": 64, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mssql.aioodbc.MSDialectAsync_aioodbc"}}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/aioodbc.py"}