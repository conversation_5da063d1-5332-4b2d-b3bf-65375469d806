{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.mssql.information_schema", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Boolean": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Boolean", "kind": "Gdef"}, "CoerceUnicode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeDecorator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.information_schema.CoerceUnicode", "name": "CoerceUnicode", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.information_schema.CoerceUnicode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.information_schema", "mro": ["sqlalchemy.dialects.mssql.information_schema.CoerceUnicode", "sqlalchemy.sql.type_api.TypeDecorator", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.type_api.ExternalType", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "bind_expression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bindvalue"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.information_schema.CoerceUnicode.bind_expression", "name": "bind_expression", "type": null}}, "cache_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.CoerceUnicode.cache_ok", "name": "cache_ok", "type": "builtins.bool"}}, "impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.CoerceUnicode.impl", "name": "impl", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["length", "collation"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["sqlalchemy.sql.sqltypes.Unicode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.sqltypes.Unicode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.information_schema.CoerceUnicode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.information_schema.CoerceUnicode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "Integer": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Integer", "kind": "Gdef"}, "MetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.MetaData", "kind": "Gdef"}, "NVARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NVARCHAR", "kind": "Gdef"}, "NVarcharSqlVariant": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeDecorator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.information_schema.NVarcharSqlVariant", "name": "NVarcharSqlVariant", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.information_schema.NVarcharSqlVariant", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.information_schema", "mro": ["sqlalchemy.dialects.mssql.information_schema.NVarcharSqlVariant", "sqlalchemy.sql.type_api.TypeDecorator", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.type_api.ExternalType", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "cache_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.NVarcharSqlVariant.cache_ok", "name": "cache_ok", "type": "builtins.bool"}}, "column_expression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "colexpr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.information_schema.NVarcharSqlVariant.column_expression", "name": "column_expression", "type": null}}, "impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.NVarcharSqlVariant.impl", "name": "impl", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["length", "collation"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["sqlalchemy.sql.sqltypes.Unicode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.sqltypes.Unicode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.information_schema.NVarcharSqlVariant.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.information_schema.NVarcharSqlVariant", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Numeric": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Numeric", "kind": "Gdef"}, "NumericSqlVariant": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeDecorator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.information_schema.NumericSqlVariant", "name": "NumericSqlVariant", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.information_schema.NumericSqlVariant", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.information_schema", "mro": ["sqlalchemy.dialects.mssql.information_schema.NumericSqlVariant", "sqlalchemy.sql.type_api.TypeDecorator", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.type_api.ExternalType", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "cache_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.NumericSqlVariant.cache_ok", "name": "cache_ok", "type": "builtins.bool"}}, "column_expression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "colexpr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.information_schema.NumericSqlVariant.column_expression", "name": "column_expression", "type": null}}, "impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.NumericSqlVariant.impl", "name": "impl", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["length", "collation"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["sqlalchemy.sql.sqltypes.Unicode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.sqltypes.Unicode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.information_schema.NumericSqlVariant.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.information_schema.NumericSqlVariant", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "String": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.String", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "TypeDecorator": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeDecorator", "kind": "Gdef"}, "Unicode": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Unicode", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.information_schema.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.information_schema.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.information_schema.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.information_schema.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.information_schema.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.information_schema.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_cast_on_2005": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.information_schema._cast_on_2005", "name": "_cast_on_2005", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.information_schema._cast_on_2005", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.information_schema", "mro": ["sqlalchemy.dialects.mssql.information_schema._cast_on_2005", "sqlalchemy.sql.elements.ColumnElement", "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "sqlalchemy.sql.roles.ColumnArgumentRole", "sqlalchemy.sql.roles.StatementOptionRole", "sqlalchemy.sql.roles.WhereHavingRole", "sqlalchemy.sql.roles.OnClauseRole", "sqlalchemy.sql.roles.BinaryElementRole", "sqlalchemy.sql.roles.OrderByRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.ByOfRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.LimitOffsetRole", "sqlalchemy.sql.roles.DMLColumnRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.DDLExpressionRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.elements.SQLColumnExpression", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bindvalue"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.information_schema._cast_on_2005.__init__", "name": "__init__", "type": null}}, "bindvalue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mssql.information_schema._cast_on_2005.bindvalue", "name": "bindvalue", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.information_schema._cast_on_2005.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.information_schema._cast_on_2005", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_compile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mssql.information_schema._compile", "name": "_compile", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mssql.information_schema._compile", "name": "_compile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compile", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.cast", "kind": "Gdef"}, "column_constraints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.column_constraints", "name": "column_constraints", "type": "sqlalchemy.sql.schema.Table"}}, "columns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.columns", "name": "columns", "type": "sqlalchemy.sql.schema.Table"}}, "compiles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.compiler.compiles", "kind": "Gdef"}, "computed_columns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.computed_columns", "name": "computed_columns", "type": "sqlalchemy.sql.schema.Table"}}, "constraints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.constraints", "name": "constraints", "type": "sqlalchemy.sql.schema.Table"}}, "expression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.expression", "kind": "Gdef"}, "extended_properties": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.extended_properties", "name": "extended_properties", "type": "sqlalchemy.sql.schema.Table"}}, "identity_columns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.identity_columns", "name": "identity_columns", "type": "sqlalchemy.sql.schema.Table"}}, "ischema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.ischema", "name": "ischema", "type": "sqlalchemy.sql.schema.MetaData"}}, "key_constraints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.key_constraints", "name": "key_constraints", "type": "sqlalchemy.sql.schema.Table"}}, "mssql_temp_table_columns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.mssql_temp_table_columns", "name": "mssql_temp_table_columns", "type": "sqlalchemy.sql.schema.Table"}}, "ref_constraints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.ref_constraints", "name": "ref_constraints", "type": "sqlalchemy.sql.schema.Table"}}, "schemata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.schemata", "name": "schemata", "type": "sqlalchemy.sql.schema.Table"}}, "sequences": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.sequences", "name": "sequences", "type": "sqlalchemy.sql.schema.Table"}}, "tables": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.tables", "name": "tables", "type": "sqlalchemy.sql.schema.Table"}}, "views": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.information_schema.views", "name": "views", "type": "sqlalchemy.sql.schema.Table"}}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/information_schema.py"}