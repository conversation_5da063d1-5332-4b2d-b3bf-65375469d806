{"data_mtime": 1751259991, "dep_lines": [943, 944, 943, 954, 955, 956, 958, 959, 960, 961, 964, 965, 967, 968, 986, 989, 990, 947, 949, 951, 953, 954, 971, 933, 935, 936, 937, 938, 939, 941, 947, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 10, 5, 10, 5, 5, 10, 10, 10, 10, 5, 5, 25, 25, 10, 10, 5, 5, 20, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.mssql.information_schema", "sqlalchemy.dialects.mssql.json", "sqlalchemy.dialects.mssql", "sqlalchemy.engine.cursor", "sqlalchemy.engine.default", "sqlalchemy.engine.reflection", "sqlalchemy.sql.coercions", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.expression", "sqlalchemy.sql.roles", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.util", "sqlalchemy.sql._typing", "sqlalchemy.util.typing", "sqlalchemy.sql.dml", "sqlalchemy.sql.selectable", "sqlalchemy.exc", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.types", "__future__", "codecs", "datetime", "operator", "re", "typing", "uuid", "sqlalchemy", "builtins", "_decimal", "_typeshed", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql._selectable_constructors", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.functions", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.schema", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "types", "typing_extensions"], "hash": "30ac997997949b1f45dd63940d50778403d31073", "id": "sqlalchemy.dialects.mssql.base", "ignore_all": true, "interface_hash": "3ad1eada55cbfdd35803fa6330d72ee557562097", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/base.py", "plugin_data": null, "size": 132423, "suppressed": [], "version_id": "1.13.0"}