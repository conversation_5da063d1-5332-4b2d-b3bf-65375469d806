{"data_mtime": 1751259991, "dep_lines": [353, 361, 367, 368, 364, 365, 366, 368, 348, 349, 350, 351, 364, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 20, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.mssql.base", "sqlalchemy.dialects.mssql.json", "sqlalchemy.connectors.pyodbc", "sqlalchemy.engine.cursor", "sqlalchemy.exc", "sqlalchemy.types", "sqlalchemy.util", "sqlalchemy.engine", "datetime", "decimal", "re", "struct", "sqlalchemy", "builtins", "_decimal", "_typeshed", "abc", "importlib", "importlib.machinery", "sqlalchemy.connectors", "sqlalchemy.dialects.mysql", "sqlalchemy.dialects.mysql.base", "sqlalchemy.dialects.oracle", "sqlalchemy.dialects.oracle.base", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql", "sqlalchemy.sql.base", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "types", "typing", "typing_extensions"], "hash": "35234f0a8139da42634bda659322044194277d18", "id": "sqlalchemy.dialects.mssql.pyodbc", "ignore_all": true, "interface_hash": "bda90adcc82301644bb9a04ff313ce97f8cc9077", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/pyodbc.py", "plugin_data": null, "size": 27056, "suppressed": [], "version_id": "1.13.0"}