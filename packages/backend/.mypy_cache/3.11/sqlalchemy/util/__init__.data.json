{".class": "MypyFile", "_fullname": "sqlalchemy.util", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "EMPTY_DICT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.EMPTY_DICT", "kind": "Gdef"}, "EMPTY_SET": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.EMPTY_SET", "kind": "Gdef"}, "EnsureKWArg": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.EnsureKWArg", "kind": "Gdef"}, "FacadeDict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.FacadeDict", "kind": "Gdef"}, "FastIntFlag": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.FastIntFlag", "kind": "Gdef"}, "HasMemoized": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.HasMemoized", "kind": "Gdef"}, "HasMemoized_ro_memoized_attribute": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.HasMemoized_ro_memoized_attribute", "kind": "Gdef"}, "IdentitySet": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._py_collections.IdentitySet", "kind": "Gdef"}, "LRUCache": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.LRUCache", "kind": "Gdef"}, "MemoizedSlots": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.MemoizedSlots", "kind": "Gdef"}, "NONE_SET": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.NONE_SET", "kind": "Gdef"}, "NoneType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.NoneType", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.OrderedDict", "kind": "Gdef"}, "OrderedIdentitySet": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.OrderedIdentitySet", "kind": "Gdef"}, "OrderedProperties": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.OrderedProperties", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._py_collections.OrderedSet", "kind": "Gdef"}, "PluginLoader": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.PluginLoader", "kind": "Gdef"}, "PopulateDict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.PopulateDict", "kind": "Gdef"}, "Properties": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.Properties", "kind": "Gdef"}, "ReadOnlyContainer": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._py_collections.ReadOnlyContainer", "kind": "Gdef"}, "ReadOnlyProperties": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.ReadOnlyProperties", "kind": "Gdef"}, "ScopedRegistry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.ScopedRegistry", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "ThreadLocalRegistry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.ThreadLocalRegistry", "kind": "Gdef"}, "TypingOnly": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.TypingOnly", "kind": "Gdef"}, "UniqueAppender": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.UniqueAppender", "kind": "Gdef"}, "WeakPopulateDict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.WeakPopulateDict", "kind": "Gdef"}, "WeakSequence": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.WeakSequence", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "add_parameter_text": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.add_parameter_text", "kind": "Gdef"}, "anext_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.anext_", "kind": "Gdef"}, "arm": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.arm", "kind": "Gdef"}, "as_interface": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.as_interface", "kind": "Gdef"}, "asbool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.asbool", "kind": "Gdef"}, "asint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.asint", "kind": "Gdef"}, "assert_arg_type": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.assert_arg_type", "kind": "Gdef"}, "attrsetter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.attrsetter", "kind": "Gdef"}, "await_fallback": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.await_fallback", "kind": "Gdef"}, "await_only": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.await_only", "kind": "Gdef"}, "b": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.b", "kind": "Gdef"}, "b64decode": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.b64decode", "kind": "Gdef"}, "b64encode": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.b64encode", "kind": "Gdef"}, "became_legacy_20": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.deprecations.became_legacy_20", "kind": "Gdef"}, "bool_or_str": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.bool_or_str", "kind": "Gdef"}, "chop_traceback": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.chop_traceback", "kind": "Gdef"}, "class_hierarchy": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.class_hierarchy", "kind": "Gdef"}, "classproperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.classproperty", "kind": "Gdef"}, "clsname_as_plain_name": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.clsname_as_plain_name", "kind": "Gdef"}, "cmp": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.cmp", "kind": "Gdef"}, "coerce_generator_arg": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.coerce_generator_arg", "kind": "Gdef"}, "coerce_kw_type": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.coerce_kw_type", "kind": "Gdef"}, "coerce_to_immutabledict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.coerce_to_immutabledict", "kind": "Gdef"}, "column_dict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.column_dict", "kind": "Gdef"}, "column_set": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.column_set", "kind": "Gdef"}, "constructor_copy": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.constructor_copy", "kind": "Gdef"}, "constructor_key": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.constructor_key", "kind": "Gdef"}, "counter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.counter", "kind": "Gdef"}, "cpython": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.cpython", "kind": "Gdef"}, "create_proxy_methods": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.create_proxy_methods", "kind": "Gdef"}, "dataclass_fields": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.dataclass_fields", "kind": "Gdef"}, "decode_backslashreplace": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.decode_backslashreplace", "kind": "Gdef"}, "decode_slice": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.decode_slice", "kind": "Gdef"}, "decorator": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.decorator", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.deprecations.deprecated", "kind": "Gdef"}, "deprecated_cls": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.deprecations.deprecated_cls", "kind": "Gdef"}, "deprecated_params": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.deprecations.deprecated_params", "kind": "Gdef"}, "dictlike_iteritems": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.dictlike_iteritems", "kind": "Gdef"}, "dottedgetter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.dottedgetter", "kind": "Gdef"}, "duck_type_collection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.duck_type_collection", "kind": "Gdef"}, "ellipses_string": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.ellipses_string", "kind": "Gdef"}, "flatten_iterator": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.flatten_iterator", "kind": "Gdef"}, "format_argspec_init": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.format_argspec_init", "kind": "Gdef"}, "format_argspec_plus": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.format_argspec_plus", "kind": "Gdef"}, "generic_fn_descriptor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor", "kind": "Gdef"}, "generic_repr": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.generic_repr", "kind": "Gdef"}, "get_annotations": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.get_annotations", "kind": "Gdef"}, "get_callable_argspec": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.get_callable_argspec", "kind": "Gdef"}, "get_cls_kwargs": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.get_cls_kwargs", "kind": "Gdef"}, "get_func_kwargs": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.get_func_kwargs", "kind": "Gdef"}, "getargspec_init": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.getargspec_init", "kind": "Gdef"}, "greenlet_spawn": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.greenlet_spawn", "kind": "Gdef"}, "has_compiled_ext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.has_compiled_ext", "kind": "Gdef"}, "has_dupes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.has_dupes", "kind": "Gdef"}, "has_intersection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.has_intersection", "kind": "Gdef"}, "has_refcount_gc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.has_refcount_gc", "kind": "Gdef"}, "hybridmethod": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.hybridmethod", "kind": "Gdef"}, "hybridproperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.hybridproperty", "kind": "Gdef"}, "immutabledict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._py_collections.immutabledict", "kind": "Gdef"}, "inject_docstring_text": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.inject_docstring_text", "kind": "Gdef"}, "inspect_getfullargspec": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.inspect_getfullargspec", "kind": "Gdef"}, "is64bit": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.is64bit", "kind": "Gdef"}, "is_exit_exception": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.is_exit_exception", "kind": "Gdef"}, "is_non_string_iterable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.typing.is_non_string_iterable", "kind": "Gdef"}, "iterate_attributes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.iterate_attributes", "kind": "Gdef"}, "local_dataclass_fields": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.local_dataclass_fields", "kind": "Gdef"}, "map_bits": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.map_bits", "kind": "Gdef"}, "md5_hex": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.md5_hex", "kind": "Gdef"}, "memoized_instancemethod": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.memoized_instancemethod", "kind": "Gdef"}, "memoized_property": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.memoized_property", "kind": "Gdef"}, "merge_lists_w_ordering": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.merge_lists_w_ordering", "kind": "Gdef"}, "method_is_overridden": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.method_is_overridden", "kind": "Gdef"}, "methods_equivalent": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.methods_equivalent", "kind": "Gdef"}, "monkeypatch_proxied_specials": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.monkeypatch_proxied_specials", "kind": "Gdef"}, "moved_20": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.deprecations.moved_20", "kind": "Gdef"}, "non_memoized_property": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.non_memoized_property", "kind": "Gdef"}, "only_once": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.only_once", "kind": "Gdef"}, "ordered_column_set": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.ordered_column_set", "kind": "Gdef"}, "osx": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.osx", "kind": "Gdef"}, "parse_user_argument_for_enum": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.parse_user_argument_for_enum", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "portable_instancemethod": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.portable_instancemethod", "kind": "Gdef"}, "preload_module": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.preloaded.preload_module", "kind": "Gdef"}, "preloaded": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.preloaded", "kind": "Gdef"}, "py310": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.py310", "kind": "Gdef"}, "py311": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.py311", "kind": "Gdef"}, "py312": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.py312", "kind": "Gdef"}, "py313": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.py313", "kind": "Gdef"}, "py38": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.py38", "kind": "Gdef"}, "py39": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.py39", "kind": "Gdef"}, "pypy": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.pypy", "kind": "Gdef"}, "quoted_token_parser": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.quoted_token_parser", "kind": "Gdef"}, "ro_memoized_property": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.ro_memoized_property", "kind": "Gdef"}, "ro_non_memoized_property": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.ro_non_memoized_property", "kind": "Gdef"}, "rw_hybridproperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.rw_hybridproperty", "kind": "Gdef"}, "safe_reraise": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.safe_reraise", "kind": "Gdef"}, "set_creation_order": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.set_creation_order", "kind": "Gdef"}, "sort_dictionary": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.sort_dictionary", "kind": "Gdef"}, "string_or_unprintable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.string_or_unprintable", "kind": "Gdef"}, "symbol": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.symbol", "kind": "Gdef"}, "to_column_set": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.to_column_set", "kind": "Gdef"}, "to_list": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.to_list", "kind": "Gdef"}, "to_set": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.to_set", "kind": "Gdef"}, "unbound_method_to_callable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.unbound_method_to_callable", "kind": "Gdef"}, "unique_list": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._py_collections.unique_list", "kind": "Gdef"}, "update_copy": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.update_copy", "kind": "Gdef"}, "update_wrapper": {".class": "SymbolTableNode", "cross_ref": "functools.update_wrapper", "kind": "Gdef"}, "walk_subclasses": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.walk_subclasses", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.warn", "kind": "Gdef"}, "warn_deprecated": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.deprecations.warn_deprecated", "kind": "Gdef"}, "warn_exception": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.warn_exception", "kind": "Gdef"}, "warn_limited": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.warn_limited", "kind": "Gdef"}, "win32": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.win32", "kind": "Gdef"}, "wrap_callable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.wrap_callable", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/util/__init__.py"}