{"data_mtime": 1751259990, "dep_lines": [27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 25, 26, 27, 47, 13, 15, 16, 25, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 10, 5, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.cursor", "sqlalchemy.engine.default", "sqlalchemy.engine.reflection", "sqlalchemy.engine.result", "sqlalchemy.engine.url", "sqlalchemy.orm.attributes", "sqlalchemy.orm.base", "sqlalchemy.orm.clsregistry", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.decl_base", "sqlalchemy.orm.dependency", "sqlalchemy.orm.descriptor_props", "sqlalchemy.orm.properties", "sqlalchemy.orm.relationships", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.orm.strategies", "sqlalchemy.orm.strategy_options", "sqlalchemy.orm.util", "sqlalchemy.sql.default_comparator", "sqlalchemy.sql.dml", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "sqlalchemy.sql.naming", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.traversals", "sqlalchemy.sql.util", "sqlalchemy.dialects", "sqlalchemy.orm", "sqlalchemy.engine", "sqlalchemy.sql", "__future__", "sys", "typing", "sqlalchemy", "builtins", "abc", "importlib", "importlib.machinery", "sqlalchemy.orm.mapper", "types"], "hash": "7e909af04079bfda513033bb76ca94c9cd6d5680", "id": "sqlalchemy.util.preloaded", "ignore_all": true, "interface_hash": "f1285623dc5bfaf8d1402aa953a96937f7c1fa2d", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/util/preloaded.py", "plugin_data": null, "size": 5904, "suppressed": [], "version_id": "1.13.0"}