{"data_mtime": 1751259990, "dep_lines": [48, 49, 50, 51, 48, 52, 13, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 46, 52, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 20, 10, 5, 10, 10, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util._collections", "sqlalchemy.util.compat", "sqlalchemy.util._has_cy", "sqlalchemy.util.typing", "sqlalchemy.util", "sqlalchemy.exc", "__future__", "collections", "enum", "functools", "inspect", "itertools", "operator", "re", "sys", "textwrap", "threading", "types", "typing", "warnings", "sqlalchemy", "builtins", "_collections_abc", "_typeshed", "abc", "importlib", "importlib.machinery", "sqlalchemy.util._py_collections", "typing_extensions"], "hash": "18aced7a6776165594abe00e5472f81818dbc978", "id": "sqlalchemy.util.langhelpers", "ignore_all": true, "interface_hash": "cadad659c47dc601851c98f3b022a6f6064a87fa", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py", "plugin_data": null, "size": 65090, "suppressed": [], "version_id": "1.13.0"}