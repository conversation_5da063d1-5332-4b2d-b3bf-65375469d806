{"data_mtime": 1751259990, "dep_lines": [16, 15, 11, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util.concurrency", "sqlalchemy.engine", "__future__", "collections", "builtins", "abc", "asyncio", "asyncio.locks", "asyncio.mixins", "importlib", "importlib.machinery", "sqlalchemy.engine.interfaces", "sqlalchemy.util", "sqlalchemy.util._concurrency_py3k", "typing"], "hash": "289527d94f2a319e95e53bbfba2f656db58910b1", "id": "sqlalchemy.connectors.asyncio", "ignore_all": true, "interface_hash": "b34747822f782433c2338164e0130b973560273a", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/connectors/asyncio.py", "plugin_data": null, "size": 6138, "suppressed": [], "version_id": "1.13.0"}