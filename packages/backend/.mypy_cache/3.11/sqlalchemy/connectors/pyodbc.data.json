{".class": "MypyFile", "_fullname": "sqlalchemy.connectors.pyodbc", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ConnectArgsType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ConnectArgsType", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "Connector": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.connectors.Connector", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ExecutionContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ExecutionContext", "kind": "Gdef"}, "IsolationLevel": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.IsolationLevel", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PyODBCConnector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.connectors.Connector"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector", "name": "PyODBCConnector", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.connectors.pyodbc", "mro": ["sqlalchemy.connectors.pyodbc.PyODBCConnector", "sqlalchemy.connectors.Connector", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "use_setinputsizes", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "use_setinputsizes", "kw"], "arg_types": ["sqlalchemy.connectors.pyodbc.PyODBCConnector", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyODBCConnector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_dbapi_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector._dbapi_version", "name": "_dbapi_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.connectors.pyodbc.PyODBCConnector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dbapi_version of PyODBCConnector", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.VersionInfoType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_server_version_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector._get_server_version_info", "name": "_get_server_version_info", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["sqlalchemy.connectors.pyodbc.PyODBCConnector", "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_server_version_info of PyODBCConnector", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.VersionInfoType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_dbapi_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "vers"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector._parse_dbapi_version", "name": "_parse_dbapi_version", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "vers"], "arg_types": ["sqlalchemy.connectors.pyodbc.PyODBCConnector", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_dbapi_version of PyODBCConnector", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.VersionInfoType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_connect_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.create_connect_args", "name": "create_connect_args", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["sqlalchemy.connectors.pyodbc.PyODBCConnector", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_connect_args of PyODBCConnector", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ConnectArgsType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dbapi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.dbapi", "name": "dbapi", "type": "types.ModuleType"}}, "default_paramstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.default_paramstyle", "name": "default_paramstyle", "type": "builtins.str"}}, "do_set_input_sizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cursor", "list_of_tuples", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.do_set_input_sizes", "name": "do_set_input_sizes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cursor", "list_of_tuples", "context"], "arg_types": ["sqlalchemy.connectors.pyodbc.PyODBCConnector", "sqlalchemy.engine.interfaces.DBAPICursor", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "sqlalchemy.engine.interfaces.ExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_set_input_sizes of PyODBCConnector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "driver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.driver", "name": "driver", "type": "builtins.str"}}, "fast_executemany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.fast_executemany", "name": "fast_executemany", "type": "builtins.bool"}}, "get_isolation_level_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.get_isolation_level_values", "name": "get_isolation_level_values", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "arg_types": ["sqlalchemy.connectors.pyodbc.PyODBCConnector", "sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_isolation_level_values of PyODBCConnector", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.IsolationLevel"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "import_dbapi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.import_dbapi", "name": "import_dbapi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.connectors.pyodbc.PyODBCConnector"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_dbapi of PyODBCConnector", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.import_dbapi", "name": "import_dbapi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.connectors.pyodbc.PyODBCConnector"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_dbapi of PyODBCConnector", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "e", "connection", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.is_disconnect", "name": "is_disconnect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "e", "connection", "cursor"], "arg_types": ["sqlalchemy.connectors.pyodbc.PyODBCConnector", "builtins.Exception", {".class": "UnionType", "items": ["sqlalchemy.pool.base.PoolProxiedConnection", "sqlalchemy.engine.interfaces.DBAPIConnection", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.DBAPICursor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_disconnect of PyODBCConnector", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pyodbc_driver_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.pyodbc_driver_name", "name": "pyodbc_driver_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "set_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "level"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.set_isolation_level", "name": "set_isolation_level", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "level"], "arg_types": ["sqlalchemy.connectors.pyodbc.PyODBCConnector", "sqlalchemy.engine.interfaces.DBAPIConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.IsolationLevel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_isolation_level of PyODBCConnector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_native_decimal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.supports_native_decimal", "name": "supports_native_decimal", "type": "builtins.bool"}}, "supports_sane_multi_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.supports_sane_multi_rowcount", "name": "supports_sane_multi_rowcount", "type": "builtins.bool"}}, "supports_sane_rowcount_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.supports_sane_rowcount_returning", "name": "supports_sane_rowcount_returning", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.connectors.pyodbc.PyODBCConnector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.connectors.pyodbc.PyODBCConnector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.URL", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.connectors.pyodbc.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.connectors.pyodbc.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.connectors.pyodbc.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.connectors.pyodbc.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.connectors.pyodbc.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.connectors.pyodbc.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "interfaces": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces", "kind": "Gdef"}, "pool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "unquote_plus": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.unquote_plus", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/connectors/pyodbc.py"}