{"data_mtime": 1751259990, "dep_lines": [25, 26, 27, 28, 29, 34, 35, 36, 37, 25, 30, 31, 32, 33, 34, 18, 20, 23, 30, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 10, 10, 5, 5, 20, 10, 10, 10, 10, 20, 5, 5, 10, 20, 5, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.exc", "sqlalchemy.orm.loading", "sqlalchemy.orm.sync", "sqlalchemy.orm.base", "sqlalchemy.engine.cursor", "sqlalchemy.sql.operators", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.future", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "itertools", "operator", "sqlalchemy", "builtins", "abc", "importlib", "importlib.machinery", "typing"], "hash": "e2eaa45bd46c0db206da17c8a6b7437e4b93c068", "id": "sqlalchemy.orm.persistence", "ignore_all": true, "interface_hash": "f4ca0a82be58d8a5b9e78e37a9b575a5c6cf2775", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/persistence.py", "plugin_data": null, "size": 61701, "suppressed": [], "version_id": "1.13.0"}