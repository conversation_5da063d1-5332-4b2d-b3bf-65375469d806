{"data_mtime": 1751259990, "dep_lines": [33, 34, 35, 36, 38, 42, 44, 46, 52, 53, 55, 62, 63, 64, 66, 67, 78, 80, 82, 33, 59, 60, 61, 62, 10, 12, 13, 14, 15, 31, 59, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 10, 5, 25, 25, 25, 5, 10, 10, 10, 20, 5, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.clsregistry", "sqlalchemy.orm.exc", "sqlalchemy.orm.instrumentation", "sqlalchemy.orm._typing", "sqlalchemy.orm.base", "sqlalchemy.orm.descriptor_props", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.properties", "sqlalchemy.orm.util", "sqlalchemy.sql.expression", "sqlalchemy.sql.base", "sqlalchemy.sql.schema", "sqlalchemy.util.topological", "sqlalchemy.util.typing", "sqlalchemy.orm.decl_api", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.orm", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "collections", "dataclasses", "re", "typing", "weakref", "sqlalchemy", "builtins", "_collections_abc", "_typeshed", "_weakref", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.engine", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "threading", "types", "typing_extensions"], "hash": "6522965fc2e68211ad94865694db186729a91569", "id": "sqlalchemy.orm.decl_base", "ignore_all": true, "interface_hash": "13011c6c91e2278a28f9629409b825b55f5fa881", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/decl_base.py", "plugin_data": null, "size": 83343, "suppressed": [], "version_id": "1.13.0"}