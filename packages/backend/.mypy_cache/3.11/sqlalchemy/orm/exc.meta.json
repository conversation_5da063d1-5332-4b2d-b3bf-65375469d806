{"data_mtime": 1751259990, "dep_lines": [19, 26, 28, 20, 21, 10, 12, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.util", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.state", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "abc", "importlib", "importlib.machinery", "sqlalchemy.orm.base", "sqlalchemy.sql", "sqlalchemy.sql.cache_key", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded"], "hash": "3394c08b92d387016d946f39a24d9584674087fd", "id": "sqlalchemy.orm.exc", "ignore_all": true, "interface_hash": "c7eff77d0738432b08e842dcfa4179e7cc0904ae", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/exc.py", "plugin_data": null, "size": 7413, "suppressed": [], "version_id": "1.13.0"}