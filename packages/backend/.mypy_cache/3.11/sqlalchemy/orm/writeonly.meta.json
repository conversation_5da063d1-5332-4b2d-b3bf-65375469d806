{"data_mtime": 1751259990, "dep_lines": [38, 39, 40, 41, 42, 54, 57, 61, 64, 66, 68, 69, 71, 37, 38, 46, 48, 49, 70, 19, 21, 46, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 5, 5, 25, 25, 25, 25, 25, 25, 5, 20, 10, 10, 10, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.relationships", "sqlalchemy.orm.strategies", "sqlalchemy.orm.base", "sqlalchemy.sql.dml", "sqlalchemy.util.typing", "sqlalchemy.orm._typing", "sqlalchemy.orm.collections", "sqlalchemy.orm.mapper", "sqlalchemy.orm.state", "sqlalchemy.orm.util", "sqlalchemy.sql.selectable", "sqlalchemy.sql", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.log", "sqlalchemy.util", "sqlalchemy.event", "__future__", "typing", "sqlalchemy", "builtins", "_weakref", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.orm.query", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "bdecb62b2c3aa0492edb5c43c5f22dda03a16f43", "id": "sqlalchemy.orm.writeonly", "ignore_all": true, "interface_hash": "04d7652bc1d6266eef676183ecff9d670dfd54bd", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/writeonly.py", "plugin_data": null, "size": 22305, "suppressed": [], "version_id": "1.13.0"}