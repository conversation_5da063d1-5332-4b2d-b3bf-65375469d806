{"data_mtime": 1751259990, "dep_lines": [24, 28, 29, 24, 25, 8, 10, 22, 25, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 25, 25, 20, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.util", "sqlalchemy.orm._typing", "sqlalchemy.orm.state", "sqlalchemy.orm", "sqlalchemy.exc", "__future__", "typing", "weakref", "sqlalchemy", "builtins", "_weakref", "abc", "importlib", "importlib.machinery", "sqlalchemy.orm.base"], "hash": "1ae7fc38c5feb78ba26ce86e032f27bb9abc0054", "id": "sqlalchemy.orm.identity", "ignore_all": true, "interface_hash": "0ff064e19a25720c28970924731a354323c35229", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/identity.py", "plugin_data": null, "size": 9249, "suppressed": [], "version_id": "1.13.0"}