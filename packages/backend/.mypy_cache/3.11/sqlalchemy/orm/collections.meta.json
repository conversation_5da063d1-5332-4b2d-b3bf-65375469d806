{"data_mtime": 1751259990, "dep_lines": [128, 131, 132, 133, 136, 138, 142, 129, 130, 106, 108, 109, 110, 126, 129, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 25, 25, 25, 10, 10, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.base", "sqlalchemy.sql.base", "sqlalchemy.util.compat", "sqlalchemy.util.typing", "sqlalchemy.orm.attributes", "sqlalchemy.orm.mapped_collection", "sqlalchemy.orm.state", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "operator", "threading", "typing", "weakref", "sqlalchemy", "builtins", "_typeshed", "_weakref", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "types"], "hash": "0411c1bbd3efeedaee211c74a42d01ba6fadb3e2", "id": "sqlalchemy.orm.collections", "ignore_all": true, "interface_hash": "31ee03b97223c647a278418d3c7f8583c60dbaa5", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/collections.py", "plugin_data": null, "size": 52243, "suppressed": [], "version_id": "1.13.0"}