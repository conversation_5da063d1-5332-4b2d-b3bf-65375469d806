{"data_mtime": 1751259990, "dep_lines": [20, 21, 22, 23, 42, 45, 55, 57, 69, 70, 74, 75, 81, 82, 83, 97, 99, 115, 118, 120, 124, 126, 135, 153, 154, 161, 162, 16, 18, 162, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.exc", "sqlalchemy.orm.mapper", "sqlalchemy.orm.strategy_options", "sqlalchemy.orm._orm_constructors", "sqlalchemy.orm.attributes", "sqlalchemy.orm.base", "sqlalchemy.orm.context", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.decl_base", "sqlalchemy.orm.descriptor_props", "sqlalchemy.orm.dynamic", "sqlalchemy.orm.events", "sqlalchemy.orm.identity", "sqlalchemy.orm.instrumentation", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.loading", "sqlalchemy.orm.mapped_collection", "sqlalchemy.orm.properties", "sqlalchemy.orm.query", "sqlalchemy.orm.relationships", "sqlalchemy.orm.scoping", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.orm.unitofwork", "sqlalchemy.orm.util", "sqlalchemy.orm.writeonly", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "abc", "importlib", "importlib.machinery", "sqlalchemy.sql"], "hash": "44d97d25eb6e104447c92addc5d5ebf4ef2d755a", "id": "sqlalchemy.orm", "ignore_all": true, "interface_hash": "4822deaee3247cb1f70483383f27ee9c84df09ce", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/__init__.py", "plugin_data": null, "size": 8463, "suppressed": [], "version_id": "1.13.0"}