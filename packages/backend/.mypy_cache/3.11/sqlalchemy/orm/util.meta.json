{"data_mtime": 1751259990, "dep_lines": [36, 37, 38, 42, 61, 66, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 84, 86, 87, 88, 104, 106, 108, 109, 36, 67, 68, 69, 70, 71, 110, 9, 11, 12, 13, 14, 15, 34, 67, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 25, 25, 25, 25, 20, 10, 10, 10, 10, 10, 25, 5, 10, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.exc", "sqlalchemy.orm._typing", "sqlalchemy.orm.base", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.path_registry", "sqlalchemy.engine.result", "sqlalchemy.sql.coercions", "sqlalchemy.sql.expression", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.roles", "sqlalchemy.sql.util", "sqlalchemy.sql.visitors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.util.langhelpers", "sqlalchemy.util.typing", "sqlalchemy.orm.context", "sqlalchemy.orm.mapper", "sqlalchemy.orm.query", "sqlalchemy.orm.relationships", "sqlalchemy.orm", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "enum", "functools", "re", "types", "typing", "weakref", "sqlalchemy", "builtins", "_typeshed", "_weakref", "abc", "importlib", "importlib.machinery", "sqlalchemy.engine._py_row", "sqlalchemy.engine.row", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql._py_util", "sqlalchemy.sql._selectable_constructors", "sqlalchemy.sql.operators", "sqlalchemy.sql.schema", "sqlalchemy.sql.traversals", "sqlalchemy.util._py_collections", "sqlalchemy.util.preloaded", "typing_extensions"], "hash": "683634d355468a21b264da2a5c825352b35f47f8", "id": "sqlalchemy.orm.util", "ignore_all": true, "interface_hash": "d4d79f735cf98a18caf99332f992a4e25ce08327", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/util.py", "plugin_data": null, "size": 81021, "suppressed": [], "version_id": "1.13.0"}