{"data_mtime": 1751259990, "dep_lines": [34, 35, 36, 41, 53, 54, 55, 56, 61, 66, 67, 68, 69, 71, 72, 73, 74, 79, 80, 34, 48, 49, 50, 51, 52, 13, 15, 16, 17, 18, 19, 32, 48, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 10, 10, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 10, 10, 10, 5, 5, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.util", "sqlalchemy.orm.base", "sqlalchemy.orm.interfaces", "sqlalchemy.sql.expression", "sqlalchemy.sql.operators", "sqlalchemy.sql.elements", "sqlalchemy.util.typing", "sqlalchemy.orm._typing", "sqlalchemy.orm.context", "sqlalchemy.orm.decl_base", "sqlalchemy.orm.mapper", "sqlalchemy.orm.properties", "sqlalchemy.orm.state", "sqlalchemy.engine.base", "sqlalchemy.engine.row", "sqlalchemy.sql._typing", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.orm", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "dataclasses", "inspect", "itertools", "operator", "typing", "weakref", "sqlalchemy", "builtins", "_operator", "_typeshed", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.engine", "sqlalchemy.engine._py_row", "sqlalchemy.engine.interfaces", "sqlalchemy.event.api", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.instrumentation", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "c5e6743bcaea60a6a79b85de3856f9c25d85819a", "id": "sqlalchemy.orm.descriptor_props", "ignore_all": true, "interface_hash": "aacce2d8f4baec30906fe3e3befe58f33e1ca82f", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/descriptor_props.py", "plugin_data": null, "size": 37244, "suppressed": [], "version_id": "1.13.0"}