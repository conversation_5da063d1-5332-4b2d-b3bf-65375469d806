{".class": "MypyFile", "_fullname": "sqlalchemy.orm.unitofwork", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "DeleteAll": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.unitofwork.PostSortRec"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.unitofwork.DeleteAll", "name": "DeleteAll", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.unitofwork.DeleteAll", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.unitofwork", "mro": ["sqlalchemy.orm.unitofwork.DeleteAll", "sqlalchemy.orm.unitofwork.PostSortRec", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uow", "mapper"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.DeleteAll.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.DeleteAll.__repr__", "name": "__repr__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.unitofwork.DeleteAll.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.unitofwork.DeleteAll.execute", "name": "execute", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.DeleteAll.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "arg_types": ["sqlalchemy.orm.unitofwork.DeleteAll", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of DeleteAll", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.DeleteAll.mapper", "name": "mapper", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "per_state_flush_actions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.DeleteAll.per_state_flush_actions", "name": "per_state_flush_actions", "type": null}}, "sort_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.DeleteAll.sort_key", "name": "sort_key", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.unitofwork.DeleteAll.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.unitofwork.DeleteAll", "values": [], "variance": 0}, "slots": ["disabled", "mapper", "sort_key"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DeleteState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.unitofwork.PostSortRec"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.unitofwork.DeleteState", "name": "DeleteState", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.unitofwork.DeleteState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.unitofwork", "mro": ["sqlalchemy.orm.unitofwork.DeleteState", "sqlalchemy.orm.unitofwork.PostSortRec", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uow", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.DeleteState.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.DeleteState.__repr__", "name": "__repr__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.unitofwork.DeleteState.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "execute_aggregate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uow", "recs"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.unitofwork.DeleteState.execute_aggregate", "name": "execute_aggregate", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.DeleteState.execute_aggregate", "name": "execute_aggregate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "uow", "recs"], "arg_types": ["sqlalchemy.orm.unitofwork.DeleteState", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute_aggregate of DeleteState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.DeleteState.mapper", "name": "mapper", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "sort_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.DeleteState.sort_key", "name": "sort_key", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.DeleteState.state", "name": "state", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.unitofwork.DeleteState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.unitofwork.DeleteState", "values": [], "variance": 0}, "slots": ["disabled", "mapper", "sort_key", "state"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DependencyProcessor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.dependency.DependencyProcessor", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "InstanceState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state.InstanceState", "kind": "Gdef"}, "IterateMappersMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.unitofwork.IterateMappersMixin", "name": "IterateMappersMixin", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.unitofwork.IterateMappersMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.unitofwork", "mro": ["sqlalchemy.orm.unitofwork.IterateMappersMixin", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.unitofwork.IterateMappersMixin.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_mappers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.IterateMappersMixin._mappers", "name": "_mappers", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.unitofwork.IterateMappersMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.unitofwork.IterateMappersMixin", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.Mapper", "kind": "Gdef"}, "MapperProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.MapperProperty", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PostSortRec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.unitofwork.PostSortRec", "name": "PostSortRec", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.unitofwork.PostSortRec", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.unitofwork", "mro": ["sqlalchemy.orm.unitofwork.PostSortRec", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["cls", "uow", "args"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "sqlalchemy.orm.unitofwork.PostSortRec.__new__", "name": "__new__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.unitofwork.PostSortRec.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "execute_aggregate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uow", "recs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.PostSortRec.execute_aggregate", "name": "execute_aggregate", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.unitofwork.PostSortRec.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.unitofwork.PostSortRec", "values": [], "variance": 0}, "slots": ["disabled"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PostUpdateAll": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.unitofwork.PostSortRec"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.unitofwork.PostUpdateAll", "name": "PostUpdateAll", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.unitofwork.PostUpdateAll", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.unitofwork", "mro": ["sqlalchemy.orm.unitofwork.PostUpdateAll", "sqlalchemy.orm.unitofwork.PostSortRec", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "uow", "mapper", "isdelete"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.PostUpdateAll.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.unitofwork.PostUpdateAll.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.unitofwork.PostUpdateAll.execute", "name": "execute", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.PostUpdateAll.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "arg_types": ["sqlalchemy.orm.unitofwork.PostUpdateAll", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of PostUpdateAll", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "isdelete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.PostUpdateAll.isdelete", "name": "isdelete", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "mapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.PostUpdateAll.mapper", "name": "mapper", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "sort_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.PostUpdateAll.sort_key", "name": "sort_key", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.unitofwork.PostUpdateAll.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.unitofwork.PostUpdateAll", "values": [], "variance": 0}, "slots": ["disabled", "isdelete", "mapper", "sort_key"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Preprocess": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.unitofwork.IterateMappersMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.unitofwork.Preprocess", "name": "Preprocess", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.unitofwork.Preprocess", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.unitofwork", "mro": ["sqlalchemy.orm.unitofwork.Preprocess", "sqlalchemy.orm.unitofwork.IterateMappersMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dependency_processor", "fromparent"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.Preprocess.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.unitofwork.Preprocess.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "dependency_processor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.Preprocess.dependency_processor", "name": "dependency_processor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.Preprocess.execute", "name": "execute", "type": null}}, "fromparent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.Preprocess.fromparent", "name": "fromparent", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "processed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sqlalchemy.orm.unitofwork.Preprocess.processed", "name": "processed", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "setup_flush_actions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.Preprocess.setup_flush_actions", "name": "setup_flush_actions", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.unitofwork.Preprocess.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.unitofwork.Preprocess", "values": [], "variance": 0}, "slots": ["dependency_processor", "fromparent", "processed", "setup_flush_actions"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProcessAll": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.unitofwork.IterateMappersMixin", "sqlalchemy.orm.unitofwork.PostSortRec"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.unitofwork.ProcessAll", "name": "ProcessAll", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.unitofwork.ProcessAll", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.unitofwork", "mro": ["sqlalchemy.orm.unitofwork.ProcessAll", "sqlalchemy.orm.unitofwork.IterateMappersMixin", "sqlalchemy.orm.unitofwork.PostSortRec", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "uow", "dependency_processor", "isdelete", "fromparent"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.ProcessAll.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.ProcessAll.__repr__", "name": "__repr__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.unitofwork.ProcessAll.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.ProcessAll._elements", "name": "_elements", "type": null}}, "dependency_processor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.ProcessAll.dependency_processor", "name": "dependency_processor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.ProcessAll.execute", "name": "execute", "type": null}}, "fromparent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.ProcessAll.fromparent", "name": "fromparent", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "isdelete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.ProcessAll.isdelete", "name": "isdelete", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "per_state_flush_actions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.ProcessAll.per_state_flush_actions", "name": "per_state_flush_actions", "type": null}}, "sort_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.ProcessAll.sort_key", "name": "sort_key", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.unitofwork.ProcessAll.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.unitofwork.ProcessAll", "values": [], "variance": 0}, "slots": ["dependency_processor", "disabled", "fromparent", "isdelete", "sort_key"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProcessState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.unitofwork.PostSortRec"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.unitofwork.ProcessState", "name": "ProcessState", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.unitofwork.ProcessState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.unitofwork", "mro": ["sqlalchemy.orm.unitofwork.ProcessState", "sqlalchemy.orm.unitofwork.PostSortRec", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "uow", "dependency_processor", "isdelete", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.ProcessState.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.ProcessState.__repr__", "name": "__repr__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.unitofwork.ProcessState.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "dependency_processor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.ProcessState.dependency_processor", "name": "dependency_processor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "execute_aggregate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uow", "recs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.ProcessState.execute_aggregate", "name": "execute_aggregate", "type": null}}, "isdelete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.ProcessState.isdelete", "name": "isdelete", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "sort_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.ProcessState.sort_key", "name": "sort_key", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.ProcessState.state", "name": "state", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.unitofwork.ProcessState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.unitofwork.ProcessState", "values": [], "variance": 0}, "slots": ["dependency_processor", "disabled", "isdelete", "sort_key", "state"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SaveUpdateAll": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.unitofwork.PostSortRec"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateAll", "name": "SaveUpdateAll", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateAll", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.unitofwork", "mro": ["sqlalchemy.orm.unitofwork.SaveUpdateAll", "sqlalchemy.orm.unitofwork.PostSortRec", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uow", "mapper"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateAll.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateAll.__repr__", "name": "__repr__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateAll.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateAll.execute", "name": "execute", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateAll.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "arg_types": ["sqlalchemy.orm.unitofwork.SaveUpdateAll", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of SaveUpdateAll", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateAll.mapper", "name": "mapper", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "per_state_flush_actions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uow"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateAll.per_state_flush_actions", "name": "per_state_flush_actions", "type": null}}, "sort_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateAll.sort_key", "name": "sort_key", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateAll.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.unitofwork.SaveUpdateAll", "values": [], "variance": 0}, "slots": ["disabled", "mapper", "sort_key"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SaveUpdateState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.unitofwork.PostSortRec"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateState", "name": "SaveUpdateState", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.unitofwork", "mro": ["sqlalchemy.orm.unitofwork.SaveUpdateState", "sqlalchemy.orm.unitofwork.PostSortRec", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uow", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateState.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateState.__repr__", "name": "__repr__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateState.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "execute_aggregate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uow", "recs"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateState.execute_aggregate", "name": "execute_aggregate", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateState.execute_aggregate", "name": "execute_aggregate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "uow", "recs"], "arg_types": ["sqlalchemy.orm.unitofwork.SaveUpdateState", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute_aggregate of SaveUpdateState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateState.mapper", "name": "mapper", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "sort_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateState.sort_key", "name": "sort_key", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateState.state", "name": "state", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.unitofwork.SaveUpdateState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.unitofwork.SaveUpdateState", "values": [], "variance": 0}, "slots": ["disabled", "mapper", "sort_key", "state"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.Session", "kind": "Gdef"}, "SessionTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.SessionTransaction", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "UOWTransaction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction", "name": "UOWTransaction", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.unitofwork", "mro": ["sqlalchemy.orm.unitofwork.UOWTransaction", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "session"], "arg_types": ["sqlalchemy.orm.unitofwork.UOWTransaction", "sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UOWTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_actions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction._generate_actions", "name": "_generate_actions", "type": null}}, "_mapper_for_dep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction._mapper_for_dep", "name": "_mapper_for_dep", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction._mapper_for_dep", "name": "_mapper_for_dep", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_per_mapper_flush_actions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction._per_mapper_flush_actions", "name": "_per_mapper_flush_actions", "type": null}}, "attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.attributes", "name": "attributes", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "cycles": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.cycles", "name": "cycles", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "dependencies": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.dependencies", "name": "dependencies", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "deps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.deps", "name": "deps", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": ["sqlalchemy.orm.dependency.DependencyProcessor"], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "collections.defaultdict"}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.unitofwork.UOWTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of UOWTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter_states_for_dep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dep", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.filter_states_for_dep", "name": "filter_states_for_dep", "type": null}}, "finalize_flush_changes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.finalize_flush_changes", "name": "finalize_flush_changes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.unitofwork.UOWTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finalize_flush_changes of UOWTransaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_attribute_history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "state", "key", "passive"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.get_attribute_history", "name": "get_attribute_history", "type": null}}, "has_dep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "processor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.has_dep", "name": "has_dep", "type": null}}, "has_work": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.has_work", "name": "has_work", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.has_work", "name": "has_work", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.unitofwork.UOWTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_work of UOWTransaction", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_deleted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.is_deleted", "name": "is_deleted", "type": null}}, "mappers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.mappers", "name": "mappers", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "collections.defaultdict"}}}, "memo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "callable_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.memo", "name": "memo", "type": null}}, "post_update_states": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.post_update_states", "name": "post_update_states", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "collections.defaultdict"}}}, "postsort_actions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.postsort_actions", "name": "postsort_actions", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "presort_actions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.presort_actions", "name": "presort_actions", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "register_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "state", "isdelete", "listonly", "cancel_delete", "operation", "prop"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.register_object", "name": "register_object", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "state", "isdelete", "listonly", "cancel_delete", "operation", "prop"], "arg_types": ["sqlalchemy.orm.unitofwork.UOWTransaction", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_object of UOWTransaction", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_post_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "post_update_cols"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.register_post_update", "name": "register_post_update", "type": null}}, "register_preprocessor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "processor", "fromparent"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.register_preprocessor", "name": "register_preprocessor", "type": null}}, "remove_state_actions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.remove_state_actions", "name": "remove_state_actions", "type": null}}, "session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.session", "name": "session", "type": "sqlalchemy.orm.session.Session"}}, "states": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.states", "name": "states", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "states_for_mapper_hierarchy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mapper", "isdelete", "listonly"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.states_for_mapper_hierarchy", "name": "states_for_mapper_hierarchy", "type": null}}, "transaction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.transaction", "name": "transaction", "type": "sqlalchemy.orm.session.SessionTransaction"}}, "was_already_deleted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.was_already_deleted", "name": "was_already_deleted", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.unitofwork.UOWTransaction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.unitofwork.UOWTransaction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.unitofwork.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.unitofwork.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.unitofwork.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.unitofwork.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.unitofwork.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.unitofwork.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "attributes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes", "kind": "Gdef"}, "event": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event", "kind": "Gdef"}, "orm_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef"}, "orm_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util", "kind": "Gdef"}, "topological": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.topological", "kind": "Gdef"}, "track_cascade_events": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["descriptor", "prop"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.unitofwork.track_cascade_events", "name": "track_cascade_events", "type": null}}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/unitofwork.py"}