{"data_mtime": 1751259990, "dep_lines": [29, 30, 33, 34, 38, 39, 40, 41, 43, 45, 29, 31, 32, 33, 11, 13, 14, 15, 16, 17, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 25, 25, 25, 25, 25, 25, 20, 10, 10, 20, 5, 5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.base", "sqlalchemy.orm._typing", "sqlalchemy.sql.visitors", "sqlalchemy.sql.cache_key", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.relationships", "sqlalchemy.orm.util", "sqlalchemy.sql.elements", "sqlalchemy.util.typing", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "functools", "itertools", "logging", "operator", "typing", "sqlalchemy", "builtins", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.query", "sqlalchemy.sql._py_util", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.util.langhelpers", "types", "typing_extensions"], "hash": "8949981604aa880afbb49bd651f48b2a9c9db0db", "id": "sqlalchemy.orm.path_registry", "ignore_all": true, "interface_hash": "db8bfdcae26f73648bb754d4c08bf3e28fa4a089", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/path_registry.py", "plugin_data": null, "size": 25920, "suppressed": [], "version_id": "1.13.0"}