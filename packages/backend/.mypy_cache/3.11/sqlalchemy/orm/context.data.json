{".class": "MypyFile", "_fullname": "sqlalchemy.orm.context", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"ExecutableReturnsRows\" and \"SelectBase\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.selectable.ExecutableReturnsRows", "sqlalchemy.sql.selectable.SelectBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context.<subclass of \"ExecutableReturnsRows\" and \"SelectBase\">", "name": "<subclass of \"ExecutableReturnsRows\" and \"SelectBase\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "sqlalchemy.orm.context.<subclass of \"ExecutableReturnsRows\" and \"SelectBase\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context.<subclass of \"ExecutableReturnsRows\" and \"SelectBase\">", "sqlalchemy.sql.selectable.ExecutableReturnsRows", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.selectable.SelectBase", "sqlalchemy.sql.roles.SelectStatementRole", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.DMLSelectRole", "sqlalchemy.sql.roles.CompoundElementRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.InElementRole", "sqlalchemy.sql.selectable.HasCTE", "sqlalchemy.sql.roles.HasCTERole", "sqlalchemy.sql.selectable.SelectsRows", "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "sqlalchemy.sql.selectable.Selectable", "sqlalchemy.sql.selectable.ReturnsRows", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"ExecutableReturnsRows\" and \"SelectBase\">1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.selectable.ExecutableReturnsRows", "sqlalchemy.sql.selectable.SelectBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context.<subclass of \"ExecutableReturnsRows\" and \"SelectBase\">1", "name": "<subclass of \"ExecutableReturnsRows\" and \"SelectBase\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "sqlalchemy.orm.context.<subclass of \"ExecutableReturnsRows\" and \"SelectBase\">1", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context.<subclass of \"ExecutableReturnsRows\" and \"SelectBase\">1", "sqlalchemy.sql.selectable.ExecutableReturnsRows", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.selectable.SelectBase", "sqlalchemy.sql.roles.SelectStatementRole", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.DMLSelectRole", "sqlalchemy.sql.roles.CompoundElementRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.InElementRole", "sqlalchemy.sql.selectable.HasCTE", "sqlalchemy.sql.roles.HasCTERole", "sqlalchemy.sql.selectable.SelectsRows", "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "sqlalchemy.sql.selectable.Selectable", "sqlalchemy.sql.selectable.ReturnsRows", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractORMCompileState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.CompileState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context.AbstractORMCompileState", "name": "AbstractORMCompileState", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context.AbstractORMCompileState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context.AbstractORMCompileState", "sqlalchemy.sql.base.CompileState", "builtins.object"], "names": {".class": "SymbolTable", "_init_global_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 3], "arg_names": ["self", "statement", "compiler", "toplevel", "process_criteria_for_toplevel"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.AbstractORMCompileState._init_global_attributes", "name": "_init_global_attributes", "type": null}}, "attributes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sqlalchemy.orm.context.AbstractORMCompileState.attributes", "name": "attributes", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "create_for_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.AbstractORMCompileState.create_for_statement", "name": "create_for_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.AbstractORMCompileState"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_for_statement of AbstractORMCompileState", "ret_type": "sqlalchemy.orm.context.AbstractORMCompileState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.AbstractORMCompileState.create_for_statement", "name": "create_for_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.AbstractORMCompileState"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_for_statement of AbstractORMCompileState", "ret_type": "sqlalchemy.orm.context.AbstractORMCompileState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "global_attributes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.AbstractORMCompileState.global_attributes", "name": "global_attributes", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "is_dml_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.AbstractORMCompileState.is_dml_returning", "name": "is_dml_returning", "type": "builtins.bool"}}, "orm_execute_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "conn"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.AbstractORMCompileState.orm_execute_statement", "name": "orm_execute_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "conn"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.AbstractORMCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_execute_statement of AbstractORMCompileState", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.AbstractORMCompileState.orm_execute_statement", "name": "orm_execute_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "conn"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.AbstractORMCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_execute_statement of AbstractORMCompileState", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "orm_pre_session_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "is_pre_event"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.AbstractORMCompileState.orm_pre_session_exec", "name": "orm_pre_session_exec", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.AbstractORMCompileState.orm_pre_session_exec", "name": "orm_pre_session_exec", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "is_pre_event"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.AbstractORMCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_pre_session_exec of AbstractORMCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "orm_setup_cursor_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "result"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.AbstractORMCompileState.orm_setup_cursor_result", "name": "orm_setup_cursor_result", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.AbstractORMCompileState.orm_setup_cursor_result", "name": "orm_setup_cursor_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "result"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.AbstractORMCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_setup_cursor_result of AbstractORMCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context.AbstractORMCompileState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context.AbstractORMCompileState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AliasedClass": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.AliasedClass", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutoflushOnlyORMCompileState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.context.AbstractORMCompileState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context.AutoflushOnlyORMCompileState", "name": "AutoflushOnlyORMCompileState", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context.AutoflushOnlyORMCompileState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context.AutoflushOnlyORMCompileState", "sqlalchemy.orm.context.AbstractORMCompileState", "sqlalchemy.sql.base.CompileState", "builtins.object"], "names": {".class": "SymbolTable", "orm_pre_session_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "is_pre_event"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.AutoflushOnlyORMCompileState.orm_pre_session_exec", "name": "orm_pre_session_exec", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.AutoflushOnlyORMCompileState.orm_pre_session_exec", "name": "orm_pre_session_exec", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "is_pre_event"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.AutoflushOnlyORMCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_pre_session_exec of AutoflushOnlyORMCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "orm_setup_cursor_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "result"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.AutoflushOnlyORMCompileState.orm_setup_cursor_result", "name": "orm_setup_cursor_result", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.AutoflushOnlyORMCompileState.orm_setup_cursor_result", "name": "orm_setup_cursor_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "result"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.AutoflushOnlyORMCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_setup_cursor_result of AutoflushOnlyORMCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context.AutoflushOnlyORMCompileState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context.AutoflushOnlyORMCompileState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Bundle": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.B<PERSON>le", "kind": "Gdef"}, "CacheableOptions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.CacheableOptions", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "CompileState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.CompileState", "kind": "Gdef"}, "CompoundSelectCompileState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.context.AutoflushOnlyORMCompileState", "sqlalchemy.sql.selectable.CompoundSelectState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context.CompoundSelectCompileState", "name": "CompoundSelectCompileState", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context.CompoundSelectCompileState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context.CompoundSelectCompileState", "sqlalchemy.orm.context.AutoflushOnlyORMCompileState", "sqlalchemy.orm.context.AbstractORMCompileState", "sqlalchemy.sql.selectable.CompoundSelectState", "sqlalchemy.sql.base.CompileState", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context.CompoundSelectCompileState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context.CompoundSelectCompileState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CompoundSelectState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.CompoundSelectState", "kind": "Gdef"}, "DMLReturningColFilter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context.DMLReturningColFilter", "name": "DMLReturningColFilter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context.DMLReturningColFilter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context.DMLReturningColFilter", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "col", "as_filter"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.DMLReturningColFilter.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "target_mapper", "immediate_dml_mapper"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.DMLReturningColFilter.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.context.DMLReturningColFilter.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "adapt_check_present": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "col"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.DMLReturningColFilter.adapt_check_present", "name": "adapt_check_present", "type": null}}, "columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.DMLReturningColFilter.columns", "name": "columns", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util._collections.WeakPopulateDict"}}}, "mapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.DMLReturningColFilter.mapper", "name": "mapper", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context.DMLReturningColFilter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context.DMLReturningColFilter", "values": [], "variance": 0}, "slots": ["__weakref__", "columns", "mapper"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef"}, "ExecutableReturnsRows": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.ExecutableReturnsRows", "kind": "Gdef"}, "FromStatement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.elements.GroupedElement", "sqlalchemy.sql.base.Generative", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._TP", "id": 1, "name": "_TP", "namespace": "sqlalchemy.orm.context.FromStatement", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context.FromStatement", "name": "FromStatement", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._TP", "id": 1, "name": "_TP", "namespace": "sqlalchemy.orm.context.FromStatement", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context.FromStatement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context.FromStatement", "sqlalchemy.sql.elements.GroupedElement", "sqlalchemy.sql.base.Generative", "sqlalchemy.sql.selectable.TypedReturnsRows", "sqlalchemy.sql.selectable.ExecutableReturnsRows", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.selectable.ReturnsRows", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "entities", "element", "_adapt_on_names"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.FromStatement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "entities", "element", "_adapt_on_names"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._TP", "id": 1, "name": "_TP", "namespace": "sqlalchemy.orm.context.FromStatement", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnsClauseArgument"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.ExecutableReturnsRows", "sqlalchemy.sql.elements.TextClause"], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FromStatement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.FromStatement.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_adapt_on_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.FromStatement._adapt_on_names", "name": "_adapt_on_names", "type": "builtins.bool"}}, "_all_selected_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context.FromStatement._all_selected_columns", "name": "_all_selected_columns", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.FromStatement._all_selected_columns", "name": "_all_selected_columns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._TP", "id": 1, "name": "_TP", "namespace": "sqlalchemy.orm.context.FromStatement", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_all_selected_columns of FromStatement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_cache_key_traversal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.FromStatement._cache_key_traversal", "name": "_cache_key_traversal", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.visitors.InternalTraversal"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.visitors.InternalTraversal"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_compile_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.FromStatement._compile_options", "name": "_compile_options", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kw"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["sqlalchemy.orm.context.ORMCompileState.default_compile_options"], "def_extras": {"first_arg": "self"}, "fallback": "sqlalchemy.sql.base._MetaOptions", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.orm.context.ORMCompileState.default_compile_options", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compile_state_factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.FromStatement._compile_state_factory", "name": "_compile_state_factory", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["statement_container", "compiler", "kw"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMFromStatementCompileState"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.orm.context.ORMFromStatementCompileState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compiler_dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "compiler", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.FromStatement._compiler_dispatch", "name": "_compiler_dispatch", "type": null}}, "_ensure_disambiguated_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.FromStatement._ensure_disambiguated_names", "name": "_ensure_disambiguated_names", "type": null}}, "_for_update_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.FromStatement._for_update_arg", "name": "_for_update_arg", "type": {".class": "NoneType"}}}, "_inline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context.FromStatement._inline", "name": "_inline", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.FromStatement._inline", "name": "_inline", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._TP", "id": 1, "name": "_TP", "namespace": "sqlalchemy.orm.context.FromStatement", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_inline of FromStatement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_label_style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.FromStatement._label_style", "name": "_label_style", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.SelectLabelStyle", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_raw_columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.FromStatement._raw_columns", "name": "_raw_columns", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_return_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context.FromStatement._return_defaults", "name": "_return_defaults", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.FromStatement._return_defaults", "name": "_return_defaults", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._TP", "id": 1, "name": "_TP", "namespace": "sqlalchemy.orm.context.FromStatement", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_return_defaults of FromStatement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context.FromStatement._returning", "name": "_returning", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.FromStatement._returning", "name": "_returning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._TP", "id": 1, "name": "_TP", "namespace": "sqlalchemy.orm.context.FromStatement", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_returning of FromStatement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_traverse_internals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.FromStatement._traverse_internals", "name": "_traverse_internals", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.visitors.InternalTraversal"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.visitors.InternalTraversal"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "column_descriptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context.FromStatement.column_descriptions", "name": "column_descriptions", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.FromStatement.column_descriptions", "name": "column_descriptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._TP", "id": 1, "name": "_TP", "namespace": "sqlalchemy.orm.context.FromStatement", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column_descriptions of FromStatement", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.FromStatement.element", "name": "element", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.ExecutableReturnsRows", "sqlalchemy.sql.elements.TextClause"], "uses_pep604_syntax": false}}}, "get_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.FromStatement.get_children", "name": "get_children", "type": null}}, "is_from_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.FromStatement.is_from_statement", "name": "is_from_statement", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context.FromStatement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._TP", "id": 1, "name": "_TP", "namespace": "sqlalchemy.orm.context.FromStatement", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_TP"], "typeddict_type": null}}, "Generative": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Generative", "kind": "Gdef"}, "GroupedElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.GroupedElement", "kind": "Gdef"}, "InternalTraversal": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors.InternalTraversal", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "LABEL_STYLE_DISAMBIGUATE_ONLY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_DISAMBIGUATE_ONLY", "kind": "Gdef"}, "LABEL_STYLE_LEGACY_ORM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.LABEL_STYLE_LEGACY_ORM", "name": "LABEL_STYLE_LEGACY_ORM", "type": "sqlalchemy.sql.selectable.SelectLabelStyle"}}, "LABEL_STYLE_NONE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_NONE", "kind": "Gdef"}, "LABEL_STYLE_TABLENAME_PLUS_COL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_TABLENAME_PLUS_COL", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.Mapper", "kind": "Gdef"}, "ORMAdapter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.ORMAdapter", "kind": "Gdef"}, "ORMColumnDescription": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.ORMColumnDescription", "kind": "Gdef"}, "ORMColumnsClauseRole": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.ORMColumnsClauseRole", "kind": "Gdef"}, "ORMCompileState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.context.AbstractORMCompileState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context.ORMCompileState", "name": "ORMCompileState", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context.ORMCompileState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.orm.context.AbstractORMCompileState", "sqlalchemy.sql.base.CompileState", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMCompileState.__init__", "name": "__init__", "type": null}}, "_append_dedupe_col_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "col_collection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMCompileState._append_dedupe_col_collection", "name": "_append_dedupe_col_collection", "type": null}}, "_column_naming_convention": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "label_style", "legacy"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.ORMCompileState._column_naming_convention", "name": "_column_naming_convention", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "label_style", "legacy"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMCompileState"}, "sqlalchemy.sql.selectable.SelectLabelStyle", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_column_naming_convention of ORMCompileState", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable._LabelConventionCallable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMCompileState._column_naming_convention", "name": "_column_naming_convention", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "label_style", "legacy"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMCompileState"}, "sqlalchemy.sql.selectable.SelectLabelStyle", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_column_naming_convention of ORMCompileState", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable._LabelConventionCallable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_create_entities_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "query", "legacy"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.ORMCompileState._create_entities_collection", "name": "_create_entities_collection", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMCompileState._create_entities_collection", "name": "_create_entities_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "query", "legacy"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_entities_collection of ORMCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_create_with_polymorphic_adapter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ext_info", "selectable"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMCompileState._create_with_polymorphic_adapter", "name": "_create_with_polymorphic_adapter", "type": null}}, "_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMCompileState._entities", "name": "_entities", "type": {".class": "Instance", "args": ["sqlalchemy.orm.context._QueryEntity"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_has_mapper_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMCompileState._has_mapper_entities", "name": "_has_mapper_entities", "type": "builtins.bool"}}, "_label_convention": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMCompileState._label_convention", "name": "_label_convention", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable._LabelConventionCallable"}}}, "_lead_mapper_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context.ORMCompileState._lead_mapper_entities", "name": "_lead_mapper_entities", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMCompileState._lead_mapper_entities", "name": "_lead_mapper_entities", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.context.ORMCompileState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_lead_mapper_entities of ORMCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_mapper_loads_polymorphically_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mapper", "adapter"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMCompileState._mapper_loads_polymorphically_with", "name": "_mapper_loads_polymorphically_with", "type": null}}, "_polymorphic_adapters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMCompileState._polymorphic_adapters", "name": "_polymorphic_adapters", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, "sqlalchemy.orm.util.ORMAdapter"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_primary_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMCompileState._primary_entity", "name": "_primary_entity", "type": {".class": "UnionType", "items": ["sqlalchemy.orm.context._QueryEntity", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMCompileState.attributes", "name": "attributes", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "compile_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMCompileState.compile_options", "name": "compile_options", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMCompileState.default_compile_options"}, "sqlalchemy.orm.context.ORMCompileState.default_compile_options"], "uses_pep604_syntax": false}}}, "create_eager_joins": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMCompileState.create_eager_joins", "name": "create_eager_joins", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "create_for_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.orm.context.ORMCompileState.create_for_statement", "name": "create_for_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMCompileState"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_for_statement of ORMCompileState", "ret_type": "sqlalchemy.orm.context.ORMCompileState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMCompileState.create_for_statement", "name": "create_for_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMCompileState"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_for_statement of ORMCompileState", "ret_type": "sqlalchemy.orm.context.ORMCompileState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "current_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMCompileState.current_path", "name": "current_path", "type": "sqlalchemy.orm.path_registry.PathRegistry"}}, "dedupe_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMCompileState.dedupe_columns", "name": "dedupe_columns", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "default_compile_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.CacheableOptions"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context.ORMCompileState.default_compile_options", "name": "default_compile_options", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context.ORMCompileState.default_compile_options", "has_param_spec_type": false, "metaclass_type": "sqlalchemy.sql.base._MetaOptions", "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context.ORMCompileState.default_compile_options", "sqlalchemy.sql.base.CacheableOptions", "sqlalchemy.sql.base.Options", "sqlalchemy.sql.cache_key.HasCacheKey", "builtins.object"], "names": {".class": "SymbolTable", "_bake_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMCompileState.default_compile_options._bake_ok", "name": "_bake_ok", "type": "builtins.bool"}}, "_cache_key_traversal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMCompileState.default_compile_options._cache_key_traversal", "name": "_cache_key_traversal", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.visitors.InternalTraversal"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_current_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMCompileState.default_compile_options._current_path", "name": "_current_path", "type": "sqlalchemy.orm.path_registry.RootRegistry"}}, "_enable_eagerloads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMCompileState.default_compile_options._enable_eagerloads", "name": "_enable_eagerloads", "type": "builtins.bool"}}, "_enable_single_crit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMCompileState.default_compile_options._enable_single_crit", "name": "_enable_single_crit", "type": "builtins.bool"}}, "_for_refresh_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMCompileState.default_compile_options._for_refresh_state", "name": "_for_refresh_state", "type": "builtins.bool"}}, "_for_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMCompileState.default_compile_options._for_statement", "name": "_for_statement", "type": "builtins.bool"}}, "_is_star": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMCompileState.default_compile_options._is_star", "name": "_is_star", "type": "builtins.bool"}}, "_only_load_props": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMCompileState.default_compile_options._only_load_props", "name": "_only_load_props", "type": {".class": "NoneType"}}}, "_render_for_subquery": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMCompileState.default_compile_options._render_for_subquery", "name": "_render_for_subquery", "type": "builtins.bool"}}, "_set_base_alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMCompileState.default_compile_options._set_base_alias", "name": "_set_base_alias", "type": "builtins.bool"}}, "_use_legacy_query_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMCompileState.default_compile_options._use_legacy_query_style", "name": "_use_legacy_query_style", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context.ORMCompileState.default_compile_options.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context.ORMCompileState.default_compile_options", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "get_column_descriptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "statement"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.ORMCompileState.get_column_descriptions", "name": "get_column_descriptions", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMCompileState.get_column_descriptions", "name": "get_column_descriptions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "statement"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_column_descriptions of ORMCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "global_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMCompileState.global_attributes", "name": "global_attributes", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "orm_pre_session_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "is_pre_event"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.ORMCompileState.orm_pre_session_exec", "name": "orm_pre_session_exec", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMCompileState.orm_pre_session_exec", "name": "orm_pre_session_exec", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "is_pre_event"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_pre_session_exec of ORMCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "orm_setup_cursor_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "result"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.ORMCompileState.orm_setup_cursor_result", "name": "orm_setup_cursor_result", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMCompileState.orm_setup_cursor_result", "name": "orm_setup_cursor_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "session", "statement", "params", "execution_options", "bind_arguments", "result"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "orm_setup_cursor_result of ORMCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "primary_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMCompileState.primary_columns", "name": "primary_columns", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "secondary_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMCompileState.secondary_columns", "name": "secondary_columns", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "select_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMCompileState.select_statement", "name": "select_statement", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}}}, "statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMCompileState.statement", "name": "statement", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}}}, "use_legacy_query_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMCompileState.use_legacy_query_style", "name": "use_legacy_query_style", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context.ORMCompileState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context.ORMCompileState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ORMFromStatementCompileState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.context.ORMCompileState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState", "name": "ORMFromStatementCompileState", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context.ORMFromStatementCompileState", "sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.orm.context.AbstractORMCompileState", "sqlalchemy.sql.base.CompileState", "builtins.object"], "names": {".class": "SymbolTable", "_adapt_col_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cols", "current_adapter"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState._adapt_col_list", "name": "_adapt_col_list", "type": null}}, "_from_obj_alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState._from_obj_alias", "name": "_from_obj_alias", "type": {".class": "NoneType"}}}, "_get_current_adapter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState._get_current_adapter", "name": "_get_current_adapter", "type": null}}, "_has_mapper_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState._has_mapper_entities", "name": "_has_mapper_entities", "type": "builtins.bool"}}, "_has_orm_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState._has_orm_entities", "name": "_has_orm_entities", "type": "builtins.bool"}}, "compound_eager_adapter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState.compound_eager_adapter", "name": "compound_eager_adapter", "type": {".class": "NoneType"}}}, "create_for_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement_container", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState.create_for_statement", "name": "create_for_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement_container", "compiler", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMFromStatementCompileState"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_for_statement of ORMFromStatementCompileState", "ret_type": "sqlalchemy.orm.context.ORMFromStatementCompileState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState.create_for_statement", "name": "create_for_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement_container", "compiler", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMFromStatementCompileState"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_for_statement of ORMFromStatementCompileState", "ret_type": "sqlalchemy.orm.context.ORMFromStatementCompileState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "dml_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState.dml_table", "name": "dml_table", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.dml._DMLTableElement"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "eager_adding_joins": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState.eager_adding_joins", "name": "eager_adding_joins", "type": "builtins.bool"}}, "eager_joins": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState.eager_joins", "name": "eager_joins", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "extra_criteria_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState.extra_criteria_entities", "name": "extra_criteria_entities", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "multi_row_eager_loaders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState.multi_row_eager_loaders", "name": "multi_row_eager_loaders", "type": "builtins.bool"}}, "requested_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState.requested_statement", "name": "requested_statement", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.SelectBase", "sqlalchemy.sql.elements.TextClause", "sqlalchemy.sql.dml.UpdateBase"], "uses_pep604_syntax": false}}}, "setup_dml_returning_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dml_mapper"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState.setup_dml_returning_compile_state", "name": "setup_dml_returning_compile_state", "type": null}}, "statement_container": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState.statement_container", "name": "statement_container", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context.ORMFromStatementCompileState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context.ORMFromStatementCompileState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ORMSelectCompileState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.sql.selectable.SelectState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context.ORMSelectCompileState", "name": "ORMSelectCompileState", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context.ORMSelectCompileState", "sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.orm.context.AbstractORMCompileState", "sqlalchemy.sql.selectable.SelectState", "sqlalchemy.util.langhelpers.MemoizedSlots", "sqlalchemy.sql.base.CompileState", "builtins.object"], "names": {".class": "SymbolTable", "_adapt_col_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cols", "current_adapter"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._adapt_col_list", "name": "_adapt_col_list", "type": null}}, "_adapt_polymorphic_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "element"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._adapt_polymorphic_element", "name": "_adapt_polymorphic_element", "type": null}}, "_adjust_for_extra_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._adjust_for_extra_criteria", "name": "_adjust_for_extra_criteria", "type": null}}, "_all_equivs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._all_equivs", "name": "_all_equivs", "type": null}}, "_already_joined_edges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._already_joined_edges", "name": "_already_joined_edges", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_compound_eager_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._compound_eager_statement", "name": "_compound_eager_statement", "type": null}}, "_create_entities_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "query", "legacy"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._create_entities_collection", "name": "_create_entities_collection", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._create_entities_collection", "name": "_create_entities_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "query", "legacy"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMSelectCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_entities_collection of ORMSelectCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_dump_option_struct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._dump_option_struct", "name": "_dump_option_struct", "type": null}}, "_entity_zero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._entity_zero", "name": "_entity_zero", "type": null}}, "_for_update_arg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._for_update_arg", "name": "_for_update_arg", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.ForUpdateArg", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_from_obj_alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._from_obj_alias", "name": "_from_obj_alias", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_get_current_adapter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._get_current_adapter", "name": "_get_current_adapter", "type": null}}, "_get_extra_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ext_info"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._get_extra_criteria", "name": "_get_extra_criteria", "type": null}}, "_get_select_from_alias_from_obj": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "from_obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._get_select_from_alias_from_obj", "name": "_get_select_from_alias_from_obj", "type": null}}, "_has_mapper_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._has_mapper_entities", "name": "_has_mapper_entities", "type": "builtins.bool"}}, "_has_orm_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._has_orm_entities", "name": "_has_orm_entities", "type": "builtins.bool"}}, "_having_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._having_criteria", "name": "_having_criteria", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "entities_collection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._join", "name": "_join", "type": null}}, "_join_check_and_adapt_right_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "left", "right", "onclause", "prop"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._join_check_and_adapt_right_side", "name": "_join_check_and_adapt_right_side", "type": null}}, "_join_determine_implicit_left_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "entities_collection", "left", "right", "onclause"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._join_determine_implicit_left_side", "name": "_join_determine_implicit_left_side", "type": null}}, "_join_entities": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._join_entities", "name": "_join_entities", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_join_left_to_right": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "entities_collection", "left", "right", "onclause", "prop", "outerjoin", "full"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._join_left_to_right", "name": "_join_left_to_right", "type": null}}, "_join_place_explicit_left_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "entities_collection", "left"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._join_place_explicit_left_side", "name": "_join_place_explicit_left_side", "type": null}}, "_mapper_zero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._mapper_zero", "name": "_mapper_zero", "type": null}}, "_memoized_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._memoized_entities", "name": "_memoized_entities", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "_only_entity_zero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "rationale"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._only_entity_zero", "name": "_only_entity_zero", "type": null}}, "_only_full_mapper_zero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON>ame"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._only_full_mapper_zero", "name": "_only_full_mapper_zero", "type": null}}, "_select_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._select_args", "name": "_select_args", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._select_args", "name": "_select_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.context.ORMSelectCompileState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_select_args of ORMSelectCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_select_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "raw_columns", "from_obj", "where_criteria", "having_criteria", "label_style", "order_by", "for_update", "hints", "statement_hints", "correlate", "correlate_except", "limit_clause", "offset_clause", "fetch_clause", "fetch_clause_options", "distinct", "distinct_on", "prefixes", "suffixes", "group_by", "independent_ctes", "independent_ctes_opts"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._select_statement", "name": "_select_statement", "type": null}}, "_set_select_from_alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._set_select_from_alias", "name": "_set_select_from_alias", "type": null}}, "_setup_for_generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._setup_for_generate", "name": "_setup_for_generate", "type": null}}, "_should_nest_selectable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._should_nest_selectable", "name": "_should_nest_selectable", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._should_nest_selectable", "name": "_should_nest_selectable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.context.ORMSelectCompileState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_nest_selectable of ORMSelectCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_simple_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._simple_statement", "name": "_simple_statement", "type": null}}, "_where_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState._where_criteria", "name": "_where_criteria", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "all_selected_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "statement"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.all_selected_columns", "name": "all_selected_columns", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.all_selected_columns", "name": "all_selected_columns", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "statement"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMSelectCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "all_selected_columns of ORMSelectCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "compound_eager_adapter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.compound_eager_adapter", "name": "compound_eager_adapter", "type": {".class": "NoneType"}}}, "correlate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.correlate", "name": "correlate", "type": {".class": "NoneType"}}}, "correlate_except": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.correlate_except", "name": "correlate_except", "type": {".class": "NoneType"}}}, "create_for_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.create_for_statement", "name": "create_for_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMSelectCompileState"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_for_statement of ORMSelectCompileState", "ret_type": "sqlalchemy.orm.context.ORMSelectCompileState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.create_for_statement", "name": "create_for_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "statement", "compiler", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMSelectCompileState"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_for_statement of ORMSelectCompileState", "ret_type": "sqlalchemy.orm.context.ORMSelectCompileState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "determine_last_joined_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "statement"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.determine_last_joined_entity", "name": "determine_last_joined_entity", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.determine_last_joined_entity", "name": "determine_last_joined_entity", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "statement"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMSelectCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "determine_last_joined_entity of ORMSelectCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "distinct": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.distinct", "name": "distinct", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "uses_pep604_syntax": false}}}, "distinct_on": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.distinct_on", "name": "distinct_on", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "eager_adding_joins": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.eager_adding_joins", "name": "eager_adding_joins", "type": "builtins.bool"}}, "eager_order_by": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.eager_order_by", "name": "eager_order_by", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}}}, "from_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "statement", "from_statement"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.from_statement", "name": "from_statement", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.from_statement", "name": "from_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "statement", "from_statement"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMSelectCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_statement of ORMSelectCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_columns_clause_froms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "statement"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.get_columns_clause_froms", "name": "get_columns_clause_froms", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.get_columns_clause_froms", "name": "get_columns_clause_froms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "statement"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context.ORMSelectCompileState"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_columns_clause_froms of ORMSelectCompileState", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "group_by": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.group_by", "name": "group_by", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "multi_row_eager_loaders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.multi_row_eager_loaders", "name": "multi_row_eager_loaders", "type": "builtins.bool"}}, "order_by": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.order_by", "name": "order_by", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context.ORMSelectCompileState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context.ORMSelectCompileState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ORMStatementAdapter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.ORMStatementAdapter", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Options": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Options", "kind": "Gdef"}, "OrmExecuteOptionsParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter", "kind": "Gdef"}, "PathRegistry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry.PathRegistry", "kind": "Gdef"}, "PostLoad": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.loading.PostLoad", "kind": "Gdef"}, "Query": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.query.Query", "kind": "Gdef"}, "QueryContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context.QueryContext", "name": "QueryContext", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context.QueryContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context.QueryContext", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "compile_state", "statement", "user_passed_query", "params", "session", "load_options", "execution_options", "bind_arguments"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.QueryContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "compile_state", "statement", "user_passed_query", "params", "session", "load_options", "execution_options", "bind_arguments"], "arg_types": ["sqlalchemy.orm.context.QueryContext", "sqlalchemy.sql.base.CompileState", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, "sqlalchemy.orm.session.Session", {".class": "UnionType", "items": [{".class": "TypeType", "item": "sqlalchemy.orm.context.QueryContext.default_load_options"}, "sqlalchemy.orm.context.QueryContext.default_load_options"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QueryContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.context.QueryContext.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_get_top_level_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context.QueryContext._get_top_level_context", "name": "_get_top_level_context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.context.QueryContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_top_level_context of QueryContext", "ret_type": "sqlalchemy.orm.context.QueryContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attributes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.attributes", "name": "attributes", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "autoflush": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.autoflush", "name": "autoflush", "type": "builtins.bool"}}, "bind_arguments": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.bind_arguments", "name": "bind_arguments", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "uses_pep604_syntax": false}}}, "compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.QueryContext.compile_state", "name": "compile_state", "type": "sqlalchemy.orm.context.ORMCompileState"}}, "default_load_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.Options"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context.QueryContext.default_load_options", "name": "default_load_options", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context.QueryContext.default_load_options", "has_param_spec_type": false, "metaclass_type": "sqlalchemy.sql.base._MetaOptions", "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context.QueryContext.default_load_options", "sqlalchemy.sql.base.Options", "builtins.object"], "names": {".class": "SymbolTable", "_autoflush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.QueryContext.default_load_options._autoflush", "name": "_autoflush", "type": "builtins.bool"}}, "_identity_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.QueryContext.default_load_options._identity_token", "name": "_identity_token", "type": {".class": "NoneType"}}}, "_invoke_all_eagers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.QueryContext.default_load_options._invoke_all_eagers", "name": "_invoke_all_eagers", "type": "builtins.bool"}}, "_is_user_refresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.QueryContext.default_load_options._is_user_refresh", "name": "_is_user_refresh", "type": "builtins.bool"}}, "_lazy_loaded_from": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.QueryContext.default_load_options._lazy_loaded_from", "name": "_lazy_loaded_from", "type": {".class": "NoneType"}}}, "_legacy_uniquing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.QueryContext.default_load_options._legacy_uniquing", "name": "_legacy_uniquing", "type": "builtins.bool"}}, "_only_return_tuples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.QueryContext.default_load_options._only_return_tuples", "name": "_only_return_tuples", "type": "builtins.bool"}}, "_populate_existing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.QueryContext.default_load_options._populate_existing", "name": "_populate_existing", "type": "builtins.bool"}}, "_refresh_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.QueryContext.default_load_options._refresh_state", "name": "_refresh_state", "type": {".class": "NoneType"}}}, "_sa_top_level_orm_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.QueryContext.default_load_options._sa_top_level_orm_context", "name": "_sa_top_level_orm_context", "type": {".class": "NoneType"}}}, "_version_check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.QueryContext.default_load_options._version_check", "name": "_version_check", "type": "builtins.bool"}}, "_yield_per": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context.QueryContext.default_load_options._yield_per", "name": "_yield_per", "type": {".class": "NoneType"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context.QueryContext.default_load_options.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context.QueryContext.default_load_options", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "execution_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.execution_options", "name": "execution_options", "type": {".class": "UnionType", "items": [{".class": "TypedDictType", "fallback": "sqlalchemy.orm._typing._OrmKnownExecutionOptions", "items": [["compiled_cache", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.CompiledCacheType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["logging_token", "builtins.str"], ["isolation_level", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.IsolationLevel"}], ["no_parameters", "builtins.bool"], ["stream_results", "builtins.bool"], ["max_row_buffer", "builtins.int"], ["yield_per", "builtins.int"], ["insertmanyvalues_page_size", "builtins.int"], ["schema_translate_map", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.SchemaTranslateMapType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["preserve_rowcount", "builtins.bool"], ["populate_existing", "builtins.bool"], ["autoflush", "builtins.bool"], ["synchronize_session", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._orm_types.SynchronizeSessionArgument"}], ["dml_strategy", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._orm_types.DMLStrategyArgument"}], ["is_delete_using", "builtins.bool"], ["is_update_from", "builtins.bool"], ["render_nulls", "builtins.bool"]], "readonly_keys": [], "required_keys": []}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "uses_pep604_syntax": false}}}, "identity_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.identity_token", "name": "identity_token", "type": {".class": "NoneType"}}}, "invoke_all_eagers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.invoke_all_eagers", "name": "invoke_all_eagers", "type": "builtins.bool"}}, "load_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.load_options", "name": "load_options", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "sqlalchemy.orm.context.QueryContext.default_load_options"}, "sqlalchemy.orm.context.QueryContext.default_load_options"], "uses_pep604_syntax": false}}}, "loaders_require_buffering": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.loaders_require_buffering", "name": "loaders_require_buffering", "type": "builtins.bool"}}, "loaders_require_uniquing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.loaders_require_uniquing", "name": "loaders_require_uniquing", "type": "builtins.bool"}}, "params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.params", "name": "params", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}}}, "populate_existing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.populate_existing", "name": "populate_existing", "type": "builtins.bool"}}, "post_load_paths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.QueryContext.post_load_paths", "name": "post_load_paths", "type": {".class": "Instance", "args": ["sqlalchemy.orm.path_registry.PathRegistry", "sqlalchemy.orm.loading.PostLoad"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "propagated_loader_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.propagated_loader_options", "name": "propagated_loader_options", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.query", "name": "query", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}}}, "refresh_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.refresh_state", "name": "refresh_state", "type": {".class": "NoneType"}}}, "runid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context.QueryContext.runid", "name": "runid", "type": "builtins.int"}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.session", "name": "session", "type": "sqlalchemy.orm.session.Session"}}, "top_level_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.top_level_context", "name": "top_level_context", "type": {".class": "NoneType"}}}, "user_passed_query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.user_passed_query", "name": "user_passed_query", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}}}, "version_check": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.version_check", "name": "version_check", "type": "builtins.bool"}}, "yield_per": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context.QueryContext.yield_per", "name": "yield_per", "type": {".class": "NoneType"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context.QueryContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context.QueryContext", "values": [], "variance": 0}, "slots": ["attributes", "autoflush", "bind_arguments", "compile_state", "create_eager_joins", "execution_options", "identity_token", "invoke_all_eagers", "load_options", "loaders_require_buffering", "loaders_require_uniquing", "params", "partials", "populate_existing", "post_load_paths", "propagated_loader_options", "query", "refresh_state", "runid", "session", "top_level_context", "user_passed_query", "version_check", "yield_per"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Result": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.Result", "kind": "Gdef"}, "SQLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.SQLCompiler", "kind": "Gdef"}, "Select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Select", "kind": "Gdef"}, "SelectBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.SelectBase", "kind": "Gdef"}, "SelectLabelStyle": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.SelectLabelStyle", "kind": "Gdef"}, "SelectState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.SelectState", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.Session", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedReturnsRows": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TypedReturnsRows", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UpdateBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.UpdateBase", "kind": "Gdef"}, "_BindArguments": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session._BindArguments", "kind": "Gdef"}, "_BundleEntity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.context._QueryEntity"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context._BundleEntity", "name": "_BundleEntity", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context._BundleEntity", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context._BundleEntity", "sqlalchemy.orm.context._QueryEntity", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "compile_state", "expr", "entities_collection", "is_current_entities", "setup_entities", "parent_bundle"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._BundleEntity.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.context._BundleEntity.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._BundleEntity._entities", "name": "_entities", "type": {".class": "Instance", "args": ["sqlalchemy.orm.context._QueryEntity"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_extra_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context._BundleEntity._extra_entities", "name": "_extra_entities", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_label_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._BundleEntity._label_name", "name": "_label_name", "type": "builtins.str"}}, "bundle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._BundleEntity.bundle", "name": "bundle", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.util.B<PERSON>le"}}}, "corresponds_to": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._BundleEntity.corresponds_to", "name": "corresponds_to", "type": null}}, "entity_zero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context._BundleEntity.entity_zero", "name": "entity_zero", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context._BundleEntity.entity_zero", "name": "entity_zero", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.context._BundleEntity"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_zero of _BundleEntity", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "entity_zero_or_selectable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context._BundleEntity.entity_zero_or_selectable", "name": "entity_zero_or_selectable", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context._BundleEntity.entity_zero_or_selectable", "name": "entity_zero_or_selectable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.context._BundleEntity"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_zero_or_selectable of _BundleEntity", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._BundleEntity.expr", "name": "expr", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.util.B<PERSON>le"}}}, "mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context._BundleEntity.mapper", "name": "mapper", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context._BundleEntity.mapper", "name": "mapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.context._BundleEntity"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapper of _BundleEntity", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "result"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._BundleEntity.row_processor", "name": "row_processor", "type": null}}, "setup_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._BundleEntity.setup_compile_state", "name": "setup_compile_state", "type": null}}, "setup_dml_returning_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "adapter"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._BundleEntity.setup_dml_returning_compile_state", "name": "setup_dml_returning_compile_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "adapter"], "arg_types": ["sqlalchemy.orm.context._BundleEntity", "sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.orm.context.DMLReturningColFilter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_dml_returning_compile_state of _BundleEntity", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_single_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._BundleEntity.supports_single_entity", "name": "supports_single_entity", "type": "builtins.bool"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._BundleEntity.type", "name": "type", "type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context._BundleEntity.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context._BundleEntity", "values": [], "variance": 0}, "slots": ["_entities", "_label_name", "bundle", "expr", "supports_single_entity", "type"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ColumnEntity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.context._QueryEntity"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context._ColumnEntity", "name": "_ColumnEntity", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context._ColumnEntity", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context._ColumnEntity", "sqlalchemy.orm.context._QueryEntity", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.context._ColumnEntity.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_for_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["cls", "compile_state", "columns", "entities_collection", "raw_column_index", "is_current_entities", "parent_bundle"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context._ColumnEntity._for_columns", "name": "_for_columns", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context._ColumnEntity._for_columns", "name": "_for_columns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["cls", "compile_state", "columns", "entities_collection", "raw_column_index", "is_current_entities", "parent_bundle"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context._ColumnEntity"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_for_columns of _ColumnEntity", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_non_hashable_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context._ColumnEntity._non_hashable_value", "name": "_non_hashable_value", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context._ColumnEntity._non_hashable_value", "name": "_non_hashable_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.context._ColumnEntity"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_non_hashable_value of _ColumnEntity", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_null_column_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context._ColumnEntity._null_column_type", "name": "_null_column_type", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context._ColumnEntity._null_column_type", "name": "_null_column_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.context._ColumnEntity"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_null_column_type of _ColumnEntity", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_row_processor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._ColumnEntity._row_processor", "name": "_row_processor", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "result"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._ColumnEntity.row_processor", "name": "row_processor", "type": null}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context._ColumnEntity.type", "name": "type", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context._ColumnEntity.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.context._ColumnEntity"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "type of _ColumnEntity", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context._ColumnEntity.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context._ColumnEntity", "values": [], "variance": 0}, "slots": ["_fetch_column", "_row_processor", "raw_column_index", "translate_raw_column"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ColumnsClauseArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._ColumnsClauseArgument", "kind": "Gdef"}, "_CoreSingleExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams", "kind": "Gdef"}, "_DMLTableElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml._DMLTableElement", "kind": "Gdef"}, "_EMPTY_DICT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context._EMPTY_DICT", "name": "_EMPTY_DICT", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "_IdentityTokenEntity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.context._ORMColumnEntity"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context._IdentityTokenEntity", "name": "_IdentityTokenEntity", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context._IdentityTokenEntity", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context._IdentityTokenEntity", "sqlalchemy.orm.context._ORMColumnEntity", "sqlalchemy.orm.context._ColumnEntity", "sqlalchemy.orm.context._QueryEntity", "builtins.object"], "names": {".class": "SymbolTable", "row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "result"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._IdentityTokenEntity.row_processor", "name": "row_processor", "type": null}}, "setup_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._IdentityTokenEntity.setup_compile_state", "name": "setup_compile_state", "type": null}}, "translate_raw_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context._IdentityTokenEntity.translate_raw_column", "name": "translate_raw_column", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context._IdentityTokenEntity.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context._IdentityTokenEntity", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_InternalEntityType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._InternalEntityType", "kind": "Gdef"}, "_JoinTargetElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable._JoinTargetElement", "kind": "Gdef"}, "_LabelConventionCallable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable._LabelConventionCallable", "kind": "Gdef"}, "_MapperEntity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.context._QueryEntity"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context._MapperEntity", "name": "_MapperEntity", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context._MapperEntity", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context._MapperEntity", "sqlalchemy.orm.context._QueryEntity", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "compile_state", "entity", "entities_collection", "is_current_entities"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._MapperEntity.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.context._MapperEntity.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_extra_entities": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._MapperEntity._extra_entities", "name": "_extra_entities", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_get_entity_clauses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._MapperEntity._get_entity_clauses", "name": "_get_entity_clauses", "type": null}}, "_label_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._MapperEntity._label_name", "name": "_label_name", "type": "builtins.str"}}, "_non_hashable_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context._MapperEntity._non_hashable_value", "name": "_non_hashable_value", "type": "builtins.bool"}}, "_polymorphic_discriminator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._MapperEntity._polymorphic_discriminator", "name": "_polymorphic_discriminator", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "_with_polymorphic_mappers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._MapperEntity._with_polymorphic_mappers", "name": "_with_polymorphic_mappers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "corresponds_to": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._MapperEntity.corresponds_to", "name": "corresponds_to", "type": null}}, "entity_zero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._MapperEntity.entity_zero", "name": "entity_zero", "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}}}, "entity_zero_or_selectable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context._MapperEntity.entity_zero_or_selectable", "name": "entity_zero_or_selectable", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context._MapperEntity.entity_zero_or_selectable", "name": "entity_zero_or_selectable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.context._MapperEntity"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_zero_or_selectable of _MapperEntity", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._MapperEntity.expr", "name": "expr", "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}}}, "is_aliased_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._MapperEntity.is_aliased_class", "name": "is_aliased_class", "type": "builtins.bool"}}, "mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._MapperEntity.mapper", "name": "mapper", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._MapperEntity.path", "name": "path", "type": "sqlalchemy.orm.path_registry.PathRegistry"}}, "row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "result"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._MapperEntity.row_processor", "name": "row_processor", "type": null}}, "selectable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._MapperEntity.selectable", "name": "selectable", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "setup_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._MapperEntity.setup_compile_state", "name": "setup_compile_state", "type": null}}, "setup_dml_returning_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "adapter"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._MapperEntity.setup_dml_returning_compile_state", "name": "setup_dml_returning_compile_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "adapter"], "arg_types": ["sqlalchemy.orm.context._MapperEntity", "sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.orm.context.DMLReturningColFilter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_dml_returning_compile_state of _MapperEntity", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_single_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context._MapperEntity.supports_single_entity", "name": "supports_single_entity", "type": "builtins.bool"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.context._MapperEntity.type", "name": "type", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context._MapperEntity.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.context._MapperEntity"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "type of _MapperEntity", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "use_id_for_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context._MapperEntity.use_id_for_hash", "name": "use_id_for_hash", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context._MapperEntity.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context._MapperEntity", "values": [], "variance": 0}, "slots": ["_extra_entities", "_label_name", "_polymorphic_discriminator", "_with_polymorphic_mappers", "entity_zero", "expr", "is_aliased_class", "mapper", "path", "selectable"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ORMColumnEntity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.context._ColumnEntity"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context._ORMColumnEntity", "name": "_ORMColumnEntity", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context._ORMColumnEntity", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context._ORMColumnEntity", "sqlalchemy.orm.context._ColumnEntity", "sqlalchemy.orm.context._QueryEntity", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "compile_state", "column", "entities_collection", "parententity", "raw_column_index", "is_current_entities", "parent_bundle"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._ORMColumnEntity.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.context._ORMColumnEntity.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_extra_entities": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._ORMColumnEntity._extra_entities", "name": "_extra_entities", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_fetch_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sqlalchemy.orm.context._ORMColumnEntity._fetch_column", "name": "_fetch_column", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._ORMColumnEntity.column", "name": "column", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "corresponds_to": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._ORMColumnEntity.corresponds_to", "name": "corresponds_to", "type": null}}, "entity_zero_or_selectable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._ORMColumnEntity.entity_zero_or_selectable", "name": "entity_zero_or_selectable", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "mapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._ORMColumnEntity.mapper", "name": "mapper", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "raw_column_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._ORMColumnEntity.raw_column_index", "name": "raw_column_index", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._ORMColumnEntity.setup_compile_state", "name": "setup_compile_state", "type": null}}, "setup_dml_returning_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "adapter"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._ORMColumnEntity.setup_dml_returning_compile_state", "name": "setup_dml_returning_compile_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "adapter"], "arg_types": ["sqlalchemy.orm.context._ORMColumnEntity", "sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.orm.context.DMLReturningColFilter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_dml_returning_compile_state of _ORMColumnEntity", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_single_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context._ORMColumnEntity.supports_single_entity", "name": "supports_single_entity", "type": "builtins.bool"}}, "translate_raw_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._ORMColumnEntity.translate_raw_column", "name": "translate_raw_column", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context._ORMColumnEntity.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context._ORMColumnEntity", "values": [], "variance": 0}, "slots": ["_extra_entities", "_fetch_column", "_label_name", "_row_processor", "column", "entity_zero", "entity_zero_or_selectable", "expr", "mapper", "raw_column_index", "translate_raw_column"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ORMJoin": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util._ORMJoin", "kind": "Gdef"}, "_QueryEntity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context._QueryEntity", "name": "_QueryEntity", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context._QueryEntity", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context._QueryEntity", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.context._QueryEntity.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_label_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._QueryEntity._label_name", "name": "_label_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_non_hashable_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context._QueryEntity._non_hashable_value", "name": "_non_hashable_value", "type": "builtins.bool"}}, "_null_column_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context._QueryEntity._null_column_type", "name": "_null_column_type", "type": "builtins.bool"}}, "entity_zero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._QueryEntity.entity_zero", "name": "entity_zero", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._QueryEntity.expr", "name": "expr", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "uses_pep604_syntax": false}}}, "row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "result"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._QueryEntity.row_processor", "name": "row_processor", "type": null}}, "setup_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._QueryEntity.setup_compile_state", "name": "setup_compile_state", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "arg_types": ["sqlalchemy.orm.context._QueryEntity", "sqlalchemy.orm.context.ORMCompileState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_compile_state of _QueryEntity", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setup_dml_returning_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "adapter"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._QueryEntity.setup_dml_returning_compile_state", "name": "setup_dml_returning_compile_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "adapter"], "arg_types": ["sqlalchemy.orm.context._QueryEntity", "sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.orm.context.DMLReturningColFilter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_dml_returning_compile_state of _QueryEntity", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_single_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._QueryEntity.supports_single_entity", "name": "supports_single_entity", "type": "builtins.bool"}}, "to_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "compile_state", "entities", "entities_collection", "is_current_entities"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.context._QueryEntity.to_compile_state", "name": "to_compile_state", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.context._QueryEntity.to_compile_state", "name": "to_compile_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "compile_state", "entities", "entities_collection", "is_current_entities"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.context._QueryEntity"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_compile_state of _QueryEntity", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.context._QueryEntity.type", "name": "type", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}}}, "use_id_for_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context._QueryEntity.use_id_for_hash", "name": "use_id_for_hash", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context._QueryEntity.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context._QueryEntity", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_RawColumnEntity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.context._ColumnEntity"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.context._RawColumnEntity", "name": "_RawColumnEntity", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.context._RawColumnEntity", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.context", "mro": ["sqlalchemy.orm.context._RawColumnEntity", "sqlalchemy.orm.context._ColumnEntity", "sqlalchemy.orm.context._QueryEntity", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "compile_state", "column", "entities_collection", "raw_column_index", "is_current_entities", "parent_bundle"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._RawColumnEntity.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.context._RawColumnEntity.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_extra_entities": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._RawColumnEntity._extra_entities", "name": "_extra_entities", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_fetch_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sqlalchemy.orm.context._RawColumnEntity._fetch_column", "name": "_fetch_column", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._RawColumnEntity.column", "name": "column", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "corresponds_to": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._RawColumnEntity.corresponds_to", "name": "corresponds_to", "type": null}}, "entity_zero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context._RawColumnEntity.entity_zero", "name": "entity_zero", "type": {".class": "NoneType"}}}, "entity_zero_or_selectable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._RawColumnEntity.entity_zero_or_selectable", "name": "entity_zero_or_selectable", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context._RawColumnEntity.mapper", "name": "mapper", "type": {".class": "NoneType"}}}, "raw_column_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._RawColumnEntity.raw_column_index", "name": "raw_column_index", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._RawColumnEntity.setup_compile_state", "name": "setup_compile_state", "type": null}}, "setup_dml_returning_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "adapter"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._RawColumnEntity.setup_dml_returning_compile_state", "name": "setup_dml_returning_compile_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "adapter"], "arg_types": ["sqlalchemy.orm.context._RawColumnEntity", "sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.orm.context.DMLReturningColFilter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_dml_returning_compile_state of _RawColumnEntity", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_single_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context._RawColumnEntity.supports_single_entity", "name": "supports_single_entity", "type": "builtins.bool"}}, "translate_raw_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.context._RawColumnEntity.translate_raw_column", "name": "translate_raw_column", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context._RawColumnEntity.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.context._RawColumnEntity", "values": [], "variance": 0}, "slots": ["_extra_entities", "_fetch_column", "_label_name", "_row_processor", "column", "entity_zero_or_selectable", "expr", "raw_column_index", "translate_raw_column"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SetupJoinsElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable._SetupJoinsElement", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.context._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_TP": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._TP", "kind": "Gdef"}, "_TraceAdaptRole": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util._TraceAdaptRole", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.context.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.context.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.context.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.context.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.context.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.context.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_column_descriptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["query_or_select_stmt", "compile_state", "legacy"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._column_descriptions", "name": "_column_descriptions", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["query_or_select_stmt", "compile_state", "legacy"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.query.Query"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.context.FromStatement"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.orm.context.ORMSelectCompileState", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_column_descriptions", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces.ORMColumnDescription"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_determine_last_joined_entity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["setup_joins", "entity_zero"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._determine_last_joined_entity", "name": "_determine_last_joined_entity", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["setup_joins", "entity_zero"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable._SetupJoinsElement"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_determine_last_joined_entity", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable._JoinTargetElement"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_entity_corresponds_to": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util._entity_corresponds_to", "kind": "Gdef"}, "_entity_from_pre_ent_zero": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["query_or_augmented_select"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._entity_from_pre_ent_zero", "name": "_entity_from_pre_ent_zero", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["query_or_augmented_select"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.query.Query"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_entity_from_pre_ent_zero", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_aliased_class": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._is_aliased_class", "kind": "Gdef"}, "_legacy_filter_by_entity_zero": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["query_or_augmented_select"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.context._legacy_filter_by_entity_zero", "name": "_legacy_filter_by_entity_zero", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["query_or_augmented_select"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.query.Query"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_legacy_filter_by_entity_zero", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_orm_load_exec_options": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context._orm_load_exec_options", "name": "_orm_load_exec_options", "type": {".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "_path_registry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.context._path_registry", "name": "_path_registry", "type": "sqlalchemy.orm.path_registry.RootRegistry"}}, "_select_iterables": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._select_iterables", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "attributes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "coercions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.coercions", "kind": "Gdef"}, "expression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.expression", "kind": "Gdef"}, "future": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.future", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection.inspect", "kind": "Gdef"}, "interfaces": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces", "kind": "Gdef"}, "is_dml": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing.is_dml", "kind": "Gdef"}, "is_insert_update": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing.is_insert_update", "kind": "Gdef"}, "is_select_base": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing.is_select_base", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "loading": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.loading", "kind": "Gdef"}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef"}, "sa_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "sql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql", "kind": "Gdef"}, "sql_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.util", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "visitors": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/context.py"}