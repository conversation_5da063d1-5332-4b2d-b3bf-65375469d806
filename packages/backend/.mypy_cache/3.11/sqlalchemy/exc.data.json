{".class": "MypyFile", "_fullname": "sqlalchemy.exc", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AmbiguousForeignKeysError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.ArgumentError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.AmbiguousForeignKeysError", "name": "AmbiguousForeignKeysError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.AmbiguousForeignKeysError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.AmbiguousForeignKeysError", "sqlalchemy.exc.ArgumentError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.AmbiguousForeignKeysError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.AmbiguousForeignKeysError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ArgumentError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.SQLAlchemyError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.ArgumentError", "name": "ArgumentError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.ArgumentError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.ArgumentError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.ArgumentError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.ArgumentError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AwaitRequired": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.AwaitRequired", "name": "AwaitRequired", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.AwaitRequired", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.AwaitRequired", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.exc.AwaitRequired.code", "name": "code", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.AwaitRequired.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.AwaitRequired", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Base20DeprecationWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.SADeprecationWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.Base20DeprecationWarning", "name": "Base20DeprecationWarning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.Base20DeprecationWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.Base20DeprecationWarning", "sqlalchemy.exc.SADeprecationWarning", "sqlalchemy.exc.HasDescriptionCode", "builtins.DeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.Base20DeprecationWarning.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.exc.Base20DeprecationWarning"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of Base20DeprecationWarning", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deprecated_since": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.exc.Base20DeprecationWarning.deprecated_since", "name": "deprecated_since", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.Base20DeprecationWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.Base20DeprecationWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CircularDependencyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.SQLAlchemyError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.CircularDependencyError", "name": "CircularDependencyError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.CircularDependencyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.CircularDependencyError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "message", "cycles", "edges", "msg", "code"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.CircularDependencyError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "message", "cycles", "edges", "msg", "code"], "arg_types": ["sqlalchemy.exc.CircularDependencyError", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CircularDependencyError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.CircularDependencyError.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.exc.CircularDependencyError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of CircularDependencyError", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cycles": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.exc.CircularDependencyError.cycles", "name": "cycles", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "edges": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.exc.CircularDependencyError.edges", "name": "edges", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.CircularDependencyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.CircularDependencyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef"}, "CompileError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.SQLAlchemyError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.CompileError", "name": "CompileError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.CompileError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.CompileError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.CompileError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.CompileError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Compiled": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.Compiled", "kind": "Gdef"}, "ConstraintColumnNotFoundError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.ArgumentError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.ConstraintColumnNotFoundError", "name": "ConstraintColumnNotFoundError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.ConstraintColumnNotFoundError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.ConstraintColumnNotFoundError", "sqlalchemy.exc.ArgumentError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.ConstraintColumnNotFoundError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.ConstraintColumnNotFoundError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBAPIError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.StatementError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.DBAPIError", "name": "DBAPIError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.DBAPIError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.DBAPIError", "sqlalchemy.exc.StatementError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "statement", "params", "orig", "hide_parameters", "connection_invalidated", "code", "<PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.DBAPIError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "statement", "params", "orig", "hide_parameters", "connection_invalidated", "code", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": ["sqlalchemy.exc.DBAPIError", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.BaseException", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DBAPIError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.DBAPIError.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.exc.DBAPIError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of DBAPIError", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.exc.DBAPIError.code", "name": "code", "type": "builtins.str"}}, "instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": ["is_class"], "fullname": "sqlalchemy.exc.DBAPIError.instance", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "statement", "params", "orig", "dbapi_base_err", "hide_parameters", "connection_invalidated", "dialect", "<PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.exc.DBAPIError.instance", "name": "instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "statement", "params", "orig", "dbapi_base_err", "hide_parameters", "connection_invalidated", "dialect", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.exc.DBAPIError"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", "sqlalchemy.exc.DontWrapMixin"], "uses_pep604_syntax": false}, {".class": "TypeType", "item": "builtins.Exception"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance of DBAPIError", "ret_type": {".class": "UnionType", "items": ["builtins.BaseException", "sqlalchemy.exc.DontWrapMixin"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.exc.DBAPIError.instance", "name": "instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "statement", "params", "orig", "dbapi_base_err", "hide_parameters", "connection_invalidated", "dialect", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.exc.DBAPIError"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", "sqlalchemy.exc.DontWrapMixin"], "uses_pep604_syntax": false}, {".class": "TypeType", "item": "builtins.Exception"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance of DBAPIError", "ret_type": {".class": "UnionType", "items": ["builtins.BaseException", "sqlalchemy.exc.DontWrapMixin"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "statement", "params", "orig", "dbapi_base_err", "hide_parameters", "connection_invalidated", "dialect", "<PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "sqlalchemy.exc.DBAPIError.instance", "name": "instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "statement", "params", "orig", "dbapi_base_err", "hide_parameters", "connection_invalidated", "dialect", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.exc.DBAPIError"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.Exception", {".class": "TypeType", "item": "builtins.Exception"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance of DBAPIError", "ret_type": "sqlalchemy.exc.StatementError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.exc.DBAPIError.instance", "name": "instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "statement", "params", "orig", "dbapi_base_err", "hide_parameters", "connection_invalidated", "dialect", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.exc.DBAPIError"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.Exception", {".class": "TypeType", "item": "builtins.Exception"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance of DBAPIError", "ret_type": "sqlalchemy.exc.StatementError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "statement", "params", "orig", "dbapi_base_err", "hide_parameters", "connection_invalidated", "dialect", "<PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "sqlalchemy.exc.DBAPIError.instance", "name": "instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "statement", "params", "orig", "dbapi_base_err", "hide_parameters", "connection_invalidated", "dialect", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.exc.DBAPIError"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "sqlalchemy.exc.DontWrapMixin", {".class": "TypeType", "item": "builtins.Exception"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance of DBAPIError", "ret_type": "sqlalchemy.exc.DontWrapMixin", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.exc.DBAPIError.instance", "name": "instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "statement", "params", "orig", "dbapi_base_err", "hide_parameters", "connection_invalidated", "dialect", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.exc.DBAPIError"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "sqlalchemy.exc.DontWrapMixin", {".class": "TypeType", "item": "builtins.Exception"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance of DBAPIError", "ret_type": "sqlalchemy.exc.DontWrapMixin", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "statement", "params", "orig", "dbapi_base_err", "hide_parameters", "connection_invalidated", "dialect", "<PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "sqlalchemy.exc.DBAPIError.instance", "name": "instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "statement", "params", "orig", "dbapi_base_err", "hide_parameters", "connection_invalidated", "dialect", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.exc.DBAPIError"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.BaseException", {".class": "TypeType", "item": "builtins.Exception"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance of DBAPIError", "ret_type": "builtins.BaseException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.exc.DBAPIError.instance", "name": "instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "statement", "params", "orig", "dbapi_base_err", "hide_parameters", "connection_invalidated", "dialect", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.exc.DBAPIError"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.BaseException", {".class": "TypeType", "item": "builtins.Exception"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance of DBAPIError", "ret_type": "builtins.BaseException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "statement", "params", "orig", "dbapi_base_err", "hide_parameters", "connection_invalidated", "dialect", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.exc.DBAPIError"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.Exception", {".class": "TypeType", "item": "builtins.Exception"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance of DBAPIError", "ret_type": "sqlalchemy.exc.StatementError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "statement", "params", "orig", "dbapi_base_err", "hide_parameters", "connection_invalidated", "dialect", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.exc.DBAPIError"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "sqlalchemy.exc.DontWrapMixin", {".class": "TypeType", "item": "builtins.Exception"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance of DBAPIError", "ret_type": "sqlalchemy.exc.DontWrapMixin", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "statement", "params", "orig", "dbapi_base_err", "hide_parameters", "connection_invalidated", "dialect", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.exc.DBAPIError"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.BaseException", {".class": "TypeType", "item": "builtins.Exception"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance of DBAPIError", "ret_type": "builtins.BaseException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.DBAPIError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.DBAPIError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DataError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.DatabaseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.DataError", "name": "DataError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.DataError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.DataError", "sqlalchemy.exc.DatabaseError", "sqlalchemy.exc.DBAPIError", "sqlalchemy.exc.StatementError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.exc.DataError.code", "name": "code", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.DataError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.DataError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DatabaseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.DBAPIError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.DatabaseError", "name": "DatabaseError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.DatabaseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.DatabaseError", "sqlalchemy.exc.DBAPIError", "sqlalchemy.exc.StatementError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.exc.DatabaseError.code", "name": "code", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.DatabaseError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.DatabaseError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "DisconnectionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.SQLAlchemyError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.DisconnectionError", "name": "DisconnectionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.DisconnectionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.DisconnectionError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "invalidate_pool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.exc.DisconnectionError.invalidate_pool", "name": "invalidate_pool", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.DisconnectionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.DisconnectionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DontWrapMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.DontWrapMixin", "name": "DontWrapMixin", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.DontWrapMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.DontWrapMixin", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.DontWrapMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.DontWrapMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DuplicateColumnError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.ArgumentError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.DuplicateColumnError", "name": "DuplicateColumnError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.DuplicateColumnError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.DuplicateColumnError", "sqlalchemy.exc.ArgumentError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.DuplicateColumnError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.DuplicateColumnError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HasDescriptionCode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.HasDescriptionCode", "name": "HasDescriptionCode", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.HasDescriptionCode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.HasDescriptionCode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.HasDescriptionCode.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "arg_types": ["sqlalchemy.exc.HasDescriptionCode", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HasDescriptionCode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.HasDescriptionCode.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.exc.HasDescriptionCode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of HasDescriptionCode", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_code_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.HasDescriptionCode._code_str", "name": "_code_str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.exc.HasDescriptionCode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_code_str of HasDescriptionCode", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_what_are_we": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.exc.HasDescriptionCode._what_are_we", "name": "_what_are_we", "type": "builtins.str"}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.exc.HasDescriptionCode.code", "name": "code", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.HasDescriptionCode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.HasDescriptionCode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IdentifierError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.SQLAlchemyError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.IdentifierError", "name": "IdentifierError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.IdentifierError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.IdentifierError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.IdentifierError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.IdentifierError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IllegalStateChangeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.IllegalStateChangeError", "name": "IllegalStateChangeError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.IllegalStateChangeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.IllegalStateChangeError", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.IllegalStateChangeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.IllegalStateChangeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IntegrityError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.DatabaseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.IntegrityError", "name": "IntegrityError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.IntegrityError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.IntegrityError", "sqlalchemy.exc.DatabaseError", "sqlalchemy.exc.DBAPIError", "sqlalchemy.exc.StatementError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.exc.IntegrityError.code", "name": "code", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.IntegrityError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.IntegrityError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InterfaceError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.DBAPIError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.InterfaceError", "name": "InterfaceError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.InterfaceError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.InterfaceError", "sqlalchemy.exc.DBAPIError", "sqlalchemy.exc.StatementError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.exc.InterfaceError.code", "name": "code", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.InterfaceError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.InterfaceError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InternalError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.DatabaseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.InternalError", "name": "InternalError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.InternalError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.InternalError", "sqlalchemy.exc.DatabaseError", "sqlalchemy.exc.DBAPIError", "sqlalchemy.exc.StatementError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.exc.InternalError.code", "name": "code", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.InternalError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.InternalError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidRequestError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.SQLAlchemyError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.InvalidRequestError", "name": "InvalidRequestError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.InvalidRequestError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.InvalidRequestError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.InvalidRequestError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidatePoolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.DisconnectionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.InvalidatePoolError", "name": "InvalidatePoolError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.InvalidatePoolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.InvalidatePoolError", "sqlalchemy.exc.DisconnectionError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "invalidate_pool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.exc.InvalidatePoolError.invalidate_pool", "name": "invalidate_pool", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.InvalidatePoolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.InvalidatePoolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LegacyAPIWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.Base20DeprecationWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.LegacyAPIWarning", "name": "LegacyAPIWarning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.LegacyAPIWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.LegacyAPIWarning", "sqlalchemy.exc.Base20DeprecationWarning", "sqlalchemy.exc.SADeprecationWarning", "sqlalchemy.exc.HasDescriptionCode", "builtins.DeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.LegacyAPIWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.LegacyAPIWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MissingGreenlet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.MissingGreenlet", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.MissingGreenlet", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.MissingGreenlet", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.exc.MissingGreenlet.code", "name": "code", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.MissingGreenlet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.MissingGreenlet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MovedIn20Warning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.Base20DeprecationWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.MovedIn20Warning", "name": "MovedIn20Warning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.MovedIn20Warning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.MovedIn20Warning", "sqlalchemy.exc.Base20DeprecationWarning", "sqlalchemy.exc.SADeprecationWarning", "sqlalchemy.exc.HasDescriptionCode", "builtins.DeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.MovedIn20Warning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.MovedIn20Warning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultipleResultsFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.MultipleResultsFound", "name": "MultipleResultsFound", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.MultipleResultsFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.MultipleResultsFound", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.MultipleResultsFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.MultipleResultsFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoForeignKeysError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.ArgumentError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.NoForeignKeysError", "name": "NoForeignKeysError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.NoForeignKeysError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.NoForeignKeysError", "sqlalchemy.exc.ArgumentError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.NoForeignKeysError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.NoForeignKeysError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoInspectionAvailable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.NoInspectionAvailable", "name": "NoInspectionAvailable", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.NoInspectionAvailable", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.NoInspectionAvailable", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.NoInspectionAvailable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.NoInspectionAvailable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoReferenceError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.NoReferenceError", "name": "NoReferenceError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.NoReferenceError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.NoReferenceError", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "table_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.exc.NoReferenceError.table_name", "name": "table_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.NoReferenceError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.NoReferenceError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoReferencedColumnError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.NoReferenceError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.NoReferencedColumnError", "name": "NoReferencedColumnError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.NoReferencedColumnError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.NoReferencedColumnError", "sqlalchemy.exc.NoReferenceError", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "message", "tname", "cname"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.NoReferencedColumnError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "message", "tname", "cname"], "arg_types": ["sqlalchemy.exc.NoReferencedColumnError", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoReferencedColumnError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.NoReferencedColumnError.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.exc.NoReferencedColumnError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of NoReferencedColumnError", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "column_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.exc.NoReferencedColumnError.column_name", "name": "column_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.NoReferencedColumnError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.NoReferencedColumnError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoReferencedTableError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.NoReferenceError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.NoReferencedTableError", "name": "NoReferencedTableError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.NoReferencedTableError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.NoReferencedTableError", "sqlalchemy.exc.NoReferenceError", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "message", "tname"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.NoReferencedTableError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "message", "tname"], "arg_types": ["sqlalchemy.exc.NoReferencedTableError", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoReferencedTableError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.NoReferencedTableError.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.exc.NoReferencedTableError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of NoReferencedTableError", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.NoReferencedTableError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.NoReferencedTableError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoResultFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.NoResultFound", "name": "NoResultFound", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.NoResultFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.NoResultFound", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.NoResultFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.NoResultFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSuchColumnError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError", "builtins.KeyError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.NoSuchColumnError", "name": "NoSuchColumnError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.NoSuchColumnError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.NoSuchColumnError", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.KeyError", "builtins.LookupError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.NoSuchColumnError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.NoSuchColumnError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSuchModuleError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.ArgumentError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.NoSuchModuleError", "name": "NoSuchModuleError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.NoSuchModuleError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.NoSuchModuleError", "sqlalchemy.exc.ArgumentError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.NoSuchModuleError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.NoSuchModuleError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSuchTableError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.NoSuchTableError", "name": "NoSuchTableError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.NoSuchTableError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.NoSuchTableError", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.NoSuchTableError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.NoSuchTableError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotSupportedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.DatabaseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.NotSupportedError", "name": "NotSupportedError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.NotSupportedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.NotSupportedError", "sqlalchemy.exc.DatabaseError", "sqlalchemy.exc.DBAPIError", "sqlalchemy.exc.StatementError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.exc.NotSupportedError.code", "name": "code", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.NotSupportedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.NotSupportedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ObjectNotExecutableError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.ArgumentError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.ObjectNotExecutableError", "name": "ObjectNotExecutableError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.ObjectNotExecutableError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.ObjectNotExecutableError", "sqlalchemy.exc.ArgumentError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "target"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.ObjectNotExecutableError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "target"], "arg_types": ["sqlalchemy.exc.ObjectNotExecutableError", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ObjectNotExecutableError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.ObjectNotExecutableError.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.exc.ObjectNotExecutableError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of ObjectNotExecutableError", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "target": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.exc.ObjectNotExecutableError.target", "name": "target", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.ObjectNotExecutableError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.ObjectNotExecutableError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OperationalError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.DatabaseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.OperationalError", "name": "OperationalError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.OperationalError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.OperationalError", "sqlalchemy.exc.DatabaseError", "sqlalchemy.exc.DBAPIError", "sqlalchemy.exc.StatementError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.exc.OperationalError.code", "name": "code", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.OperationalError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.OperationalError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PendingRollbackError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.PendingRollbackError", "name": "PendingRollbackError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.PendingRollbackError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.PendingRollbackError", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.PendingRollbackError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.PendingRollbackError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProgrammingError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.DatabaseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.ProgrammingError", "name": "ProgrammingError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.ProgrammingError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.ProgrammingError", "sqlalchemy.exc.DatabaseError", "sqlalchemy.exc.DBAPIError", "sqlalchemy.exc.StatementError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.exc.ProgrammingError.code", "name": "code", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.ProgrammingError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.ProgrammingError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResourceClosedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.ResourceClosedError", "name": "ResourceClosedError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.ResourceClosedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.ResourceClosedError", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.ResourceClosedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.ResourceClosedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SADeprecationWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.HasDescriptionCode", "builtins.DeprecationWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.SADeprecationWarning", "name": "SADeprecationWarning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.SADeprecationWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.SADeprecationWarning", "sqlalchemy.exc.HasDescriptionCode", "builtins.DeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "deprecated_since": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.exc.SADeprecationWarning.deprecated_since", "name": "deprecated_since", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.SADeprecationWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.SADeprecationWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SAPendingDeprecationWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.PendingDeprecationWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.SAPendingDeprecationWarning", "name": "SAPendingDeprecationWarning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.SAPendingDeprecationWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.SAPendingDeprecationWarning", "builtins.PendingDeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "deprecated_since": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.exc.SAPendingDeprecationWarning.deprecated_since", "name": "deprecated_since", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.SAPendingDeprecationWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.SAPendingDeprecationWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SATestSuiteWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Warning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.SATestSuiteWarning", "name": "SATestSuiteWarning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.SATestSuiteWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.SATestSuiteWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.SATestSuiteWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.SATestSuiteWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SAWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.HasDescriptionCode", "builtins.RuntimeWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.SAWarning", "name": "SAWarning", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.SAWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.SAWarning", "sqlalchemy.exc.HasDescriptionCode", "builtins.RuntimeWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "_what_are_we": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.exc.SAWarning._what_are_we", "name": "_what_are_we", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.SAWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.SAWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SQLAlchemyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.HasDescriptionCode", "builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.SQLAlchemyError", "name": "SQLAlchemyError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.SQLAlchemyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.SQLAlchemyError.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.exc.SQLAlchemyError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of SQLAlchemyError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.SQLAlchemyError._message", "name": "_message", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.exc.SQLAlchemyError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_message of SQLAlchemyError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sql_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.SQLAlchemyError._sql_message", "name": "_sql_message", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.exc.SQLAlchemyError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sql_message of SQLAlchemyError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.SQLAlchemyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.SQLAlchemyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StatementError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.SQLAlchemyError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.StatementError", "name": "StatementError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.StatementError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.StatementError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "message", "statement", "params", "orig", "hide_parameters", "code", "<PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.StatementError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "message", "statement", "params", "orig", "hide_parameters", "code", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": ["sqlalchemy.exc.StatementError", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StatementError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.StatementError.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.exc.StatementError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of StatementError", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sql_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.exc.StatementError._sql_message", "name": "_sql_message", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.exc.StatementError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sql_message of StatementError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.exc.StatementError._sql_message", "name": "_sql_message", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.exc.StatementError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sql_message of StatementError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "add_detail": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.StatementError.add_detail", "name": "add_detail", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "arg_types": ["sqlalchemy.exc.StatementError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_detail of StatementError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connection_invalidated": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.exc.StatementError.connection_invalidated", "name": "connection_invalidated", "type": "builtins.bool"}}, "detail": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sqlalchemy.exc.StatementError.detail", "name": "detail", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "hide_parameters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.exc.StatementError.hide_parameters", "name": "hide_parameters", "type": "builtins.bool"}}, "ismulti": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.exc.StatementError.ismulti", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "orig": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.exc.StatementError.orig", "name": "orig", "type": {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.exc.StatementError.params", "name": "params", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.exc.StatementError.statement", "name": "statement", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.StatementError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.StatementError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeoutError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.SQLAlchemyError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.TimeoutError", "name": "TimeoutError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.TimeoutError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.TimeoutError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.TimeoutError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.TimeoutError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.TypeCompiler", "kind": "Gdef"}, "UnboundExecutionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.UnboundExecutionError", "name": "UnboundExecutionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.UnboundExecutionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.UnboundExecutionError", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.UnboundExecutionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.UnboundExecutionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UnreflectableTableError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.UnreflectableTableError", "name": "UnreflectableTableError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.UnreflectableTableError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.UnreflectableTableError", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.UnreflectableTableError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.UnreflectableTableError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedCompilationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.CompileError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.exc.UnsupportedCompilationError", "name": "UnsupportedCompilationError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.exc.UnsupportedCompilationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.exc", "mro": ["sqlalchemy.exc.UnsupportedCompilationError", "sqlalchemy.exc.CompileError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "compiler", "element_type", "message"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.UnsupportedCompilationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "compiler", "element_type", "message"], "arg_types": ["sqlalchemy.exc.UnsupportedCompilationError", {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.Compiled", "sqlalchemy.sql.compiler.TypeCompiler"], "uses_pep604_syntax": false}, {".class": "TypeType", "item": "sqlalchemy.sql.elements.ClauseElement"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnsupportedCompilationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.exc.UnsupportedCompilationError.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.exc.UnsupportedCompilationError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of UnsupportedCompilationError", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.exc.UnsupportedCompilationError.code", "name": "code", "type": "builtins.str"}}, "compiler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.exc.UnsupportedCompilationError.compiler", "name": "compiler", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.Compiled", "sqlalchemy.sql.compiler.TypeCompiler"], "uses_pep604_syntax": false}}}, "element_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.exc.UnsupportedCompilationError.element_type", "name": "element_type", "type": {".class": "TypeType", "item": "sqlalchemy.sql.elements.ClauseElement"}}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.exc.UnsupportedCompilationError.message", "name": "message", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.exc.UnsupportedCompilationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.exc.UnsupportedCompilationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AnyExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._AnyExecuteParams", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.exc.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.exc.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.exc.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.exc.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.exc.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.exc.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_preloaded": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.preloaded", "kind": "Gdef"}, "_version_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.exc._version_token", "name": "_version_token", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "compat": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/exc.py"}