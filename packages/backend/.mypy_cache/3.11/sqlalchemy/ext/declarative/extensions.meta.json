{"data_mtime": 1751259991, "dep_lines": [23, 24, 25, 26, 27, 28, 33, 20, 21, 23, 29, 30, 11, 13, 14, 15, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 25, 10, 5, 20, 5, 5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.exc", "sqlalchemy.orm.relationships", "sqlalchemy.orm.base", "sqlalchemy.orm.clsregistry", "sqlalchemy.orm.decl_base", "sqlalchemy.orm.util", "sqlalchemy.sql.schema", "sqlalchemy.exc", "sqlalchemy.engine", "sqlalchemy.orm", "sqlalchemy.schema", "sqlalchemy.util", "__future__", "collections", "contextlib", "typing", "sqlalchemy", "builtins", "_collections_abc", "_typeshed", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.url", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.query", "sqlalchemy.pool", "sqlalchemy.pool.base", "sqlalchemy.sql", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types", "typing_extensions"], "hash": "ca5a8870fbfd86c76efdf8fb660c60eadc6bd32e", "id": "sqlalchemy.ext.declarative.extensions", "ignore_all": true, "interface_hash": "2a8de7518b71a53fa8f9fd397e3c89d127f71908", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/ext/declarative/extensions.py", "plugin_data": null, "size": 19547, "suppressed": [], "version_id": "1.13.0"}