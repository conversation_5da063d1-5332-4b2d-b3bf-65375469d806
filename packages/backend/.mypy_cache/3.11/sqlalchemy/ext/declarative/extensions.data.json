{".class": "MypyFile", "_fullname": "sqlalchemy.ext.declarative.extensions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractConcreteBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.ext.declarative.extensions.ConcreteBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.ext.declarative.extensions.AbstractConcreteBase", "name": "AbstractConcreteBase", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.ext.declarative.extensions.AbstractConcreteBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.ext.declarative.extensions", "mro": ["sqlalchemy.ext.declarative.extensions.AbstractConcreteBase", "sqlalchemy.ext.declarative.extensions.ConcreteBase", "builtins.object"], "names": {".class": "SymbolTable", "__declare_first__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.ext.declarative.extensions.AbstractConcreteBase.__declare_first__", "name": "__declare_first__", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.declarative.extensions.AbstractConcreteBase.__declare_first__", "name": "__declare_first__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.ext.declarative.extensions.AbstractConcreteBase"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__declare_first__ of AbstractConcreteBase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__no_table__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.ext.declarative.extensions.AbstractConcreteBase.__no_table__", "name": "__no_table__", "type": "builtins.bool"}}, "_sa_decl_prepare_nocascade": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.ext.declarative.extensions.AbstractConcreteBase._sa_decl_prepare_nocascade", "name": "_sa_decl_prepare_nocascade", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.declarative.extensions.AbstractConcreteBase._sa_decl_prepare_nocascade", "name": "_sa_decl_prepare_nocascade", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.ext.declarative.extensions.AbstractConcreteBase"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sa_decl_prepare_nocascade of AbstractConcreteBase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_sa_raise_deferred_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.ext.declarative.extensions.AbstractConcreteBase._sa_raise_deferred_config", "name": "_sa_raise_deferred_config", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.declarative.extensions.AbstractConcreteBase._sa_raise_deferred_config", "name": "_sa_raise_deferred_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.ext.declarative.extensions.AbstractConcreteBase"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sa_raise_deferred_config of AbstractConcreteBase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.declarative.extensions.AbstractConcreteBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.ext.declarative.extensions.AbstractConcreteBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ConcreteBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.ext.declarative.extensions.ConcreteBase", "name": "ConcreteBase", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.ext.declarative.extensions.ConcreteBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.ext.declarative.extensions", "mro": ["sqlalchemy.ext.declarative.extensions.ConcreteBase", "builtins.object"], "names": {".class": "SymbolTable", "__declare_first__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.ext.declarative.extensions.ConcreteBase.__declare_first__", "name": "__declare_first__", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.declarative.extensions.ConcreteBase.__declare_first__", "name": "__declare_first__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.ext.declarative.extensions.ConcreteBase"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__declare_first__ of ConcreteBase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_create_polymorphic_union": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "mappers", "discriminator_name"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.ext.declarative.extensions.ConcreteBase._create_polymorphic_union", "name": "_create_polymorphic_union", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.declarative.extensions.ConcreteBase._create_polymorphic_union", "name": "_create_polymorphic_union", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "mappers", "discriminator_name"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.ext.declarative.extensions.ConcreteBase"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_polymorphic_union of ConcreteBase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.declarative.extensions.ConcreteBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.ext.declarative.extensions.ConcreteBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "DeferredReflection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.ext.declarative.extensions.DeferredReflection", "name": "DeferredReflection", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.ext.declarative.extensions.DeferredReflection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.ext.declarative.extensions", "mro": ["sqlalchemy.ext.declarative.extensions.DeferredReflection", "builtins.object"], "names": {".class": "SymbolTable", "_sa_decl_prepare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.ext.declarative.extensions.DeferredReflection._sa_decl_prepare", "name": "_sa_decl_prepare", "type": "builtins.bool"}}, "_sa_deferred_table_resolver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "metadata"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.ext.declarative.extensions.DeferredReflection._sa_deferred_table_resolver", "name": "_sa_deferred_table_resolver", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "metadata"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.ext.declarative.extensions.DeferredReflection"}, "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sa_deferred_table_resolver of DeferredReflection", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.declarative.extensions.DeferredReflection._sa_deferred_table_resolver", "name": "_sa_deferred_table_resolver", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "metadata"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.ext.declarative.extensions.DeferredReflection"}, "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sa_deferred_table_resolver of DeferredReflection", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_sa_raise_deferred_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.ext.declarative.extensions.DeferredReflection._sa_raise_deferred_config", "name": "_sa_raise_deferred_config", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.declarative.extensions.DeferredReflection._sa_raise_deferred_config", "name": "_sa_raise_deferred_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.ext.declarative.extensions.DeferredReflection"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sa_raise_deferred_config of DeferredReflection", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "prepare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "bind", "reflect_kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.ext.declarative.extensions.DeferredReflection.prepare", "name": "prepare", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "bind", "reflect_kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.ext.declarative.extensions.DeferredReflection"}, {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare of DeferredReflection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.declarative.extensions.DeferredReflection.prepare", "name": "prepare", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "bind", "reflect_kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.ext.declarative.extensions.DeferredReflection"}, {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare of DeferredReflection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.declarative.extensions.DeferredReflection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.ext.declarative.extensions.DeferredReflection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Engine", "kind": "Gdef"}, "MetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.MetaData", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.OrderedDict", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_DeferredMapperConfig": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_base._DeferredMapperConfig", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.declarative.extensions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.declarative.extensions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.declarative.extensions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.declarative.extensions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.declarative.extensions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.declarative.extensions.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_mapper_or_none": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._mapper_or_none", "kind": "Gdef"}, "_resolver": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.clsregistry._resolver", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "orm_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef"}, "polymorphic_union": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.polymorphic_union", "kind": "Gdef"}, "relationships": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.relationships", "kind": "Gdef"}, "sa_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/ext/declarative/extensions.py"}