{"data_mtime": 1751259990, "dep_lines": [25, 26, 30, 25, 40, 43, 44, 48, 49, 58, 59, 62, 63, 64, 33, 34, 35, 36, 60, 7, 9, 10, 11, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 20, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 5, 10, 10, 5, 25, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.ext.asyncio.exc", "sqlalchemy.ext.asyncio.base", "sqlalchemy.ext.asyncio.result", "sqlalchemy.ext.asyncio", "sqlalchemy.engine.base", "sqlalchemy.util.concurrency", "sqlalchemy.util.typing", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.result", "sqlalchemy.engine.url", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.selectable", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.pool", "__future__", "asyncio", "contextlib", "typing", "sqlalchemy", "builtins", "abc", "asyncio.futures", "asyncio.tasks", "<PERSON><PERSON><PERSON>", "importlib", "importlib.machinery", "sqlalchemy.engine.default", "sqlalchemy.engine.util", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.pool.base", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._concurrency_py3k", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "528aa6ebf2cb15c9c1ed1e87fe2ec30d3eff0508", "id": "sqlalchemy.ext.asyncio.engine", "ignore_all": true, "interface_hash": "f22d565423a69a2958d8342c7c94b53e51c0c845", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/engine.py", "plugin_data": null, "size": 48190, "suppressed": [], "version_id": "1.13.0"}