{".class": "MypyFile", "_fullname": "sqlalchemy.ext.asyncio", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncAttrs": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.AsyncAttrs", "kind": "Gdef"}, "AsyncConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.engine.AsyncConnection", "kind": "Gdef"}, "AsyncEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.engine.AsyncEngine", "kind": "Gdef"}, "AsyncMappingResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.result.AsyncMappingResult", "kind": "Gdef"}, "AsyncResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.result.AsyncResult", "kind": "Gdef"}, "AsyncScalarResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.result.AsyncScalarResult", "kind": "Gdef"}, "AsyncSession": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.AsyncSession", "kind": "Gdef"}, "AsyncSessionTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.AsyncSessionTransaction", "kind": "Gdef"}, "AsyncTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.engine.AsyncTransaction", "kind": "Gdef"}, "AsyncTupleResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.result.AsyncTupleResult", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "async_engine_from_config": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.engine.async_engine_from_config", "kind": "Gdef"}, "async_object_session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.async_object_session", "kind": "Gdef"}, "async_scoped_session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "kind": "Gdef"}, "async_session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.async_session", "kind": "Gdef"}, "async_sessionmaker": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.async_sessionmaker", "kind": "Gdef"}, "close_all_sessions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.close_all_sessions", "kind": "Gdef"}, "create_async_engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.engine.create_async_engine", "kind": "Gdef"}, "create_async_pool_from_url": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.engine.create_async_pool_from_url", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/__init__.py"}