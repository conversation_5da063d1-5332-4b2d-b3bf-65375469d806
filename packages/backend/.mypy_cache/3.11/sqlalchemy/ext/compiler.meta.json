{"data_mtime": 1751259990, "dep_lines": [464, 467, 463, 464, 454, 456, 463, 1, 1, 1, 1], "dep_prios": [10, 25, 10, 20, 5, 5, 20, 5, 30, 30, 30], "dependencies": ["sqlalchemy.sql.sqltypes", "sqlalchemy.sql.compiler", "sqlalchemy.exc", "sqlalchemy.sql", "__future__", "typing", "sqlalchemy", "builtins", "abc", "importlib", "importlib.machinery"], "hash": "bc73c6d8b3b0ec3a3c9ec6d648138bdba8cf4fff", "id": "sqlalchemy.ext.compiler", "ignore_all": true, "interface_hash": "8ff1df679dce5d5565192f8729f05cddb7dd226f", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/sqlalchemy/ext/compiler.py", "plugin_data": null, "size": 20877, "suppressed": [], "version_id": "1.13.0"}