{"data_mtime": 1751259991, "dep_lines": [21, 22, 23, 24, 7, 18, 19, 20, 1, 3, 4, 5, 6, 8, 9, 10, 11, 12, 13, 14, 16, 323, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 373], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["uvicorn.middleware.asgi2", "uvicorn.middleware.message_logger", "uvicorn.middleware.proxy_headers", "uvicorn.middleware.wsgi", "logging.config", "uvicorn._types", "uvicorn.importer", "uvicorn.logging", "__future__", "asyncio", "inspect", "json", "logging", "os", "socket", "ssl", "sys", "configparser", "pathlib", "typing", "click", "dotenv", "builtins", "_socket", "_typeshed", "abc", "asyncio.coroutines", "asyncio.protocols", "dotenv.main", "enum", "importlib", "importlib.machinery", "io", "json.decoder", "types", "typing_extensions", "uvicorn.middleware"], "hash": "ffcb03a79c24501aa973ebd7b196a27ef03881da", "id": "uvicorn.config", "ignore_all": true, "interface_hash": "3e5a3bb8eb7617128a55a54ee74e99241389f5f9", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/uvicorn/config.py", "plugin_data": null, "size": 20849, "suppressed": ["yaml"], "version_id": "1.13.0"}