{"data_mtime": 1751259991, "dep_lines": [23, 24, 7, 10, 12, 21, 22, 25, 1, 3, 4, 5, 6, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["uvicorn.protocols.http.flow_control", "uvicorn.protocols.utils", "urllib.parse", "h11._connection", "uvicorn._types", "uvicorn.config", "uvicorn.logging", "uvicorn.server", "__future__", "asyncio", "http", "logging", "typing", "h11", "builtins", "abc", "anyio", "anyio._core", "anyio._core._synchronization", "asyncio.events", "asyncio.futures", "asyncio.locks", "asyncio.mixins", "asyncio.protocols", "asyncio.tasks", "asyncio.transports", "<PERSON><PERSON><PERSON>", "h11._events", "h11._headers", "h11._state", "h11._util", "importlib", "importlib.machinery", "types", "typing_extensions", "urllib", "uvicorn.protocols.http.httptools_impl", "uvicorn.protocols.websockets", "uvicorn.protocols.websockets.websockets_impl", "uvicorn.protocols.websockets.wsproto_impl"], "hash": "aa6eedeea26b4b0a64e64d1afa184c96f7609c95", "id": "uvicorn.protocols.http.h11_impl", "ignore_all": true, "interface_hash": "ecdf10ddd13f6e998d8f43166ac540000274c71f", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/uvicorn/protocols/http/h11_impl.py", "plugin_data": null, "size": 20694, "suppressed": [], "version_id": "1.13.0"}