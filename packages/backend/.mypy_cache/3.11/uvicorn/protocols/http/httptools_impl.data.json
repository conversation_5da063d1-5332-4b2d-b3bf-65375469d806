{".class": "MypyFile", "_fullname": "uvicorn.protocols.http.httptools_impl", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGI3Application": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGI3Application", "kind": "Gdef"}, "ASGIReceiveEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGIReceiveEvent", "kind": "Gdef"}, "ASGISendEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGISendEvent", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CLOSE_HEADER": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.http.flow_control.CLOSE_HEADER", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.Config", "kind": "Gdef"}, "FlowControl": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.http.flow_control.FlowControl", "kind": "Gdef"}, "HEADER_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "uvicorn.protocols.http.httptools_impl.HEADER_RE", "name": "HEADER_RE", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "HEADER_VALUE_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "uvicorn.protocols.http.httptools_impl.HEADER_VALUE_RE", "name": "HEADER_VALUE_RE", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "HIGH_WATER_LIMIT": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.http.flow_control.HIGH_WATER_LIMIT", "kind": "Gdef"}, "HTTPRequestEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.HTTPRequestEvent", "kind": "Gdef"}, "HTTPResponseStartEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.HTTPResponseStartEvent", "kind": "Gdef"}, "HTTPScope": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.HTTPScope", "kind": "Gdef"}, "HttpToolsProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.protocols.Protocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol", "name": "HttpToolsProtocol", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn.protocols.http.httptools_impl", "mro": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol", "asyncio.protocols.Protocol", "asyncio.protocols.BaseProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "config", "server_state", "app_state", "_loop"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "config", "server_state", "app_state", "_loop"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol", "uvicorn.config.Config", "uvicorn.server.ServerState", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_upgrade": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol._get_upgrade", "name": "_get_upgrade", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_upgrade of HttpToolsProtocol", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_should_upgrade": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol._should_upgrade", "name": "_should_upgrade", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_upgrade of HttpToolsProtocol", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_should_upgrade_to_ws": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol._should_upgrade_to_ws", "name": "_should_upgrade_to_ws", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_upgrade_to_ws of HttpToolsProtocol", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unset_keepalive_if_required": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol._unset_keepalive_if_required", "name": "_unset_keepalive_if_required", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unset_keepalive_if_required of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unsupported_upgrade_warning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol._unsupported_upgrade_warning", "name": "_unsupported_upgrade_warning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unsupported_upgrade_warning of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "access_log": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.access_log", "name": "access_log", "type": "builtins.bool"}}, "access_logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.access_logger", "name": "access_logger", "type": "logging.Logger"}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.app", "name": "app", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "app_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.app_state", "name": "app_state", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.client", "name": "client", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.config", "name": "config", "type": "uvicorn.config.Config"}}, "connection_lost": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.connection_lost", "name": "connection_lost", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol", {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_lost of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connection_made": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.connection_made", "name": "connection_made", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol", "asyncio.transports.Transport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_made of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connections": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.connections", "name": "connections", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn.server.Protocols"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "cycle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.cycle", "name": "cycle", "type": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle"}}, "data_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.data_received", "name": "data_received", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data_received of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "eof_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.eof_received", "name": "eof_received", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eof_received of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expect_100_continue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.expect_100_continue", "name": "expect_100_continue", "type": "builtins.bool"}}, "flow": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.flow", "name": "flow", "type": "uvicorn.protocols.http.flow_control.FlowControl"}}, "handle_websocket_upgrade": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.handle_websocket_upgrade", "name": "handle_websocket_upgrade", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_websocket_upgrade of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.headers", "name": "headers", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "limit_concurrency": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.limit_concurrency", "name": "limit_concurrency", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.logger", "name": "logger", "type": "logging.Logger"}}, "loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.loop", "name": "loop", "type": "asyncio.events.AbstractEventLoop"}}, "on_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "body"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.on_body", "name": "on_body", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "body"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_body of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.on_header", "name": "on_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_header of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_headers_complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.on_headers_complete", "name": "on_headers_complete", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_headers_complete of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_message_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.on_message_begin", "name": "on_message_begin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_message_begin of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_message_complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.on_message_complete", "name": "on_message_complete", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_message_complete of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_response_complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.on_response_complete", "name": "on_response_complete", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_response_complete of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.on_url", "name": "on_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_url of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.parser", "name": "parser", "type": {".class": "AnyType", "missing_import_name": "uvicorn.protocols.http.httptools_impl.httptools", "source_any": {".class": "AnyType", "missing_import_name": "uvicorn.protocols.http.httptools_impl.httptools", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "pause_writing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.pause_writing", "name": "pause_writing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pause_writing of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pipeline": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.pipeline", "name": "pipeline", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["uvicorn.protocols.http.httptools_impl.RequestResponseCycle", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGI3Application"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "collections.deque"}}}, "resume_writing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.resume_writing", "name": "resume_writing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resume_writing of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "root_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.root_path", "name": "root_path", "type": "builtins.str"}}, "scheme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.scheme", "name": "scheme", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "http"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "https"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "scope": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.scope", "name": "scope", "type": {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPScope"}}}, "send_400_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.send_400_response", "name": "send_400_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_400_response of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "server": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.server", "name": "server", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "server_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.server_state", "name": "server_state", "type": "uvicorn.server.ServerState"}}, "shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.shutdown", "name": "shutdown", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shutdown of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tasks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.tasks", "name": "tasks", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "asyncio.tasks.Task"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "timeout_keep_alive": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.timeout_keep_alive", "name": "timeout_keep_alive", "type": "builtins.int"}}, "timeout_keep_alive_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.timeout_keep_alive_handler", "name": "timeout_keep_alive_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.HttpToolsProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timeout_keep_alive_handler of HttpToolsProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "timeout_keep_alive_task": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.timeout_keep_alive_task", "name": "timeout_keep_alive_task", "type": {".class": "UnionType", "items": ["asyncio.events.TimerHandle", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "transport": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.transport", "name": "transport", "type": "asyncio.transports.Transport"}}, "url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.url", "name": "url", "type": "builtins.bytes"}}, "ws_protocol_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.ws_protocol_class", "name": "ws_protocol_class", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "asyncio.protocols.Protocol"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "uvicorn.protocols.http.httptools_impl.HttpToolsProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "RequestResponseCycle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle", "name": "RequestResponseCycle", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn.protocols.http.httptools_impl", "mro": ["uvicorn.protocols.http.httptools_impl.RequestResponseCycle", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scope", "transport", "flow", "logger", "access_logger", "access_log", "default_headers", "message_event", "expect_100_continue", "keep_alive", "on_response"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scope", "transport", "flow", "logger", "access_logger", "access_log", "default_headers", "message_event", "expect_100_continue", "keep_alive", "on_response"], "arg_types": ["uvicorn.protocols.http.httptools_impl.RequestResponseCycle", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPScope"}, "asyncio.transports.Transport", "uvicorn.protocols.http.flow_control.FlowControl", "logging.Logger", "logging.Logger", "builtins.bool", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "asyncio.locks.Event", "builtins.bool", "builtins.bool", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RequestResponseCycle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "access_log": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.access_log", "name": "access_log", "type": "builtins.bool"}}, "access_logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.access_logger", "name": "access_logger", "type": "logging.Logger"}}, "body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.body", "name": "body", "type": "builtins.bytes"}}, "chunked_encoding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.chunked_encoding", "name": "chunked_encoding", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "default_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.default_headers", "name": "default_headers", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "disconnected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.disconnected", "name": "disconnected", "type": "builtins.bool"}}, "expected_content_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.expected_content_length", "name": "expected_content_length", "type": "builtins.int"}}, "flow": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.flow", "name": "flow", "type": "uvicorn.protocols.http.flow_control.FlowControl"}}, "keep_alive": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.keep_alive", "name": "keep_alive", "type": "builtins.bool"}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.logger", "name": "logger", "type": "logging.Logger"}}, "message_event": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.message_event", "name": "message_event", "type": "asyncio.locks.Event"}}, "more_body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.more_body", "name": "more_body", "type": "builtins.bool"}}, "on_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.on_response", "name": "on_response", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.receive", "name": "receive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.RequestResponseCycle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive of RequestResponseCycle", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIReceiveEvent"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "response_complete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.response_complete", "name": "response_complete", "type": "builtins.bool"}}, "response_started": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.response_started", "name": "response_started", "type": "builtins.bool"}}, "run_asgi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "app"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.run_asgi", "name": "run_asgi", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "app"], "arg_types": ["uvicorn.protocols.http.httptools_impl.RequestResponseCycle", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGI3Application"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_asgi of RequestResponseCycle", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scope": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.scope", "name": "scope", "type": {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPScope"}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["uvicorn.protocols.http.httptools_impl.RequestResponseCycle", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendEvent"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of RequestResponseCycle", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_500_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.send_500_response", "name": "send_500_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.httptools_impl.RequestResponseCycle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_500_response of RequestResponseCycle", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transport": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.transport", "name": "transport", "type": "asyncio.transports.Transport"}}, "waiting_for_100_continue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.waiting_for_100_continue", "name": "waiting_for_100_continue", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "uvicorn.protocols.http.httptools_impl.RequestResponseCycle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "STATUS_LINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "uvicorn.protocols.http.httptools_impl.STATUS_LINE", "name": "STATUS_LINE", "type": {".class": "Instance", "args": ["builtins.int", "builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ServerState": {".class": "SymbolTableNode", "cross_ref": "uvicorn.server.ServerState", "kind": "Gdef"}, "TRACE_LOG_LEVEL": {".class": "SymbolTableNode", "cross_ref": "uvicorn.logging.TRACE_LOG_LEVEL", "kind": "Gdef"}, "TimerHandle": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.TimerHandle", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.http.httptools_impl.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.http.httptools_impl.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.http.httptools_impl.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.http.httptools_impl.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.http.httptools_impl.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.http.httptools_impl.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_get_status_line": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["status_code"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.http.httptools_impl._get_status_line", "name": "_get_status_line", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["status_code"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_status_line", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef"}, "get_client_addr": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_client_addr", "kind": "Gdef"}, "get_local_addr": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_local_addr", "kind": "Gdef"}, "get_path_with_query_string": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_path_with_query_string", "kind": "Gdef"}, "get_remote_addr": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_remote_addr", "kind": "Gdef"}, "http": {".class": "SymbolTableNode", "cross_ref": "http", "kind": "Gdef"}, "httptools": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.httptools_impl.httptools", "name": "httptools", "type": {".class": "AnyType", "missing_import_name": "uvicorn.protocols.http.httptools_impl.httptools", "source_any": null, "type_of_any": 3}}}, "is_ssl": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.is_ssl", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "service_unavailable": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.http.flow_control.service_unavailable", "kind": "Gdef"}, "urllib": {".class": "SymbolTableNode", "cross_ref": "urllib", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py"}