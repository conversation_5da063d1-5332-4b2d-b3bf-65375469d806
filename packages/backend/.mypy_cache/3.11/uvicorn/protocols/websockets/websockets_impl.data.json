{".class": "MypyFile", "_fullname": "uvicorn.protocols.websockets.websockets_impl", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGI3Application": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGI3Application", "kind": "Gdef"}, "ASGISendEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGISendEvent", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ClientDisconnected": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.ClientDisconnected", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.Config", "kind": "Gdef"}, "ConnectionClosed": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.ConnectionClosed", "kind": "Gdef"}, "HTTPResponse": {".class": "SymbolTableNode", "cross_ref": "websockets.legacy.server.HTTPResponse", "kind": "Gdef"}, "Headers": {".class": "SymbolTableNode", "cross_ref": "websockets.datastructures.Headers", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Server": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn.protocols.websockets.websockets_impl.Server", "name": "Server", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "uvicorn.protocols.websockets.websockets_impl.Server", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn.protocols.websockets.websockets_impl", "mro": ["uvicorn.protocols.websockets.websockets_impl.Server", "builtins.object"], "names": {".class": "SymbolTable", "closing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "uvicorn.protocols.websockets.websockets_impl.Server.closing", "name": "closing", "type": "builtins.bool"}}, "is_serving": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.websockets.websockets_impl.Server.is_serving", "name": "is_serving", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.websockets_impl.Server"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_serving of Server", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ws"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.websockets.websockets_impl.Server.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ws"], "arg_types": ["uvicorn.protocols.websockets.websockets_impl.Server", {".class": "AnyType", "missing_import_name": "uvicorn.protocols.websockets.websockets_impl.WebSocketServerProtocol", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of Server", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unregister": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ws"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.websockets.websockets_impl.Server.unregister", "name": "unregister", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ws"], "arg_types": ["uvicorn.protocols.websockets.websockets_impl.Server", {".class": "AnyType", "missing_import_name": "uvicorn.protocols.websockets.websockets_impl.WebSocketServerProtocol", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unregister of Server", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "uvicorn.protocols.websockets.websockets_impl.Server.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "uvicorn.protocols.websockets.websockets_impl.Server", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ServerExtensionFactory": {".class": "SymbolTableNode", "cross_ref": "websockets.extensions.base.ServerExtensionFactory", "kind": "Gdef"}, "ServerPerMessageDeflateFactory": {".class": "SymbolTableNode", "cross_ref": "websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory", "kind": "Gdef"}, "ServerState": {".class": "SymbolTableNode", "cross_ref": "uvicorn.server.ServerState", "kind": "Gdef"}, "Subprotocol": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.Subprotocol", "kind": "Gdef"}, "TRACE_LOG_LEVEL": {".class": "SymbolTableNode", "cross_ref": "uvicorn.logging.TRACE_LOG_LEVEL", "kind": "Gdef"}, "WebSocketAcceptEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketAcceptEvent", "kind": "Gdef"}, "WebSocketCloseEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketCloseEvent", "kind": "Gdef"}, "WebSocketConnectEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketConnectEvent", "kind": "Gdef"}, "WebSocketDisconnectEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketDisconnectEvent", "kind": "Gdef"}, "WebSocketProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol", "name": "WebSocketProtocol", "type_vars": []}, "deletable_attributes": [], "flags": ["fallback_to_any"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn.protocols.websockets.websockets_impl", "mro": ["uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "config", "server_state", "app_state", "_loop"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "config", "server_state", "app_state", "_loop"], "arg_types": ["uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol", "uvicorn.config.Config", "uvicorn.server.ServerState", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WebSocketProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accepted_subprotocol": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.accepted_subprotocol", "name": "accepted_subprotocol", "type": {".class": "UnionType", "items": ["websockets.typing.Subprotocol", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.app", "name": "app", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIReceiveCallable"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.app_state", "name": "app_state", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "asgi_receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.asgi_receive", "name": "asgi_receive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asgi_receive of WebSocketProtocol", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketDisconnectEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketConnectEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketReceiveEvent"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "asgi_send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.asgi_send", "name": "asgi_send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendEvent"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asgi_send of WebSocketProtocol", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.client", "name": "client", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "closed_event": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.closed_event", "name": "closed_event", "type": "asyncio.locks.Event"}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.config", "name": "config", "type": "uvicorn.config.Config"}}, "connect_sent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.connect_sent", "name": "connect_sent", "type": "builtins.bool"}}, "connection_lost": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.connection_lost", "name": "connection_lost", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "arg_types": ["uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol", {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_lost of WebSocketProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connection_made": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.connection_made", "name": "connection_made", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "arg_types": ["uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol", "asyncio.transports.Transport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_made of WebSocketProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connections": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.connections", "name": "connections", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn.server.Protocols"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "extra_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.extra_headers", "name": "extra_headers", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "handshake_completed_event": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.handshake_completed_event", "name": "handshake_completed_event", "type": "asyncio.locks.Event"}}, "handshake_started_event": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.handshake_started_event", "name": "handshake_started_event", "type": "asyncio.locks.Event"}}, "initial_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.initial_response", "name": "initial_response", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.legacy.server.HTTPResponse"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "logger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.logger", "name": "logger", "type": {".class": "UnionType", "items": ["logging.Logger", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "logging.LoggerAdapter"}], "uses_pep604_syntax": true}}}, "loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.loop", "name": "loop", "type": "asyncio.events.AbstractEventLoop"}}, "lost_connection_before_handshake": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.lost_connection_before_handshake", "name": "lost_connection_before_handshake", "type": "builtins.bool"}}, "on_task_complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.on_task_complete", "name": "on_task_complete", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "task"], "arg_types": ["uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol", {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "asyncio.tasks.Task"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_task_complete of WebSocketProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "path", "request_headers"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.process_request", "name": "process_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "path", "request_headers"], "arg_types": ["uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol", "builtins.str", "websockets.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_request of WebSocketProtocol", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.legacy.server.HTTPResponse"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_subprotocol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "headers", "available_subprotocols"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.process_subprotocol", "name": "process_subprotocol", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "headers", "available_subprotocols"], "arg_types": ["uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol", "websockets.datastructures.Headers", {".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.typing.Subprotocol"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_subprotocol of WebSocketProtocol", "ret_type": {".class": "UnionType", "items": ["websockets.typing.Subprotocol", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "root_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.root_path", "name": "root_path", "type": "builtins.str"}}, "run_asgi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.run_asgi", "name": "run_asgi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_asgi of WebSocketProtocol", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scheme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.scheme", "name": "scheme", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wss"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ws"}], "uses_pep604_syntax": false}}}, "scope": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.scope", "name": "scope", "type": {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketScope"}}}, "send_500_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.send_500_response", "name": "send_500_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_500_response of WebSocketProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "server": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.server", "name": "server", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "server_header": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.server_header", "name": "server_header", "type": {".class": "NoneType"}}}, "shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.shutdown", "name": "shutdown", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shutdown of WebSocketProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tasks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.tasks", "name": "tasks", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "asyncio.tasks.Task"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "transport": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.transport", "name": "transport", "type": "asyncio.transports.Transport"}}, "ws_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "protocol", "path"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.ws_handler", "name": "ws_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "protocol", "path"], "arg_types": ["uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol", {".class": "AnyType", "missing_import_name": "uvicorn.protocols.websockets.websockets_impl.WebSocketServerProtocol", "source_any": null, "type_of_any": 3}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ws_handler of WebSocketProtocol", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ws_server": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.ws_server", "name": "ws_server", "type": "uvicorn.protocols.websockets.websockets_impl.Server"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "uvicorn.protocols.websockets.websockets_impl.WebSocketProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WebSocketReceiveEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketReceiveEvent", "kind": "Gdef"}, "WebSocketResponseBodyEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketResponseBodyEvent", "kind": "Gdef"}, "WebSocketResponseStartEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketResponseStartEvent", "kind": "Gdef"}, "WebSocketScope": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketScope", "kind": "Gdef"}, "WebSocketSendEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketSendEvent", "kind": "Gdef"}, "WebSocketServerProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_impl.WebSocketServerProtocol", "name": "WebSocketServerProtocol", "type": {".class": "AnyType", "missing_import_name": "uvicorn.protocols.websockets.websockets_impl.WebSocketServerProtocol", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.websockets_impl.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.websockets_impl.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.websockets_impl.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.websockets_impl.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.websockets_impl.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.websockets_impl.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "get_local_addr": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_local_addr", "kind": "Gdef"}, "get_path_with_query_string": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_path_with_query_string", "kind": "Gdef"}, "get_remote_addr": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_remote_addr", "kind": "Gdef"}, "http": {".class": "SymbolTableNode", "cross_ref": "http", "kind": "Gdef"}, "is_ssl": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.is_ssl", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "unquote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.unquote", "kind": "Gdef"}, "websockets": {".class": "SymbolTableNode", "cross_ref": "websockets", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/uvicorn/protocols/websockets/websockets_impl.py"}