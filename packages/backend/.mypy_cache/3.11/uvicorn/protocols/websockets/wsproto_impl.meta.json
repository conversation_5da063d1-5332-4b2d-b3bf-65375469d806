{"data_mtime": 1751259991, "dep_lines": [28, 7, 15, 26, 27, 35, 1, 3, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 11, 12, 13, 9], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5], "dependencies": ["uvicorn.protocols.utils", "urllib.parse", "uvicorn._types", "uvicorn.config", "uvicorn.logging", "uvicorn.server", "__future__", "asyncio", "logging", "typing", "builtins", "abc", "anyio", "anyio._core", "anyio._core._synchronization", "asyncio.events", "asyncio.futures", "asyncio.locks", "asyncio.mixins", "asyncio.protocols", "asyncio.queues", "asyncio.tasks", "asyncio.transports", "<PERSON><PERSON><PERSON>", "importlib", "importlib.machinery", "types", "urllib", "uvicorn.protocols.http", "uvicorn.protocols.http.h11_impl", "uvicorn.protocols.http.httptools_impl", "uvicorn.protocols.websockets.websockets_impl"], "hash": "3f2c0feaa986736fa80f7ca5da74b430ed0b7dcb", "id": "uvicorn.protocols.websockets.wsproto_impl", "ignore_all": true, "interface_hash": "54412bbf342e60bbbcaba093456f86dd6f87133a", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/uvicorn/protocols/websockets/wsproto_impl.py", "plugin_data": null, "size": 15341, "suppressed": ["wsproto.connection", "wsproto.extensions", "wsproto.utilities", "wsproto"], "version_id": "1.13.0"}