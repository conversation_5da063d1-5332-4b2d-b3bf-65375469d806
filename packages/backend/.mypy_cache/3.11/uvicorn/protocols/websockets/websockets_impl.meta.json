{"data_mtime": 1751259991, "dep_lines": [10, 13, 14, 15, 34, 7, 10, 11, 12, 16, 17, 19, 32, 33, 41, 1, 3, 4, 5, 6, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 20, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["websockets.legacy.handshake", "websockets.extensions.base", "websockets.extensions.permessage_deflate", "websockets.legacy.server", "uvicorn.protocols.utils", "urllib.parse", "websockets.legacy", "websockets.datastructures", "websockets.exceptions", "websockets.server", "websockets.typing", "uvicorn._types", "uvicorn.config", "uvicorn.logging", "uvicorn.server", "__future__", "asyncio", "http", "logging", "typing", "websockets", "builtins", "abc", "anyio", "anyio._core", "anyio._core._synchronization", "asyncio.events", "asyncio.futures", "asyncio.locks", "asyncio.mixins", "asyncio.protocols", "asyncio.tasks", "asyncio.transports", "<PERSON><PERSON><PERSON>", "enum", "importlib", "importlib.machinery", "types", "typing_extensions", "urllib", "uvicorn.protocols.http", "uvicorn.protocols.http.h11_impl", "uvicorn.protocols.http.httptools_impl", "uvicorn.protocols.websockets.wsproto_impl", "websockets.extensions", "websockets.frames"], "hash": "8c03b6f4f38d472b5ae5e1671abef54ddefd59d8", "id": "uvicorn.protocols.websockets.websockets_impl", "ignore_all": true, "interface_hash": "1908428a00eb01a65a2d18e100fbb7eac04b7ae8", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/uvicorn/protocols/websockets/websockets_impl.py", "plugin_data": null, "size": 15490, "suppressed": [], "version_id": "1.13.0"}