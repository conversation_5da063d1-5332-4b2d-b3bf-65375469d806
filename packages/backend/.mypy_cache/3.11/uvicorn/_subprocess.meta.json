{"data_mtime": 1751259991, "dep_lines": [11, 15, 6, 8, 9, 10, 12, 13, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["multiprocessing.context", "uvicorn.config", "__future__", "multiprocessing", "os", "sys", "socket", "typing", "builtins", "_socket", "abc", "importlib", "importlib.machinery", "multiprocessing.process"], "hash": "cdae5819392fc1aca8426653dab73280275f6940", "id": "uvicorn._subprocess", "ignore_all": true, "interface_hash": "04b5ba3bfecfd8b88df32c4e05a1199033d1d74d", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", "plugin_data": null, "size": 2766, "suppressed": [], "version_id": "1.13.0"}