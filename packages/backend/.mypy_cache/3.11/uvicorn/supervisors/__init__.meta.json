{"data_mtime": 1751259991, "dep_lines": [5, 6, 1, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["uvicorn.supervisors.basereload", "uvicorn.supervisors.multiprocess", "__future__", "typing", "builtins", "abc", "importlib", "importlib.machinery"], "hash": "159dc5cdd2095bf6fa31a2fefd4854379e6d0609", "id": "uvicorn.supervisors", "ignore_all": true, "interface_hash": "20724b85f2dd6b7a14c81f35a89f8f3a1d572bff", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/uvicorn/supervisors/__init__.py", "plugin_data": null, "size": 700, "suppressed": [], "version_id": "1.13.0"}