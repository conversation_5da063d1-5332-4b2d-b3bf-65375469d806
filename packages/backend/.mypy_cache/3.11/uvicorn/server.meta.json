{"data_mtime": 1751259991, "dep_lines": [22, 23, 24, 25, 13, 19, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["uvicorn.protocols.http.h11_impl", "uvicorn.protocols.http.httptools_impl", "uvicorn.protocols.websockets.websockets_impl", "uvicorn.protocols.websockets.wsproto_impl", "email.utils", "uvicorn.config", "__future__", "asyncio", "contextlib", "logging", "os", "platform", "signal", "socket", "sys", "threading", "time", "types", "typing", "click", "builtins", "_socket", "_typeshed", "abc", "asyncio.base_events", "asyncio.events", "asyncio.futures", "asyncio.protocols", "asyncio.tasks", "click.termui", "email", "enum", "genericpath", "importlib", "importlib.machinery", "ssl", "uvicorn.protocols", "uvicorn.protocols.http", "uvicorn.protocols.websockets"], "hash": "1b90a14e6f13161ab20745fa784db2f8f3564fe8", "id": "uvicorn.server", "ignore_all": true, "interface_hash": "d22008485a1ec319c78e1d643dfae1dce9d84ed8", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/uvicorn/server.py", "plugin_data": null, "size": 13010, "suppressed": [], "version_id": "1.13.0"}