{"data_mtime": 1751259988, "dep_lines": [4, 11, 1, 3, 4, 5, 6, 7, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 198], "dep_prios": [10, 5, 5, 10, 20, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["concurrent.futures", "uvicorn._types", "__future__", "asyncio", "concurrent", "io", "sys", "warnings", "collections", "typing", "builtins", "_typeshed", "_warnings", "abc", "anyio", "anyio._core", "anyio._core._synchronization", "asyncio.events", "asyncio.futures", "asyncio.locks", "asyncio.mixins", "asyncio.tasks", "concurrent.futures._base", "concurrent.futures.thread", "<PERSON><PERSON><PERSON>", "importlib", "importlib.machinery", "types", "typing_extensions"], "hash": "23d7214a0d19fd1cd373026a2b60be4729039d14", "id": "uvicorn.middleware.wsgi", "ignore_all": true, "interface_hash": "d1039db23530e8f4d4f9bd2f219b1b51f1432319", "mtime": 1751256092, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/uvicorn/middleware/wsgi.py", "plugin_data": null, "size": 7111, "suppressed": ["a2wsgi"], "version_id": "1.13.0"}