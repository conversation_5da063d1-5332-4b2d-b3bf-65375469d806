{".class": "MypyFile", "_fullname": "uvicorn.middleware.message_logger", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGI3Application": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGI3Application", "kind": "Gdef"}, "ASGIReceiveCallable": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGIReceiveCallable", "kind": "Gdef"}, "ASGIReceiveEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGIReceiveEvent", "kind": "Gdef"}, "ASGISendCallable": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGISendCallable", "kind": "Gdef"}, "ASGISendEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGISendEvent", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "MessageLoggerMiddleware": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn.middleware.message_logger.MessageLoggerMiddleware", "name": "MessageLoggerMiddleware", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "uvicorn.middleware.message_logger.MessageLoggerMiddleware", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn.middleware.message_logger", "mro": ["uvicorn.middleware.message_logger.MessageLoggerMiddleware", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "uvicorn.middleware.message_logger.MessageLoggerMiddleware.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "arg_types": ["uvicorn.middleware.message_logger.MessageLoggerMiddleware", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WWWScope"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIReceiveCallable"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendCallable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of MessageLoggerMiddleware", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "app"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.middleware.message_logger.MessageLoggerMiddleware.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "app"], "arg_types": ["uvicorn.middleware.message_logger.MessageLoggerMiddleware", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGI3Application"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MessageLoggerMiddleware", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.middleware.message_logger.MessageLoggerMiddleware.app", "name": "app", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIReceiveCallable"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.middleware.message_logger.MessageLoggerMiddleware.logger", "name": "logger", "type": "logging.Logger"}}, "task_counter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.middleware.message_logger.MessageLoggerMiddleware.task_counter", "name": "task_counter", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "uvicorn.middleware.message_logger.MessageLoggerMiddleware.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "uvicorn.middleware.message_logger.MessageLoggerMiddleware", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PLACEHOLDER_FORMAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "uvicorn.middleware.message_logger.PLACEHOLDER_FORMAT", "name": "PLACEHOLDER_FORMAT", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "TRACE_LOG_LEVEL": {".class": "SymbolTableNode", "cross_ref": "uvicorn.logging.TRACE_LOG_LEVEL", "kind": "Gdef"}, "WWWScope": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WWWScope", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.middleware.message_logger.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.middleware.message_logger.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.middleware.message_logger.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.middleware.message_logger.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.middleware.message_logger.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.middleware.message_logger.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "message_with_placeholders": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["message"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.middleware.message_logger.message_with_placeholders", "name": "message_with_placeholders", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["message"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "message_with_placeholders", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/uvicorn/middleware/message_logger.py"}