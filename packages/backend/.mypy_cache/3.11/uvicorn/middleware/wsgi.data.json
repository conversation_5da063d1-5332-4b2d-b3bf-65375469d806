{".class": "MypyFile", "_fullname": "uvicorn.middleware.wsgi", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGIReceiveCallable": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGIReceiveCallable", "kind": "Gdef"}, "ASGIReceiveEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGIReceiveEvent", "kind": "Gdef"}, "ASGISendCallable": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGISendCallable", "kind": "Gdef"}, "ASGISendEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGISendEvent", "kind": "Gdef"}, "Environ": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.Environ", "kind": "Gdef"}, "ExcInfo": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ExcInfo", "kind": "Gdef"}, "HTTPRequestEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.HTTPRequestEvent", "kind": "Gdef"}, "HTTPResponseBodyEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.HTTPResponseBodyEvent", "kind": "Gdef"}, "HTTPResponseStartEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.HTTPResponseStartEvent", "kind": "Gdef"}, "HTTPScope": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.HTTPScope", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "StartResponse": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.StartResponse", "kind": "Gdef"}, "WSGIApp": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WSGIApp", "kind": "Gdef"}, "WSGIMiddleware": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "uvicorn.middleware.wsgi.WSGIMiddleware", "name": "WSGIMiddleware", "type": {".class": "AnyType", "missing_import_name": "uvicorn.middleware.wsgi.WSGIMiddleware", "source_any": null, "type_of_any": 3}}}, "WSGIResponder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn.middleware.wsgi.WSGIResponder", "name": "WSGIResponder", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "uvicorn.middleware.wsgi.WSGIResponder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn.middleware.wsgi", "mro": ["uvicorn.middleware.wsgi.WSGIResponder", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "receive", "send"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "uvicorn.middleware.wsgi.WSGIResponder.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "receive", "send"], "arg_types": ["uvicorn.middleware.wsgi.WSGIResponder", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIReceiveCallable"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendCallable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of WSGIResponder", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "app", "executor", "scope"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.middleware.wsgi.WSGIResponder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "app", "executor", "scope"], "arg_types": ["uvicorn.middleware.wsgi.WSGIResponder", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WSGIApp"}, "concurrent.futures.thread.ThreadPoolExecutor", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPScope"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WSGIResponder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.middleware.wsgi.WSGIResponder.app", "name": "app", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.Environ"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.StartResponse"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.BaseException"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exc_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.middleware.wsgi.WSGIResponder.exc_info", "name": "exc_info", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ExcInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "executor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.middleware.wsgi.WSGIResponder.executor", "name": "executor", "type": "concurrent.futures.thread.ThreadPoolExecutor"}}, "loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.middleware.wsgi.WSGIResponder.loop", "name": "loop", "type": "asyncio.events.AbstractEventLoop"}}, "response_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.middleware.wsgi.WSGIResponder.response_headers", "name": "response_headers", "type": {".class": "NoneType"}}}, "response_started": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.middleware.wsgi.WSGIResponder.response_started", "name": "response_started", "type": "builtins.bool"}}, "scope": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.middleware.wsgi.WSGIResponder.scope", "name": "scope", "type": {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPScope"}}}, "send_event": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.middleware.wsgi.WSGIResponder.send_event", "name": "send_event", "type": "asyncio.locks.Event"}}, "send_queue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.middleware.wsgi.WSGIResponder.send_queue", "name": "send_queue", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendEvent"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "collections.deque"}}}, "sender": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "send"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "uvicorn.middleware.wsgi.WSGIResponder.sender", "name": "sender", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "send"], "arg_types": ["uvicorn.middleware.wsgi.WSGIResponder", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendCallable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sender of WSGIResponder", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "status", "response_headers", "exc_info"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.middleware.wsgi.WSGIResponder.start_response", "name": "start_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "status", "response_headers", "exc_info"], "arg_types": ["uvicorn.middleware.wsgi.WSGIResponder", "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ExcInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_response of WSGIResponder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.middleware.wsgi.WSGIResponder.status", "name": "status", "type": {".class": "NoneType"}}}, "wsgi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "environ", "start_response"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.middleware.wsgi.WSGIResponder.wsgi", "name": "wsgi", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "environ", "start_response"], "arg_types": ["uvicorn.middleware.wsgi.WSGIResponder", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.Environ"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.StartResponse"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wsgi of WSGIResponder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "uvicorn.middleware.wsgi.WSGIResponder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "uvicorn.middleware.wsgi.WSGIResponder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WSGIMiddleware": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn.middleware.wsgi._WSGIMiddleware", "name": "_WSGIMiddleware", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "uvicorn.middleware.wsgi._WSGIMiddleware", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn.middleware.wsgi", "mro": ["uvicorn.middleware.wsgi._WSGIMiddleware", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "uvicorn.middleware.wsgi._WSGIMiddleware.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "arg_types": ["uvicorn.middleware.wsgi._WSGIMiddleware", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPScope"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIReceiveCallable"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendCallable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _WSGIMiddleware", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "app", "workers"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.middleware.wsgi._WSGIMiddleware.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "app", "workers"], "arg_types": ["uvicorn.middleware.wsgi._WSGIMiddleware", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WSGIApp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _WSGIMiddleware", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.middleware.wsgi._WSGIMiddleware.app", "name": "app", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.Environ"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.StartResponse"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.BaseException"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "executor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.middleware.wsgi._WSGIMiddleware.executor", "name": "executor", "type": "concurrent.futures.thread.ThreadPoolExecutor"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "uvicorn.middleware.wsgi._WSGIMiddleware.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "uvicorn.middleware.wsgi._WSGIMiddleware", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.middleware.wsgi.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.middleware.wsgi.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.middleware.wsgi.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.middleware.wsgi.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.middleware.wsgi.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.middleware.wsgi.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "build_environ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["scope", "message", "body"], "dataclass_transform_spec": null, "flags": [], "fullname": "uvicorn.middleware.wsgi.build_environ", "name": "build_environ", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["scope", "message", "body"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPScope"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIReceiveEvent"}, "io.BytesIO"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_environ", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.Environ"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "concurrent": {".class": "SymbolTableNode", "cross_ref": "concurrent", "kind": "Gdef"}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/uvicorn/middleware/wsgi.py"}