{".class": "MypyFile", "_fullname": "starlette._utils", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AwaitableCallable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T", "id": 1, "name": "T", "namespace": "starlette._utils.AwaitableCallable", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "starlette._utils.AwaitableCallable", "line": 24, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T", "id": 1, "name": "T", "namespace": "starlette._utils.AwaitableCallable", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "AwaitableOrContextManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__aexit__", 1], ["__await__", 1]], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T_co", "id": 1, "name": "T_co", "namespace": "starlette._utils.AwaitableOrContextManager", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T_co", "id": 1, "name": "T_co", "namespace": "starlette._utils.AwaitableOrContextManager", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.AsyncContextManager"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette._utils.AwaitableOrContextManager", "name": "AwaitableOrContextManager", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T_co", "id": 1, "name": "T_co", "namespace": "starlette._utils.AwaitableOrContextManager", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "starlette._utils.AwaitableOrContextManager", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "starlette._utils", "mro": ["starlette._utils.AwaitableOrContextManager", "typing.Awaitable", "typing.AsyncContextManager", "contextlib.AbstractAsyncContextManager", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.AwaitableOrContextManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T_co", "id": 1, "name": "T_co", "namespace": "starlette._utils.AwaitableOrContextManager", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "starlette._utils.AwaitableOrContextManager"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["T_co"], "typeddict_type": null}}, "AwaitableOrContextManagerWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette._utils.AwaitableOrContextManagerWrapper", "name": "AwaitableOrContextManagerWrapper", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.SupportsAsyncCloseType", "id": 1, "name": "SupportsAsyncCloseType", "namespace": "starlette._utils.AwaitableOrContextManagerWrapper", "upper_bound": "starlette._utils.SupportsAsyncClose", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "starlette._utils.AwaitableOrContextManagerWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette._utils", "mro": ["starlette._utils.AwaitableOrContextManagerWrapper", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette._utils.AwaitableOrContextManagerWrapper.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.SupportsAsyncCloseType", "id": 1, "name": "SupportsAsyncCloseType", "namespace": "starlette._utils.AwaitableOrContextManagerWrapper", "upper_bound": "starlette._utils.SupportsAsyncClose", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "starlette._utils.AwaitableOrContextManagerWrapper"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aenter__ of AwaitableOrContextManagerWrapper", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.SupportsAsyncCloseType", "id": 1, "name": "SupportsAsyncCloseType", "namespace": "starlette._utils.AwaitableOrContextManagerWrapper", "upper_bound": "starlette._utils.SupportsAsyncClose", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette._utils.AwaitableOrContextManagerWrapper.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.SupportsAsyncCloseType", "id": 1, "name": "SupportsAsyncCloseType", "namespace": "starlette._utils.AwaitableOrContextManagerWrapper", "upper_bound": "starlette._utils.SupportsAsyncClose", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "starlette._utils.AwaitableOrContextManagerWrapper"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aexit__ of AwaitableOrContextManagerWrapper", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__await__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette._utils.AwaitableOrContextManagerWrapper.__await__", "name": "__await__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.SupportsAsyncCloseType", "id": 1, "name": "SupportsAsyncCloseType", "namespace": "starlette._utils.AwaitableOrContextManagerWrapper", "upper_bound": "starlette._utils.SupportsAsyncClose", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "starlette._utils.AwaitableOrContextManagerWrapper"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__await__ of AwaitableOrContextManagerWrapper", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.SupportsAsyncCloseType", "id": 1, "name": "SupportsAsyncCloseType", "namespace": "starlette._utils.AwaitableOrContextManagerWrapper", "upper_bound": "starlette._utils.SupportsAsyncClose", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "aw"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette._utils.AwaitableOrContextManagerWrapper.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "aw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.SupportsAsyncCloseType", "id": 1, "name": "SupportsAsyncCloseType", "namespace": "starlette._utils.AwaitableOrContextManagerWrapper", "upper_bound": "starlette._utils.SupportsAsyncClose", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "starlette._utils.AwaitableOrContextManagerWrapper"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.SupportsAsyncCloseType", "id": 1, "name": "SupportsAsyncCloseType", "namespace": "starlette._utils.AwaitableOrContextManagerWrapper", "upper_bound": "starlette._utils.SupportsAsyncClose", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AwaitableOrContextManagerWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "starlette._utils.AwaitableOrContextManagerWrapper.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "aw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette._utils.AwaitableOrContextManagerWrapper.aw", "name": "aw", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.SupportsAsyncCloseType", "id": 1, "name": "SupportsAsyncCloseType", "namespace": "starlette._utils.AwaitableOrContextManagerWrapper", "upper_bound": "starlette._utils.SupportsAsyncClose", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Awaitable"}}}, "entered": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette._utils.AwaitableOrContextManagerWrapper.entered", "name": "entered", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.SupportsAsyncCloseType", "id": 1, "name": "SupportsAsyncCloseType", "namespace": "starlette._utils.AwaitableOrContextManagerWrapper", "upper_bound": "starlette._utils.SupportsAsyncClose", "values": [], "variance": 0}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.AwaitableOrContextManagerWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.SupportsAsyncCloseType", "id": 1, "name": "SupportsAsyncCloseType", "namespace": "starlette._utils.AwaitableOrContextManagerWrapper", "upper_bound": "starlette._utils.SupportsAsyncClose", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "starlette._utils.AwaitableOrContextManagerWrapper"}, "values": [], "variance": 0}, "slots": ["aw", "entered"], "tuple_type": null, "type_vars": ["SupportsAsyncCloseType"], "typeddict_type": null}}, "Scope": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Scope", "kind": "Gdef"}, "SupportsAsyncClose": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["close", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette._utils.SupportsAsyncClose", "name": "SupportsAsyncClose", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "starlette._utils.SupportsAsyncClose", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "starlette._utils", "mro": ["starlette._utils.SupportsAsyncClose", "builtins.object"], "names": {".class": "SymbolTable", "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine", "is_trivial_body"], "fullname": "starlette._utils.SupportsAsyncClose.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette._utils.SupportsAsyncClose"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of SupportsAsyncClose", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.SupportsAsyncClose.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette._utils.SupportsAsyncClose", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsAsyncCloseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.SupportsAsyncCloseType", "name": "SupportsAsyncCloseType", "upper_bound": "starlette._utils.SupportsAsyncClose", "values": [], "variance": 0}}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "T_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T_co", "name": "T_co", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing.TypeGuard", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette._utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette._utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette._utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette._utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette._utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette._utils.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "collapse_excgroups": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "starlette._utils.collapse_excgroups", "name": "collapse_excgroups", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collapse_excgroups", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "starlette._utils.collapse_excgroups", "name": "collapse_excgroups", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collapse_excgroups", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_route_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["scope"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette._utils.get_route_path", "name": "get_route_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["scope"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_route_path", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_exceptiongroups": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette._utils.has_exceptiongroups", "name": "has_exceptiongroups", "type": "builtins.bool"}}, "is_async_callable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "starlette._utils.is_async_callable", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "starlette._utils.is_async_callable", "name": "is_async_callable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_async_callable", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "starlette._utils.is_async_callable", "name": "is_async_callable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T", "id": -1, "name": "T", "namespace": "starlette._utils.is_async_callable#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "starlette._utils.AwaitableCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_async_callable", "ret_type": "builtins.bool", "type_guard": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T", "id": -1, "name": "T", "namespace": "starlette._utils.is_async_callable#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "starlette._utils.AwaitableCallable"}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T", "id": -1, "name": "T", "namespace": "starlette._utils.is_async_callable#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "starlette._utils.is_async_callable", "name": "is_async_callable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T", "id": -1, "name": "T", "namespace": "starlette._utils.is_async_callable#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "starlette._utils.AwaitableCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_async_callable", "ret_type": "builtins.bool", "type_guard": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T", "id": -1, "name": "T", "namespace": "starlette._utils.is_async_callable#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "starlette._utils.AwaitableCallable"}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T", "id": -1, "name": "T", "namespace": "starlette._utils.is_async_callable#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "starlette._utils.is_async_callable", "name": "is_async_callable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_async_callable", "ret_type": "builtins.bool", "type_guard": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "starlette._utils.AwaitableCallable"}, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "starlette._utils.is_async_callable", "name": "is_async_callable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_async_callable", "ret_type": "builtins.bool", "type_guard": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "starlette._utils.AwaitableCallable"}, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T", "id": -1, "name": "T", "namespace": "starlette._utils.is_async_callable#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "starlette._utils.AwaitableCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_async_callable", "ret_type": "builtins.bool", "type_guard": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T", "id": -1, "name": "T", "namespace": "starlette._utils.is_async_callable#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "starlette._utils.AwaitableCallable"}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette._utils.T", "id": -1, "name": "T", "namespace": "starlette._utils.is_async_callable#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_async_callable", "ret_type": "builtins.bool", "type_guard": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "starlette._utils.AwaitableCallable"}, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/starlette/_utils.py"}