{"data_mtime": **********, "dep_lines": [14, 15, 16, 12, 13, 17, 18, 19, 20, 21, 1, 3, 4, 5, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["starlette.middleware.base", "starlette.middleware.errors", "starlette.middleware.exceptions", "starlette.datastructures", "starlette.middleware", "starlette.requests", "starlette.responses", "starlette.routing", "starlette.types", "starlette.websockets", "__future__", "sys", "typing", "warnings", "builtins", "_typeshed", "abc", "contextlib", "importlib", "importlib.machinery"], "hash": "a51bb6aac63dc3a790bba929802b7da231f739e6", "id": "starlette.applications", "ignore_all": true, "interface_hash": "ea57da2afaa2b8123a1e27c0be86e8c2142afcd6", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/starlette/applications.py", "plugin_data": null, "size": 10447, "suppressed": [], "version_id": "1.13.0"}