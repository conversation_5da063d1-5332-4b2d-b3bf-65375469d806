{"data_mtime": **********, "dep_lines": [5, 9, 10, 11, 12, 13, 18, 19, 1, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 16], "dep_prios": [10, 5, 5, 5, 5, 5, 25, 25, 5, 10, 10, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25], "dependencies": ["http.cookies", "starlette._utils", "starlette.datastructures", "starlette.exceptions", "starlette.formparsers", "starlette.types", "starlette.applications", "starlette.routing", "__future__", "json", "typing", "http", "anyio", "builtins", "_typeshed", "abc", "anyio._core", "anyio._core._tasks", "contextlib", "importlib", "importlib.machinery", "json.decoder", "typing_extensions"], "hash": "5e1e4c4df59f62a02e6e5865d9679c18789b6b5b", "id": "starlette.requests", "ignore_all": true, "interface_hash": "874deaed82d2e3dabafe3db1e95aa9a42f3b9e11", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/starlette/requests.py", "plugin_data": null, "size": 11392, "suppressed": ["multipart.multipart"], "version_id": "1.13.0"}