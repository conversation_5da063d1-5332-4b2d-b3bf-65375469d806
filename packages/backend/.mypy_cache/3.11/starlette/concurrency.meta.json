{"data_mtime": 1751259988, "dep_lines": [8, 1, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["anyio.to_thread", "__future__", "functools", "sys", "typing", "warnings", "anyio", "builtins", "_typeshed", "abc", "anyio._core", "anyio._core._synchronization", "importlib", "importlib.machinery"], "hash": "68b4189caca85767274df122ae762d0ec319e0f9", "id": "starlette.concurrency", "ignore_all": true, "interface_hash": "ffd4d7364040f06dfa6ce13868bf6ea72b3503bc", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/starlette/concurrency.py", "plugin_data": null, "size": 1850, "suppressed": [], "version_id": "1.13.0"}