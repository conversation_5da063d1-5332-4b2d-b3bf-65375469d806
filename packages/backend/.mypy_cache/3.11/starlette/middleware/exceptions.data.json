{".class": "MypyFile", "_fullname": "starlette.middleware.exceptions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGIApp": {".class": "SymbolTableNode", "cross_ref": "starlette.types.ASGIApp", "kind": "Gdef"}, "ExceptionHandlers": {".class": "SymbolTableNode", "cross_ref": "starlette._exception_handler.ExceptionHandlers", "kind": "Gdef"}, "ExceptionMiddleware": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.middleware.exceptions.ExceptionMiddleware", "name": "ExceptionMiddleware", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.middleware.exceptions.ExceptionMiddleware", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.middleware.exceptions", "mro": ["starlette.middleware.exceptions.ExceptionMiddleware", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.middleware.exceptions.ExceptionMiddleware.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "arg_types": ["starlette.middleware.exceptions.ExceptionMiddleware", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Receive"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Send"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ExceptionMiddleware", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "app", "handlers", "debug"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.middleware.exceptions.ExceptionMiddleware.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "app", "handlers", "debug"], "arg_types": ["starlette.middleware.exceptions.ExceptionMiddleware", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.ASGIApp"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["starlette.requests.Request", "builtins.Exception"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "starlette.responses.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExceptionMiddleware", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_exception_handlers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "starlette.middleware.exceptions.ExceptionMiddleware._exception_handlers", "name": "_exception_handlers", "type": {".class": "TypeAliasType", "args": [], "type_ref": "starlette._exception_handler.ExceptionHandlers"}}}, "_status_handlers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "starlette.middleware.exceptions.ExceptionMiddleware._status_handlers", "name": "_status_handlers", "type": {".class": "TypeAliasType", "args": [], "type_ref": "starlette._exception_handler.StatusHandlers"}}}, "add_exception_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "exc_class_or_status_code", "handler"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.middleware.exceptions.ExceptionMiddleware.add_exception_handler", "name": "add_exception_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "exc_class_or_status_code", "handler"], "arg_types": ["starlette.middleware.exceptions.ExceptionMiddleware", {".class": "UnionType", "items": ["builtins.int", {".class": "TypeType", "item": "builtins.Exception"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["starlette.requests.Request", "builtins.Exception"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "starlette.responses.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_exception_handler of ExceptionMiddleware", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.middleware.exceptions.ExceptionMiddleware.app", "name": "app", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Receive"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Send"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "debug": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.middleware.exceptions.ExceptionMiddleware.debug", "name": "debug", "type": "builtins.bool"}}, "http_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "exc"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.middleware.exceptions.ExceptionMiddleware.http_exception", "name": "http_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "exc"], "arg_types": ["starlette.middleware.exceptions.ExceptionMiddleware", "starlette.requests.Request", "builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "http_exception of ExceptionMiddleware", "ret_type": "starlette.responses.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "websocket_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "websocket", "exc"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.middleware.exceptions.ExceptionMiddleware.websocket_exception", "name": "websocket_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "websocket", "exc"], "arg_types": ["starlette.middleware.exceptions.ExceptionMiddleware", "starlette.websockets.WebSocket", "builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "websocket_exception of ExceptionMiddleware", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.middleware.exceptions.ExceptionMiddleware.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.middleware.exceptions.ExceptionMiddleware", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPException": {".class": "SymbolTableNode", "cross_ref": "starlette.exceptions.HTTPException", "kind": "Gdef"}, "PlainTextResponse": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.PlainTextResponse", "kind": "Gdef"}, "Receive": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Receive", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "starlette.requests.Request", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.Response", "kind": "Gdef"}, "Scope": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Scope", "kind": "Gdef"}, "Send": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Send", "kind": "Gdef"}, "StatusHandlers": {".class": "SymbolTableNode", "cross_ref": "starlette._exception_handler.StatusHandlers", "kind": "Gdef"}, "WebSocket": {".class": "SymbolTableNode", "cross_ref": "starlette.websockets.WebSocket", "kind": "Gdef"}, "WebSocketException": {".class": "SymbolTableNode", "cross_ref": "starlette.exceptions.WebSocketException", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.exceptions.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "wrap_app_handling_exceptions": {".class": "SymbolTableNode", "cross_ref": "starlette._exception_handler.wrap_app_handling_exceptions", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py"}