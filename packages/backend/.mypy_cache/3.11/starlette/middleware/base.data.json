{".class": "MypyFile", "_fullname": "starlette.middleware.base", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGIApp": {".class": "SymbolTableNode", "cross_ref": "starlette.types.ASGIApp", "kind": "Gdef"}, "AsyncContentStream": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.AsyncContentStream", "kind": "Gdef"}, "BaseHTTPMiddleware": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.middleware.base.BaseHTTPMiddleware", "name": "BaseHTTPMiddleware", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.middleware.base.BaseHTTPMiddleware", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.middleware.base", "mro": ["starlette.middleware.base.BaseHTTPMiddleware", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.middleware.base.BaseHTTPMiddleware.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "arg_types": ["starlette.middleware.base.BaseHTTPMiddleware", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Receive"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Send"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of BaseHTTPMiddleware", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "app", "dispatch"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.middleware.base.BaseHTTPMiddleware.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "app", "dispatch"], "arg_types": ["starlette.middleware.base.BaseHTTPMiddleware", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.ASGIApp"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.middleware.base.DispatchFunction"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseHTTPMiddleware", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.middleware.base.BaseHTTPMiddleware.app", "name": "app", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Receive"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Send"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "call_next"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.middleware.base.BaseHTTPMiddleware.dispatch", "name": "dispatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "call_next"], "arg_types": ["starlette.middleware.base.BaseHTTPMiddleware", "starlette.requests.Request", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.middleware.base.RequestResponseEndpoint"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dispatch of BaseHTTPMiddleware", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "starlette.responses.Response"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dispatch_func": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.middleware.base.BaseHTTPMiddleware.dispatch_func", "name": "dispatch_func", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["starlette.requests.Request", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.middleware.base.RequestResponseEndpoint"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["starlette.responses.Response"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.middleware.base.BaseHTTPMiddleware.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.middleware.base.BaseHTTPMiddleware", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClientDisconnect": {".class": "SymbolTableNode", "cross_ref": "starlette.requests.ClientDisconnect", "kind": "Gdef"}, "DispatchFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "starlette.middleware.base.DispatchFunction", "line": 14, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["starlette.requests.Request", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.middleware.base.RequestResponseEndpoint"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["starlette.responses.Response"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Message": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Message", "kind": "Gdef"}, "ObjectReceiveStream": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._streams.ObjectReceiveStream", "kind": "Gdef"}, "ObjectSendStream": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._streams.ObjectSendStream", "kind": "Gdef"}, "Receive": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Receive", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "starlette.requests.Request", "kind": "Gdef"}, "RequestResponseEndpoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "starlette.middleware.base.RequestResponseEndpoint", "line": 13, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["starlette.requests.Request"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["starlette.responses.Response"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Response": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.Response", "kind": "Gdef"}, "Scope": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Scope", "kind": "Gdef"}, "Send": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Send", "kind": "Gdef"}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.middleware.base.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_CachedRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["starlette.requests.Request"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.middleware.base._CachedRequest", "name": "_CachedRequest", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.middleware.base._CachedRequest", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "starlette.middleware.base", "mro": ["starlette.middleware.base._CachedRequest", "starlette.requests.Request", "starlette.requests.HTTPConnection", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "scope", "receive"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.middleware.base._CachedRequest.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "scope", "receive"], "arg_types": ["starlette.middleware.base._CachedRequest", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Receive"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _CachedRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_wrapped_rc_stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.middleware.base._CachedRequest._wrapped_rc_stream", "name": "_wrapped_rc_stream", "type": {".class": "Instance", "args": ["builtins.bytes", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.AsyncGenerator"}}}, "_wrapped_rcv_consumed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.middleware.base._CachedRequest._wrapped_rcv_consumed", "name": "_wrapped_rcv_consumed", "type": "builtins.bool"}}, "_wrapped_rcv_disconnected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.middleware.base._CachedRequest._wrapped_rcv_disconnected", "name": "_wrapped_rcv_disconnected", "type": "builtins.bool"}}, "wrapped_receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.middleware.base._CachedRequest.wrapped_receive", "name": "wrapped_receive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.middleware.base._CachedRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrapped_receive of _CachedRequest", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Message"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.middleware.base._CachedRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.middleware.base._CachedRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_StreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["starlette.responses.Response"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.middleware.base._StreamingResponse", "name": "_StreamingResponse", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.middleware.base._StreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.middleware.base", "mro": ["starlette.middleware.base._StreamingResponse", "starlette.responses.Response", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.middleware.base._StreamingResponse.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "arg_types": ["starlette.middleware.base._StreamingResponse", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Receive"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Send"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _StreamingResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "content", "status_code", "headers", "media_type", "info"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.middleware.base._StreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "content", "status_code", "headers", "media_type", "info"], "arg_types": ["starlette.middleware.base._StreamingResponse", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.responses.AsyncContentStream"}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _StreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "body_iterator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.middleware.base._StreamingResponse.body_iterator", "name": "body_iterator", "type": {".class": "TypeAliasType", "args": [], "type_ref": "starlette.responses.AsyncContentStream"}}}, "info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.middleware.base._StreamingResponse.info", "name": "info", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.middleware.base._StreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.middleware.base._StreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.middleware.base.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "anyio": {".class": "SymbolTableNode", "cross_ref": "anyio", "kind": "Gdef"}, "collapse_excgroups": {".class": "SymbolTableNode", "cross_ref": "starlette._utils.collapse_excgroups", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/starlette/middleware/base.py"}