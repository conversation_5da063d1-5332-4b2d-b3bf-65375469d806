{"data_mtime": 1751259991, "dep_lines": [14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 1, 3, 4, 5, 6, 7, 8, 9, 10, 12, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["starlette._exception_handler", "starlette._utils", "starlette.concurrency", "starlette.convertors", "starlette.datastructures", "starlette.exceptions", "starlette.middleware", "starlette.requests", "starlette.responses", "starlette.types", "starlette.websockets", "__future__", "contextlib", "functools", "inspect", "re", "traceback", "types", "typing", "warnings", "enum", "builtins", "_typeshed", "_warnings", "abc", "importlib", "importlib.machinery"], "hash": "72f6434c5ed8fc1a42c3d4e1ae663e5e705e21c0", "id": "starlette.routing", "ignore_all": true, "interface_hash": "35a96c6a3d95783836fc49dc62d0b68f7835e8d7", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", "plugin_data": null, "size": 34507, "suppressed": [], "version_id": "1.13.0"}