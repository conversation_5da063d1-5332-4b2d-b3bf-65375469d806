{".class": "MypyFile", "_fullname": "starlette.responses", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncContentStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "starlette.responses.AsyncContentStream", "line": 206, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.responses.Content"}], "extra_attrs": null, "type_ref": "typing.AsyncIterable"}}}, "BackgroundTask": {".class": "SymbolTableNode", "cross_ref": "starlette.background.BackgroundTask", "kind": "Gdef"}, "Content": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "starlette.responses.Content", "line": 204, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "uses_pep604_syntax": false}}}, "ContentStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "starlette.responses.ContentStream", "line": 207, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.responses.AsyncContentStream"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.responses.SyncContentStream"}], "uses_pep604_syntax": false}}}, "FileResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["starlette.responses.Response"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.responses.FileResponse", "name": "FileResponse", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.responses.FileResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.responses", "mro": ["starlette.responses.FileResponse", "starlette.responses.Response", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.responses.FileResponse.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "arg_types": ["starlette.responses.FileResponse", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Receive"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Send"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FileResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "path", "status_code", "headers", "media_type", "background", "filename", "stat_result", "method", "content_disposition_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.responses.FileResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "path", "status_code", "headers", "media_type", "background", "filename", "stat_result", "method", "content_disposition_type"], "arg_types": ["starlette.responses.FileResponse", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["starlette.background.BackgroundTask", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "os.stat_result"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FileResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_multiple_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "send", "ranges", "file_size", "send_header_only"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.responses.FileResponse._handle_multiple_ranges", "name": "_handle_multiple_ranges", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "send", "ranges", "file_size", "send_header_only"], "arg_types": ["starlette.responses.FileResponse", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Send"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_multiple_ranges of FileResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_simple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "send", "send_header_only"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.responses.FileResponse._handle_simple", "name": "_handle_simple", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "send", "send_header_only"], "arg_types": ["starlette.responses.FileResponse", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Send"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_simple of FileResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_single_range": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "send", "start", "end", "file_size", "send_header_only"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.responses.FileResponse._handle_single_range", "name": "_handle_single_range", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "send", "start", "end", "file_size", "send_header_only"], "arg_types": ["starlette.responses.FileResponse", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Send"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_single_range of FileResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_range_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_range", "file_size"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "starlette.responses.FileResponse._parse_range_header", "name": "_parse_range_header", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_range", "file_size"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_range_header of FileResponse", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "starlette.responses.FileResponse._parse_range_header", "name": "_parse_range_header", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_range", "file_size"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_range_header of FileResponse", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_should_use_range": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "http_if_range", "stat_result"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "starlette.responses.FileResponse._should_use_range", "name": "_should_use_range", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "http_if_range", "stat_result"], "arg_types": [{".class": "TypeType", "item": "starlette.responses.FileResponse"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "os.stat_result"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_use_range of FileResponse", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "starlette.responses.FileResponse._should_use_range", "name": "_should_use_range", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "http_if_range", "stat_result"], "arg_types": [{".class": "TypeType", "item": "starlette.responses.FileResponse"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "os.stat_result"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_use_range of FileResponse", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "chunk_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.responses.FileResponse.chunk_size", "name": "chunk_size", "type": "builtins.int"}}, "filename": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.responses.FileResponse.filename", "name": "filename", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "generate_multipart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "ranges", "boundary", "max_size", "content_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.responses.FileResponse.generate_multipart", "name": "generate_multipart", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "ranges", "boundary", "max_size", "content_type"], "arg_types": ["starlette.responses.FileResponse", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_multipart of FileResponse", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.responses.FileResponse.path", "name": "path", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}}}, "set_stat_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stat_result"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.responses.FileResponse.set_stat_headers", "name": "set_stat_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stat_result"], "arg_types": ["starlette.responses.FileResponse", {".class": "TypeAliasType", "args": [], "type_ref": "os.stat_result"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_stat_headers of FileResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stat_result": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.responses.FileResponse.stat_result", "name": "stat_result", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "os.stat_result"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.responses.FileResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.responses.FileResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTMLResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["starlette.responses.Response"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.responses.HTMLResponse", "name": "HTMLResponse", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.responses.HTMLResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.responses", "mro": ["starlette.responses.HTMLResponse", "starlette.responses.Response", "builtins.object"], "names": {".class": "SymbolTable", "media_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "starlette.responses.HTMLResponse.media_type", "name": "media_type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.responses.HTMLResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.responses.HTMLResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Headers": {".class": "SymbolTableNode", "cross_ref": "starlette.datastructures.Headers", "kind": "Gdef"}, "JSONResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["starlette.responses.Response"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.responses.JSONResponse", "name": "JSONResponse", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.responses.JSONResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.responses", "mro": ["starlette.responses.JSONResponse", "starlette.responses.Response", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "content", "status_code", "headers", "media_type", "background"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.responses.JSONResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "content", "status_code", "headers", "media_type", "background"], "arg_types": ["starlette.responses.JSONResponse", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["starlette.background.BackgroundTask", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of JSONResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "media_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "starlette.responses.JSONResponse.media_type", "name": "media_type", "type": "builtins.str"}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.responses.JSONResponse.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["starlette.responses.JSONResponse", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of JSONResponse", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.responses.JSONResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.responses.JSONResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MalformedRangeHeader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.responses.MalformedRangeHeader", "name": "MalformedRangeHeader", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.responses.MalformedRangeHeader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.responses", "mro": ["starlette.responses.MalformedRangeHeader", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.responses.MalformedRangeHeader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "content"], "arg_types": ["starlette.responses.MalformedRangeHeader", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MalformedRangeHeader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.responses.MalformedRangeHeader.content", "name": "content", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.responses.MalformedRangeHeader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.responses.MalformedRangeHeader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MutableHeaders": {".class": "SymbolTableNode", "cross_ref": "starlette.datastructures.MutableHeaders", "kind": "Gdef"}, "PlainTextResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["starlette.responses.Response"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.responses.PlainTextResponse", "name": "PlainTextResponse", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.responses.PlainTextResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.responses", "mro": ["starlette.responses.PlainTextResponse", "starlette.responses.Response", "builtins.object"], "names": {".class": "SymbolTable", "media_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "starlette.responses.PlainTextResponse.media_type", "name": "media_type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.responses.PlainTextResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.responses.PlainTextResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RangeNotSatisfiable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.responses.RangeNotSatisfiable", "name": "RangeNotSatisfiable", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.responses.RangeNotSatisfiable", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.responses", "mro": ["starlette.responses.RangeNotSatisfiable", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_size"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.responses.RangeNotSatisfiable.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_size"], "arg_types": ["starlette.responses.RangeNotSatisfiable", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RangeNotSatisfiable", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.responses.RangeNotSatisfiable.max_size", "name": "max_size", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.responses.RangeNotSatisfiable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.responses.RangeNotSatisfiable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Receive": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Receive", "kind": "Gdef"}, "RedirectResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["starlette.responses.Response"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.responses.RedirectResponse", "name": "RedirectResponse", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.responses.RedirectResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.responses", "mro": ["starlette.responses.RedirectResponse", "starlette.responses.Response", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "url", "status_code", "headers", "background"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.responses.RedirectResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "url", "status_code", "headers", "background"], "arg_types": ["starlette.responses.RedirectResponse", {".class": "UnionType", "items": ["builtins.str", "starlette.datastructures.URL"], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["starlette.background.BackgroundTask", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RedirectResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.responses.RedirectResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.responses.RedirectResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.responses.Response", "name": "Response", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.responses.Response", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.responses", "mro": ["starlette.responses.Response", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.responses.Response.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "arg_types": ["starlette.responses.Response", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Receive"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Send"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of Response", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "content", "status_code", "headers", "media_type", "background"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.responses.Response.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "content", "status_code", "headers", "media_type", "background"], "arg_types": ["starlette.responses.Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["starlette.background.BackgroundTask", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Response", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.responses.Response._headers", "name": "_headers", "type": "starlette.datastructures.MutableHeaders"}}, "background": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.responses.Response.background", "name": "background", "type": {".class": "UnionType", "items": ["starlette.background.BackgroundTask", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.responses.Response.body", "name": "body", "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "uses_pep604_syntax": false}}}, "charset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.responses.Response.charset", "name": "charset", "type": "builtins.str"}}, "delete_cookie": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "path", "domain", "secure", "httponly", "samesite"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.responses.Response.delete_cookie", "name": "delete_cookie", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "path", "domain", "secure", "httponly", "samesite"], "arg_types": ["starlette.responses.Response", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lax"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_cookie of Response", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "starlette.responses.Response.headers", "name": "headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.responses.Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "headers of Response", "ret_type": "starlette.datastructures.MutableHeaders", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "starlette.responses.Response.headers", "name": "headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["starlette.responses.Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "headers of Response", "ret_type": "starlette.datastructures.MutableHeaders", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "init_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.responses.Response.init_headers", "name": "init_headers", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "headers"], "arg_types": ["starlette.responses.Response", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_headers of Response", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "media_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "starlette.responses.Response.media_type", "name": "media_type", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "raw_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.responses.Response.raw_headers", "name": "raw_headers", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.responses.Response.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["starlette.responses.Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of Response", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_cookie": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "value", "max_age", "expires", "path", "domain", "secure", "httponly", "samesite"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.responses.Response.set_cookie", "name": "set_cookie", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "value", "max_age", "expires", "path", "domain", "secure", "httponly", "samesite"], "arg_types": ["starlette.responses.Response", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.datetime", "builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lax"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_cookie of Response", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "status_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "starlette.responses.Response.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.responses.Response.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.responses.Response", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Scope": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Scope", "kind": "Gdef"}, "Send": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Send", "kind": "Gdef"}, "StreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["starlette.responses.Response"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "starlette.responses.StreamingResponse", "name": "StreamingResponse", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "starlette.responses.StreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "starlette.responses", "mro": ["starlette.responses.StreamingResponse", "starlette.responses.Response", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.responses.StreamingResponse.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "scope", "receive", "send"], "arg_types": ["starlette.responses.StreamingResponse", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Receive"}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Send"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of StreamingResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "content", "status_code", "headers", "media_type", "background"], "dataclass_transform_spec": null, "flags": [], "fullname": "starlette.responses.StreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "content", "status_code", "headers", "media_type", "background"], "arg_types": ["starlette.responses.StreamingResponse", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.responses.ContentStream"}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["starlette.background.BackgroundTask", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "body_iterator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "starlette.responses.StreamingResponse.body_iterator", "name": "body_iterator", "type": {".class": "TypeAliasType", "args": [], "type_ref": "starlette.responses.AsyncContentStream"}}}, "listen_for_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "receive"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.responses.StreamingResponse.listen_for_disconnect", "name": "listen_for_disconnect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "receive"], "arg_types": ["starlette.responses.StreamingResponse", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Receive"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "listen_for_disconnect of StreamingResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stream_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "send"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "starlette.responses.StreamingResponse.stream_response", "name": "stream_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "send"], "arg_types": ["starlette.responses.StreamingResponse", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.Send"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream_response of StreamingResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "starlette.responses.StreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "starlette.responses.StreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SyncContentStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "starlette.responses.SyncContentStream", "line": 205, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.responses.Content"}], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "URL": {".class": "SymbolTableNode", "cross_ref": "starlette.datastructures.URL", "kind": "Gdef"}, "_RANGE_PATTERN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "starlette.responses._RANGE_PATTERN", "name": "_RANGE_PATTERN", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.responses.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.responses.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.responses.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.responses.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.responses.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.responses.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "anyio": {".class": "SymbolTableNode", "cross_ref": "anyio", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "format_datetime": {".class": "SymbolTableNode", "cross_ref": "email.utils.format_datetime", "kind": "Gdef"}, "formatdate": {".class": "SymbolTableNode", "cross_ref": "email.utils.formatdate", "kind": "Gdef"}, "guess_type": {".class": "SymbolTableNode", "cross_ref": "mimetypes.guess_type", "kind": "Gdef"}, "http": {".class": "SymbolTableNode", "cross_ref": "http", "kind": "Gdef"}, "iterate_in_threadpool": {".class": "SymbolTableNode", "cross_ref": "starlette.concurrency.iterate_in_threadpool", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "md5_hexdigest": {".class": "SymbolTableNode", "cross_ref": "starlette._compat.md5_hexdigest", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "quote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.quote", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "stat": {".class": "SymbolTableNode", "cross_ref": "stat", "kind": "Gdef"}, "token_hex": {".class": "SymbolTableNode", "cross_ref": "secrets.token_hex", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/starlette/responses.py"}