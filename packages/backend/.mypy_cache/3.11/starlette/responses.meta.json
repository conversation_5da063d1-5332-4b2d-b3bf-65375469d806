{"data_mtime": 1751259991, "dep_lines": [3, 11, 15, 18, 20, 21, 22, 23, 24, 1, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 17, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 5, 5, 5, 5, 5, 5, 20, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["http.cookies", "email.utils", "urllib.parse", "anyio.to_thread", "starlette._compat", "starlette.background", "starlette.concurrency", "starlette.datastructures", "starlette.types", "__future__", "http", "json", "os", "re", "stat", "typing", "warnings", "datetime", "functools", "mimetypes", "secrets", "anyio", "builtins", "_typeshed", "_warnings", "abc", "importlib", "importlib.machinery", "typing_extensions", "urllib"], "hash": "4a6584a612a49ed03a98317290f364d22ad4df23", "id": "starlette.responses", "ignore_all": true, "interface_hash": "ddad48119c5deb0cce7c5ef9b323d043e052de5e", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/starlette/responses.py", "plugin_data": null, "size": 20050, "suppressed": [], "version_id": "1.13.0"}