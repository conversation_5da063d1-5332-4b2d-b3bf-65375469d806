{"data_mtime": 1751259990, "dep_lines": [17, 4, 7, 8, 16, 1, 3, 5, 7, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["websockets.extensions.base", "collections.abc", "websockets.frames", "websockets.exceptions", "websockets.typing", "__future__", "zlib", "typing", "websockets", "builtins", "abc", "enum", "importlib", "importlib.machinery", "typing_extensions"], "hash": "c1c69de930a11ef4845fa6059013a703028fa863", "id": "websockets.extensions.permessage_deflate", "ignore_all": true, "interface_hash": "22bb21336280bfb853c41a441adb2bb1ff7ac02c", "mtime": 1751256088, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/websockets/extensions/permessage_deflate.py", "plugin_data": null, "size": 25711, "suppressed": [], "version_id": "1.13.0"}