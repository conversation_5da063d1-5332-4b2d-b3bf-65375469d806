{".class": "MypyFile", "_fullname": "websockets.extensions.base", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ClientExtensionFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.extensions.base.ClientExtensionFactory", "name": "ClientExtensionFactory", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.extensions.base.ClientExtensionFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.extensions.base", "mro": ["websockets.extensions.base.ClientExtensionFactory", "builtins.object"], "names": {".class": "SymbolTable", "get_request_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.extensions.base.ClientExtensionFactory.get_request_params", "name": "get_request_params", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.extensions.base.ClientExtensionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_request_params of ClientExtensionFactory", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionParameter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "websockets.extensions.base.ClientExtensionFactory.name", "name": "name", "type": "websockets.typing.ExtensionName"}}, "process_response_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "params", "accepted_extensions"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.extensions.base.ClientExtensionFactory.process_response_params", "name": "process_response_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "params", "accepted_extensions"], "arg_types": ["websockets.extensions.base.ClientExtensionFactory", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionParameter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["websockets.extensions.base.Extension"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_response_params of ClientExtensionFactory", "ret_type": "websockets.extensions.base.Extension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.extensions.base.ClientExtensionFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.extensions.base.ClientExtensionFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Extension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.extensions.base.Extension", "name": "Extension", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.extensions.base.Extension", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.extensions.base", "mro": ["websockets.extensions.base.Extension", "builtins.object"], "names": {".class": "SymbolTable", "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "frame", "max_size"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.extensions.base.Extension.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "frame", "max_size"], "arg_types": ["websockets.extensions.base.Extension", "websockets.frames.Frame", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decode of Extension", "ret_type": "websockets.frames.Frame", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.extensions.base.Extension.encode", "name": "encode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "arg_types": ["websockets.extensions.base.Extension", "websockets.frames.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode of Extension", "ret_type": "websockets.frames.Frame", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "websockets.extensions.base.Extension.name", "name": "name", "type": "websockets.typing.ExtensionName"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.extensions.base.Extension.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.extensions.base.Extension", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExtensionName": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.ExtensionName", "kind": "Gdef", "module_public": false}, "ExtensionParameter": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.ExtensionParameter", "kind": "Gdef", "module_public": false}, "Frame": {".class": "SymbolTableNode", "cross_ref": "websockets.frames.Frame", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "ServerExtensionFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.extensions.base.ServerExtensionFactory", "name": "ServerExtensionFactory", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.extensions.base.ServerExtensionFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.extensions.base", "mro": ["websockets.extensions.base.ServerExtensionFactory", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "websockets.extensions.base.ServerExtensionFactory.name", "name": "name", "type": "websockets.typing.ExtensionName"}}, "process_request_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "params", "accepted_extensions"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.extensions.base.ServerExtensionFactory.process_request_params", "name": "process_request_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "params", "accepted_extensions"], "arg_types": ["websockets.extensions.base.ServerExtensionFactory", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionParameter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["websockets.extensions.base.Extension"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_request_params of ServerExtensionFactory", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionParameter"}], "extra_attrs": null, "type_ref": "builtins.list"}, "websockets.extensions.base.Extension"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.extensions.base.ServerExtensionFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.extensions.base.ServerExtensionFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.extensions.base.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.extensions.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.extensions.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.extensions.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.extensions.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.extensions.base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.extensions.base.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/websockets/extensions/base.py"}