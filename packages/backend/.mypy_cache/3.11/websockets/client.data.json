{".class": "MypyFile", "_fullname": "websockets.client", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BACKOFF_FACTOR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.client.BACKOFF_FACTOR", "name": "BACKOFF_FACTOR", "type": "builtins.float"}}, "BACKOFF_INITIAL_DELAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.client.BACKOFF_INITIAL_DELAY", "name": "BACKOFF_INITIAL_DELAY", "type": "builtins.float"}}, "BACKOFF_MAX_DELAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.client.BACKOFF_MAX_DELAY", "name": "BACKOFF_MAX_DELAY", "type": "builtins.float"}}, "BACKOFF_MIN_DELAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.client.BACKOFF_MIN_DELAY", "name": "BACKOFF_MIN_DELAY", "type": "builtins.float"}}, "CLIENT": {".class": "SymbolTableNode", "cross_ref": "websockets.protocol.CLIENT", "kind": "Gdef", "module_public": false}, "CONNECTING": {".class": "SymbolTableNode", "cross_ref": "websockets.protocol.CONNECTING", "kind": "Gdef", "module_public": false}, "ClientConnection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.client.ClientProtocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.client.ClientConnection", "name": "ClientConnection", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.client.ClientConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.client", "mro": ["websockets.client.ClientConnection", "websockets.client.ClientProtocol", "websockets.protocol.Protocol", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.client.ClientConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["websockets.client.ClientConnection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClientConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.client.ClientConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.client.ClientConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClientExtensionFactory": {".class": "SymbolTableNode", "cross_ref": "websockets.extensions.base.ClientExtensionFactory", "kind": "Gdef", "module_public": false}, "ClientProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.protocol.Protocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.client.ClientProtocol", "name": "ClientProtocol", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.client.ClientProtocol", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.client", "mro": ["websockets.client.ClientProtocol", "websockets.protocol.Protocol", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "uri", "origin", "extensions", "subprotocols", "state", "max_size", "logger"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.client.ClientProtocol.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "uri", "origin", "extensions", "subprotocols", "state", "max_size", "logger"], "arg_types": ["websockets.client.ClientProtocol", "websockets.uri.WebSocketURI", {".class": "UnionType", "items": ["websockets.typing.Origin", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.extensions.base.ClientExtensionFactory"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.typing.Subprotocol"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "websockets.protocol.State", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.LoggerLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClientProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "available_extensions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.client.ClientProtocol.available_extensions", "name": "available_extensions", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.extensions.base.ClientExtensionFactory"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "available_subprotocols": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.client.ClientProtocol.available_subprotocols", "name": "available_subprotocols", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.typing.Subprotocol"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.client.ClientProtocol.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.client.ClientProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect of ClientProtocol", "ret_type": "websockets.http11.Request", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.client.ClientProtocol.key", "name": "key", "type": "builtins.str"}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_generator"], "fullname": "websockets.client.ClientProtocol.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.client.ClientProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of ClientProtocol", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_extensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.client.ClientProtocol.process_extensions", "name": "process_extensions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["websockets.client.ClientProtocol", "websockets.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_extensions of ClientProtocol", "ret_type": {".class": "Instance", "args": ["websockets.extensions.base.Extension"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.client.ClientProtocol.process_response", "name": "process_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["websockets.client.ClientProtocol", "websockets.http11.Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_response of ClientProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_subprotocol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.client.ClientProtocol.process_subprotocol", "name": "process_subprotocol", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["websockets.client.ClientProtocol", "websockets.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_subprotocol of ClientProtocol", "ret_type": {".class": "UnionType", "items": ["websockets.typing.Subprotocol", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.client.ClientProtocol.send_request", "name": "send_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["websockets.client.ClientProtocol", "websockets.http11.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_request of ClientProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "uri": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.client.ClientProtocol.uri", "name": "uri", "type": "websockets.uri.WebSocketURI"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.client.ClientProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.client.ClientProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionOption": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.ConnectionOption", "kind": "Gdef", "module_public": false}, "Extension": {".class": "SymbolTableNode", "cross_ref": "websockets.extensions.base.Extension", "kind": "Gdef", "module_public": false}, "ExtensionHeader": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.ExtensionHeader", "kind": "Gdef", "module_public": false}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_public": false}, "Headers": {".class": "SymbolTableNode", "cross_ref": "websockets.datastructures.Headers", "kind": "Gdef", "module_public": false}, "InvalidHandshake": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidHandshake", "kind": "Gdef", "module_public": false}, "InvalidHeader": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidHeader", "kind": "Gdef", "module_public": false}, "InvalidHeaderValue": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidHeaderValue", "kind": "Gdef", "module_public": false}, "InvalidMessage": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidMessage", "kind": "Gdef", "module_public": false}, "InvalidStatus": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidStatus", "kind": "Gdef", "module_public": false}, "InvalidUpgrade": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidUpgrade", "kind": "Gdef", "module_public": false}, "LoggerLike": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.LoggerLike", "kind": "Gdef", "module_public": false}, "MultipleValuesError": {".class": "SymbolTableNode", "cross_ref": "websockets.datastructures.MultipleValuesError", "kind": "Gdef", "module_public": false}, "NegotiationError": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.NegotiationError", "kind": "Gdef", "module_public": false}, "OPEN": {".class": "SymbolTableNode", "cross_ref": "websockets.protocol.OPEN", "kind": "Gdef", "module_public": false}, "Origin": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.Origin", "kind": "Gdef", "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "websockets.protocol.Protocol", "kind": "Gdef", "module_public": false}, "Request": {".class": "SymbolTableNode", "cross_ref": "websockets.http11.Request", "kind": "Gdef", "module_public": false}, "Response": {".class": "SymbolTableNode", "cross_ref": "websockets.http11.Response", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "State": {".class": "SymbolTableNode", "cross_ref": "websockets.protocol.State", "kind": "Gdef", "module_public": false}, "Subprotocol": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.Subprotocol", "kind": "Gdef", "module_public": false}, "UpgradeProtocol": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.UpgradeProtocol", "kind": "Gdef", "module_public": false}, "WebSocketURI": {".class": "SymbolTableNode", "cross_ref": "websockets.uri.WebSocketURI", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.client.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.client.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.client.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.client.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.client.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.client.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.client.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "accept_key": {".class": "SymbolTableNode", "cross_ref": "websockets.utils.accept_key", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "backoff": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1], "arg_names": ["initial_delay", "min_delay", "max_delay", "factor"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.client.backoff", "name": "backoff", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["initial_delay", "min_delay", "max_delay", "factor"], "arg_types": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backoff", "ret_type": {".class": "Instance", "args": ["builtins.float", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_authorization_basic": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.build_authorization_basic", "kind": "Gdef", "module_public": false}, "build_extension": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.build_extension", "kind": "Gdef", "module_public": false}, "build_host": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.build_host", "kind": "Gdef", "module_public": false}, "build_subprotocol": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.build_subprotocol", "kind": "Gdef", "module_public": false}, "generate_key": {".class": "SymbolTableNode", "cross_ref": "websockets.utils.generate_key", "kind": "Gdef", "module_public": false}, "lazy_import": {".class": "SymbolTableNode", "cross_ref": "websockets.imports.lazy_import", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "parse_connection": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.parse_connection", "kind": "Gdef", "module_public": false}, "parse_extension": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.parse_extension", "kind": "Gdef", "module_public": false}, "parse_subprotocol": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.parse_subprotocol", "kind": "Gdef", "module_public": false}, "parse_upgrade": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.parse_upgrade", "kind": "Gdef", "module_public": false}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/websockets/client.py"}