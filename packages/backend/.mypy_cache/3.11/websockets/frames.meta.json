{"data_mtime": 1751259990, "dep_lines": [9, 12, 18, 16, 430, 1, 3, 4, 5, 6, 7, 8, 10, 430, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 10, 10, 10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "websockets.exceptions", "websockets.utils", "websockets.speedups", "websockets.extensions", "__future__", "dataclasses", "enum", "io", "os", "secrets", "struct", "typing", "websockets", "builtins", "_typeshed", "abc", "importlib", "importlib.machinery", "typing_extensions", "websockets.extensions.base"], "hash": "49eafee5e8501c24585216c62229a943346163aa", "id": "websockets.frames", "ignore_all": true, "interface_hash": "e54c4b8d9352fad726a06301a82bd61383e818de", "mtime": 1751256088, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/websockets/frames.py", "plugin_data": null, "size": 12759, "suppressed": [], "version_id": "1.13.0"}