{"data_mtime": 1751259990, "dep_lines": [85, 86, 87, 6, 7, 95, 96, 97, 125, 126, 127, 128, 129, 1, 4, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 30, 30, 30], "dependencies": ["websockets.asyncio.client", "websockets.asyncio.router", "websockets.asyncio.server", "websockets.imports", "websockets.version", "websockets.client", "websockets.datastructures", "websockets.exceptions", "websockets.frames", "websockets.http11", "websockets.protocol", "websockets.server", "websockets.typing", "__future__", "typing", "builtins", "abc", "importlib", "importlib.machinery"], "hash": "ca078aa8fd739739d545a9ad00e8af71b32bc97d", "id": "websockets", "ignore_all": true, "interface_hash": "9c73f42f62c11d6420e72be8110650bbf12c0388", "mtime": 1751256088, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/websockets/__init__.py", "plugin_data": null, "size": 7058, "suppressed": [], "version_id": "1.13.0"}