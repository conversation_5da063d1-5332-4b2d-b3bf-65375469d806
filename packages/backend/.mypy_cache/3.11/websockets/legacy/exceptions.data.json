{".class": "MypyFile", "_fullname": "websockets.legacy.exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbortHandshake": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.InvalidHandshake"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.legacy.exceptions.AbortHandshake", "name": "AbortHandshake", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.legacy.exceptions.AbortHandshake", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.legacy.exceptions", "mro": ["websockets.legacy.exceptions.AbortHandshake", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "status", "headers", "body"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.legacy.exceptions.AbortHandshake.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "status", "headers", "body"], "arg_types": ["websockets.legacy.exceptions.AbortHandshake", {".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.StatusLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "websockets.datastructures.HeadersLike"}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AbortHandshake", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.legacy.exceptions.AbortHandshake.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["websockets.legacy.exceptions.AbortHandshake"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of AbortHandshake", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.legacy.exceptions.AbortHandshake.body", "name": "body", "type": "builtins.bytes"}}, "headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.legacy.exceptions.AbortHandshake.headers", "name": "headers", "type": "websockets.datastructures.Headers"}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.legacy.exceptions.AbortHandshake.status", "name": "status", "type": "http.HTTPStatus"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.exceptions.AbortHandshake.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.legacy.exceptions.AbortHandshake", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidHandshake": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidHandshake", "kind": "Gdef"}, "InvalidMessage": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidMessage", "kind": "Gdef"}, "InvalidStatusCode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.InvalidHandshake"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.legacy.exceptions.InvalidStatusCode", "name": "InvalidStatusCode", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.legacy.exceptions.InvalidStatusCode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.legacy.exceptions", "mro": ["websockets.legacy.exceptions.InvalidStatusCode", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "status_code", "headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.legacy.exceptions.InvalidStatusCode.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "status_code", "headers"], "arg_types": ["websockets.legacy.exceptions.InvalidStatusCode", "builtins.int", "websockets.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidStatusCode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.legacy.exceptions.InvalidStatusCode.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["websockets.legacy.exceptions.InvalidStatusCode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of InvalidStatusCode", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.legacy.exceptions.InvalidStatusCode.headers", "name": "headers", "type": "websockets.datastructures.Headers"}}, "status_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.legacy.exceptions.InvalidStatusCode.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.exceptions.InvalidStatusCode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.legacy.exceptions.InvalidStatusCode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RedirectHandshake": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.InvalidHandshake"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.legacy.exceptions.RedirectHandshake", "name": "RedirectHandshake", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.legacy.exceptions.RedirectHandshake", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.legacy.exceptions", "mro": ["websockets.legacy.exceptions.RedirectHandshake", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.legacy.exceptions.RedirectHandshake.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "arg_types": ["websockets.legacy.exceptions.RedirectHandshake", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RedirectHandshake", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.legacy.exceptions.RedirectHandshake.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["websockets.legacy.exceptions.RedirectHandshake"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of RedirectHandshake", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "uri": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.legacy.exceptions.RedirectHandshake.uri", "name": "uri", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.exceptions.RedirectHandshake.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.legacy.exceptions.RedirectHandshake", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StatusLike": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.StatusLike", "kind": "Gdef"}, "WebSocketProtocolError": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.ProtocolError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.exceptions.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "datastructures": {".class": "SymbolTableNode", "cross_ref": "websockets.datastructures", "kind": "Gdef"}, "http": {".class": "SymbolTableNode", "cross_ref": "http", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/websockets/legacy/exceptions.py"}