{".class": "MypyFile", "_fullname": "websockets.legacy.handshake", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ConnectionOption": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.ConnectionOption", "kind": "Gdef", "module_public": false}, "Headers": {".class": "SymbolTableNode", "cross_ref": "websockets.datastructures.Headers", "kind": "Gdef", "module_public": false}, "InvalidHeader": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidHeader", "kind": "Gdef", "module_public": false}, "InvalidHeaderValue": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidHeaderValue", "kind": "Gdef", "module_public": false}, "InvalidUpgrade": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidUpgrade", "kind": "Gdef", "module_public": false}, "MultipleValuesError": {".class": "SymbolTableNode", "cross_ref": "websockets.datastructures.MultipleValuesError", "kind": "Gdef", "module_public": false}, "UpgradeProtocol": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.UpgradeProtocol", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.legacy.handshake.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.handshake.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.handshake.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.handshake.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.handshake.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.handshake.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.handshake.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "accept": {".class": "SymbolTableNode", "cross_ref": "websockets.utils.accept_key", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef", "module_public": false}, "binascii": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "build_request": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.legacy.handshake.build_request", "name": "build_request", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["headers"], "arg_types": ["websockets.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_request", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["headers", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.legacy.handshake.build_response", "name": "build_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["headers", "key"], "arg_types": ["websockets.datastructures.Headers", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_response", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_request": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.legacy.handshake.check_request", "name": "check_request", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["headers"], "arg_types": ["websockets.datastructures.Headers"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_request", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["headers", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.legacy.handshake.check_response", "name": "check_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["headers", "key"], "arg_types": ["websockets.datastructures.Headers", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_response", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_key": {".class": "SymbolTableNode", "cross_ref": "websockets.utils.generate_key", "kind": "Gdef", "module_public": false}, "parse_connection": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.parse_connection", "kind": "Gdef", "module_public": false}, "parse_upgrade": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.parse_upgrade", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/websockets/legacy/handshake.py"}