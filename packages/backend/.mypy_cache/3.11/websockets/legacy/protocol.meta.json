{"data_mtime": 1751259991, "dep_lines": [18, 43, 15, 19, 20, 28, 29, 41, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["websockets.asyncio.compatibility", "websockets.legacy.framing", "collections.abc", "websockets.datastructures", "websockets.exceptions", "websockets.extensions", "websockets.frames", "websockets.protocol", "websockets.typing", "__future__", "asyncio", "codecs", "collections", "logging", "random", "ssl", "struct", "sys", "time", "traceback", "uuid", "warnings", "typing", "builtins", "_collections_abc", "_typeshed", "_warnings", "abc", "asyncio.events", "asyncio.exceptions", "asyncio.futures", "asyncio.locks", "asyncio.mixins", "asyncio.protocols", "asyncio.streams", "asyncio.tasks", "asyncio.transports", "<PERSON><PERSON><PERSON>", "enum", "importlib", "importlib.machinery", "types", "typing_extensions", "websockets.extensions.base"], "hash": "c4531d5c59fad410c86254a7736e244890a0256a", "id": "websockets.legacy.protocol", "ignore_all": true, "interface_hash": "f1d8dfafb1ff3f543ce60264b962d0e5b0590a4d", "mtime": 1751256088, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/websockets/legacy/protocol.py", "plugin_data": null, "size": 63902, "suppressed": [], "version_id": "1.13.0"}