{".class": "MypyFile", "_fullname": "websockets.exceptions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ConcurrencyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.WebSocketException", "builtins.RuntimeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.ConcurrencyError", "name": "ConcurrencyError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.ConcurrencyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.ConcurrencyError", "websockets.exceptions.WebSocketException", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.ConcurrencyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.ConcurrencyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionClosed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.WebSocketException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.ConnectionClosed", "name": "ConnectionClosed", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.ConnectionClosed", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.ConnectionClosed", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rcvd", "sent", "rcvd_then_sent"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.ConnectionClosed.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rcvd", "sent", "rcvd_then_sent"], "arg_types": ["websockets.exceptions.ConnectionClosed", {".class": "UnionType", "items": ["websockets.frames.Close", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["websockets.frames.Close", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConnectionClosed", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.ConnectionClosed.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["websockets.exceptions.ConnectionClosed"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of ConnectionClosed", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "websockets.exceptions.ConnectionClosed.code", "name": "code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.exceptions.ConnectionClosed"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "code of ConnectionClosed", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "websockets.exceptions.ConnectionClosed.code", "name": "code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.exceptions.ConnectionClosed"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "code of ConnectionClosed", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "rcvd": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.exceptions.ConnectionClosed.rcvd", "name": "rcvd", "type": {".class": "UnionType", "items": ["websockets.frames.Close", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "rcvd_then_sent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.exceptions.ConnectionClosed.rcvd_then_sent", "name": "rcvd_then_sent", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "reason": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "websockets.exceptions.ConnectionClosed.reason", "name": "reason", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.exceptions.ConnectionClosed"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reason of ConnectionClosed", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "websockets.exceptions.ConnectionClosed.reason", "name": "reason", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.exceptions.ConnectionClosed"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reason of ConnectionClosed", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.exceptions.ConnectionClosed.sent", "name": "sent", "type": {".class": "UnionType", "items": ["websockets.frames.Close", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.ConnectionClosed.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.ConnectionClosed", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionClosedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.ConnectionClosed"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.ConnectionClosedError", "name": "ConnectionClosedError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.ConnectionClosedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.ConnectionClosedError", "websockets.exceptions.ConnectionClosed", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.ConnectionClosedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.ConnectionClosedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionClosedOK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.ConnectionClosed"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.ConnectionClosedOK", "name": "ConnectionClosedOK", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.ConnectionClosedOK", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.ConnectionClosedOK", "websockets.exceptions.ConnectionClosed", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.ConnectionClosedOK.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.ConnectionClosedOK", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DuplicateParameter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.NegotiationError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.DuplicateParameter", "name": "DuplicateParameter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.DuplicateParameter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.DuplicateParameter", "websockets.exceptions.NegotiationError", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.DuplicateParameter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["websockets.exceptions.DuplicateParameter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DuplicateParameter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.DuplicateParameter.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["websockets.exceptions.DuplicateParameter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of DuplicateParameter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.exceptions.DuplicateParameter.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.DuplicateParameter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.DuplicateParameter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidHandshake": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.WebSocketException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.InvalidHandshake", "name": "InvalidHandshake", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.InvalidHandshake", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.InvalidHandshake.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.InvalidHandshake", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidHeader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.InvalidHandshake"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.InvalidHeader", "name": "InvalidHeader", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.InvalidHeader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.InvalidHeader", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidHeader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "value"], "arg_types": ["websockets.exceptions.InvalidHeader", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidHeader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidHeader.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["websockets.exceptions.InvalidHeader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of InvalidHeader", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.exceptions.InvalidHeader.name", "name": "name", "type": "builtins.str"}}, "value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.exceptions.InvalidHeader.value", "name": "value", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.InvalidHeader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.InvalidHeader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidHeaderFormat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.InvalidHeader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.InvalidHeaderFormat", "name": "InvalidHeaderFormat", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.InvalidHeaderFormat", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.InvalidHeaderFormat", "websockets.exceptions.InvalidHeader", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "name", "error", "header", "pos"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidHeaderFormat.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "name", "error", "header", "pos"], "arg_types": ["websockets.exceptions.InvalidHeaderFormat", "builtins.str", "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidHeaderFormat", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.InvalidHeaderFormat.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.InvalidHeaderFormat", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidHeaderValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.InvalidHeader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.InvalidHeaderValue", "name": "InvalidHeaderValue", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.InvalidHeaderValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.InvalidHeaderValue", "websockets.exceptions.InvalidHeader", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.InvalidHeaderValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.InvalidHeaderValue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidMessage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.InvalidHandshake"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.InvalidMessage", "name": "InvalidMessage", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.InvalidMessage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.InvalidMessage", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.InvalidMessage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.InvalidMessage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidOrigin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.InvalidHeader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.InvalidOrigin", "name": "InvalidOrigin", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.InvalidOrigin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.InvalidOrigin", "websockets.exceptions.InvalidHeader", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidOrigin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "arg_types": ["websockets.exceptions.InvalidOrigin", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidOrigin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.InvalidOrigin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.InvalidOrigin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidParameterName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.NegotiationError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.InvalidParameterName", "name": "InvalidParameterName", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.InvalidParameterName", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.InvalidParameterName", "websockets.exceptions.NegotiationError", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidParameterName.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["websockets.exceptions.InvalidParameterName", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidParameterName", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidParameterName.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["websockets.exceptions.InvalidParameterName"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of InvalidParameterName", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.exceptions.InvalidParameterName.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.InvalidParameterName.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.InvalidParameterName", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidParameterValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.NegotiationError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.InvalidParameterValue", "name": "InvalidParameterValue", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.InvalidParameterValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.InvalidParameterValue", "websockets.exceptions.NegotiationError", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidParameterValue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": ["websockets.exceptions.InvalidParameterValue", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidParameterValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidParameterValue.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["websockets.exceptions.InvalidParameterValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of InvalidParameterValue", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.exceptions.InvalidParameterValue.name", "name": "name", "type": "builtins.str"}}, "value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.exceptions.InvalidParameterValue.value", "name": "value", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.InvalidParameterValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.InvalidParameterValue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.WebSocketException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.InvalidProxy", "name": "InvalidProxy", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.InvalidProxy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.InvalidProxy", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "proxy", "msg"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidProxy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "proxy", "msg"], "arg_types": ["websockets.exceptions.InvalidProxy", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidProxy.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["websockets.exceptions.InvalidProxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of InvalidProxy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "msg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.exceptions.InvalidProxy.msg", "name": "msg", "type": "builtins.str"}}, "proxy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.exceptions.InvalidProxy.proxy", "name": "proxy", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.InvalidProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.InvalidProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidProxyMessage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.ProxyError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.InvalidProxyMessage", "name": "InvalidProxyMessage", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.InvalidProxyMessage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.InvalidProxyMessage", "websockets.exceptions.ProxyError", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.InvalidProxyMessage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.InvalidProxyMessage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidProxyStatus": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.ProxyError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.InvalidProxyStatus", "name": "InvalidProxyStatus", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.InvalidProxyStatus", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.InvalidProxyStatus", "websockets.exceptions.ProxyError", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidProxyStatus.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["websockets.exceptions.InvalidProxyStatus", "websockets.http11.Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidProxyStatus", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidProxyStatus.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["websockets.exceptions.InvalidProxyStatus"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of InvalidProxyStatus", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.exceptions.InvalidProxyStatus.response", "name": "response", "type": "websockets.http11.Response"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.InvalidProxyStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.InvalidProxyStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.WebSocketException", "builtins.AssertionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.InvalidState", "name": "InvalidState", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.InvalidState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.InvalidState", "websockets.exceptions.WebSocketException", "builtins.AssertionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.InvalidState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.InvalidState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidStatus": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.InvalidHandshake"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.InvalidStatus", "name": "InvalidStatus", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.InvalidStatus", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.InvalidStatus", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidStatus.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["websockets.exceptions.InvalidStatus", "websockets.http11.Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidStatus", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidStatus.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["websockets.exceptions.InvalidStatus"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of InvalidStatus", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.exceptions.InvalidStatus.response", "name": "response", "type": "websockets.http11.Response"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.InvalidStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.InvalidStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidURI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.WebSocketException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.InvalidURI", "name": "InvalidURI", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.InvalidURI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.InvalidURI", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uri", "msg"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidURI.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "uri", "msg"], "arg_types": ["websockets.exceptions.InvalidURI", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidURI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.InvalidURI.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["websockets.exceptions.InvalidURI"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of InvalidURI", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "msg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.exceptions.InvalidURI.msg", "name": "msg", "type": "builtins.str"}}, "uri": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.exceptions.InvalidURI.uri", "name": "uri", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.InvalidURI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.InvalidURI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidUpgrade": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.InvalidHeader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.InvalidUpgrade", "name": "InvalidUpgrade", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.InvalidUpgrade", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.InvalidUpgrade", "websockets.exceptions.InvalidHeader", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.InvalidUpgrade.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.InvalidUpgrade", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NegotiationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.InvalidHandshake"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.NegotiationError", "name": "NegotiationError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.NegotiationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.NegotiationError", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.NegotiationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.NegotiationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PayloadTooBig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.WebSocketException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.PayloadTooBig", "name": "PayloadTooBig", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.PayloadTooBig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.PayloadTooBig", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "size_or_message", "max_size", "cur_size"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.PayloadTooBig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "size_or_message", "max_size", "cur_size"], "arg_types": ["websockets.exceptions.PayloadTooBig", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PayloadTooBig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.PayloadTooBig.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["websockets.exceptions.PayloadTooBig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of PayloadTooBig", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cur_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "websockets.exceptions.PayloadTooBig.cur_size", "name": "cur_size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "max_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "websockets.exceptions.PayloadTooBig.max_size", "name": "max_size", "type": "builtins.int"}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "websockets.exceptions.PayloadTooBig.message", "name": "message", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "set_current_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cur_size"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.exceptions.PayloadTooBig.set_current_size", "name": "set_current_size", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cur_size"], "arg_types": ["websockets.exceptions.PayloadTooBig", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_current_size of PayloadTooBig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "websockets.exceptions.PayloadTooBig.size", "name": "size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.PayloadTooBig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.PayloadTooBig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProtocolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.WebSocketException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.ProtocolError", "name": "ProtocolError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.ProtocolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.ProtocolError", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.ProtocolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.ProtocolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProxyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.InvalidHandshake"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.ProxyError", "name": "ProxyError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.ProxyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.ProxyError", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.ProxyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.ProxyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SecurityError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.exceptions.InvalidHandshake"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.SecurityError", "name": "SecurityError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.SecurityError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.SecurityError", "websockets.exceptions.InvalidHandshake", "websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.SecurityError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.SecurityError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WebSocketException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.exceptions.WebSocketException", "name": "WebSocketException", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.exceptions.WebSocketException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.exceptions", "mro": ["websockets.exceptions.WebSocketException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.exceptions.WebSocketException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.exceptions.WebSocketException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.exceptions.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.exceptions.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "frames": {".class": "SymbolTableNode", "cross_ref": "websockets.frames", "kind": "Gdef", "module_public": false}, "http11": {".class": "SymbolTableNode", "cross_ref": "websockets.http11", "kind": "Gdef", "module_public": false}, "lazy_import": {".class": "SymbolTableNode", "cross_ref": "websockets.imports.lazy_import", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/websockets/exceptions.py"}