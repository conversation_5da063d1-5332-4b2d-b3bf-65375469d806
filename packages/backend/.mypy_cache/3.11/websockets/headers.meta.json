{"data_mtime": 1751259990, "dep_lines": [7, 10, 11, 1, 3, 4, 5, 6, 8, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "websockets.exceptions", "websockets.typing", "__future__", "base64", "<PERSON><PERSON><PERSON><PERSON>", "ipaddress", "re", "typing", "builtins", "abc", "importlib", "importlib.machinery"], "hash": "a4a3be786f30799cee47d99eb2a633c7ad4514d2", "id": "websockets.headers", "ignore_all": true, "interface_hash": "3b8a39cbf34ecfa0d159017135d8db5924d28214", "mtime": 1751256088, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/websockets/headers.py", "plugin_data": null, "size": 16046, "suppressed": [], "version_id": "1.13.0"}