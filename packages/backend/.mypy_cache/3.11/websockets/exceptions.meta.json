{"data_mtime": 1751259990, "dep_lines": [40, 461, 461, 36, 38, 461, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["websockets.imports", "websockets.frames", "websockets.http11", "__future__", "warnings", "websockets", "builtins", "_warnings", "abc", "importlib", "importlib.machinery", "typing", "typing_extensions"], "hash": "f2866de806641d7b3474591cd2563701d6530829", "id": "websockets.exceptions", "ignore_all": true, "interface_hash": "d5494cddf2957266ef7e5a863c25a603df30c175", "mtime": 1751256088, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/websockets/exceptions.py", "plugin_data": null, "size": 12811, "suppressed": [], "version_id": "1.13.0"}