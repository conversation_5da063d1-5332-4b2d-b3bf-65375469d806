{"data_mtime": 1751259990, "dep_lines": [26, 33, 12, 16, 22, 23, 24, 25, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["websockets.asyncio.compatibility", "websockets.asyncio.messages", "collections.abc", "websockets.exceptions", "websockets.frames", "websockets.http11", "websockets.protocol", "websockets.typing", "__future__", "asyncio", "collections", "contextlib", "logging", "random", "struct", "sys", "traceback", "uuid", "types", "typing", "builtins", "_collections_abc", "_typeshed", "abc", "asyncio.events", "asyncio.futures", "asyncio.protocols", "asyncio.tasks", "asyncio.timeouts", "asyncio.transports", "<PERSON><PERSON><PERSON>", "enum", "importlib", "importlib.machinery", "typing_extensions"], "hash": "7c8a13fc91155b40f1b25b5d6d9e2de891d45188", "id": "websockets.asyncio.connection", "ignore_all": true, "interface_hash": "b5947e7a8e2c5daa264c6f48a5594fe8d45c00eb", "mtime": 1751256088, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/websockets/asyncio/connection.py", "plugin_data": null, "size": 48722, "suppressed": [], "version_id": "1.13.0"}