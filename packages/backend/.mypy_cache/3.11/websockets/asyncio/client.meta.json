{"data_mtime": 1751259990, "dep_lines": [24, 25, 32, 33, 9, 10, 14, 15, 16, 26, 27, 28, 29, 30, 31, 1, 3, 4, 5, 6, 7, 8, 9, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 672, 671], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 20, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["websockets.extensions.base", "websockets.extensions.permessage_deflate", "websockets.asyncio.compatibility", "websockets.asyncio.connection", "urllib.parse", "collections.abc", "websockets.client", "websockets.datastructures", "websockets.exceptions", "websockets.headers", "websockets.http11", "websockets.protocol", "websockets.streams", "websockets.typing", "websockets.uri", "__future__", "asyncio", "logging", "os", "socket", "ssl", "traceback", "urllib", "types", "typing", "builtins", "_socket", "_typeshed", "abc", "anyio", "anyio._core", "anyio._core._eventloop", "asyncio.events", "asyncio.exceptions", "asyncio.futures", "asyncio.protocols", "asyncio.tasks", "asyncio.timeouts", "asyncio.transports", "contextlib", "enum", "importlib", "importlib.machinery", "typing_extensions", "websockets.extensions", "websockets.frames"], "hash": "74a44796128062cfa15916916ea66df5c6c0bbb4", "id": "websockets.asyncio.client", "ignore_all": true, "interface_hash": "16d53bc7196ca7deef191ca457a4703750e0fab7", "mtime": 1751256088, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/websockets/asyncio/client.py", "plugin_data": null, "size": 31490, "suppressed": ["python_socks.async_.asyncio", "python_socks"], "version_id": "1.13.0"}