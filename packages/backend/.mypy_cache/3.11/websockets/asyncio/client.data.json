{".class": "MypyFile", "_fullname": "websockets.asyncio.client", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef", "module_public": false}, "CONNECTING": {".class": "SymbolTableNode", "cross_ref": "websockets.protocol.CONNECTING", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ClientConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["websockets.asyncio.connection.Connection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.asyncio.client.ClientConnection", "name": "ClientConnection", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.asyncio.client.ClientConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.asyncio.client", "mro": ["websockets.asyncio.client.ClientConnection", "websockets.asyncio.connection.Connection", "asyncio.protocols.Protocol", "asyncio.protocols.BaseProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "protocol", "ping_interval", "ping_timeout", "close_timeout", "max_queue", "write_limit"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.asyncio.client.ClientConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "protocol", "ping_interval", "ping_timeout", "close_timeout", "max_queue", "write_limit"], "arg_types": ["websockets.asyncio.client.ClientConnection", "websockets.client.ClientProtocol", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClientConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handshake": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "additional_headers", "user_agent_header"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "websockets.asyncio.client.ClientConnection.handshake", "name": "handshake", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "additional_headers", "user_agent_header"], "arg_types": ["websockets.asyncio.client.ClientConnection", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.datastructures.HeadersLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handshake of ClientConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.asyncio.client.ClientConnection.process_event", "name": "process_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["websockets.asyncio.client.ClientConnection", {".class": "TypeAliasType", "args": [], "type_ref": "websockets.protocol.Event"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_event of ClientConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "protocol": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "websockets.asyncio.client.ClientConnection.protocol", "name": "protocol", "type": "websockets.client.ClientProtocol"}}, "response_rcvd": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "websockets.asyncio.client.ClientConnection.response_rcvd", "name": "response_rcvd", "type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "asyncio.futures.Future"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.asyncio.client.ClientConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.asyncio.client.ClientConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClientExtensionFactory": {".class": "SymbolTableNode", "cross_ref": "websockets.extensions.base.ClientExtensionFactory", "kind": "Gdef", "module_public": false}, "ClientProtocol": {".class": "SymbolTableNode", "cross_ref": "websockets.client.ClientProtocol", "kind": "Gdef", "module_public": false}, "Connection": {".class": "SymbolTableNode", "cross_ref": "websockets.asyncio.connection.Connection", "kind": "Gdef", "module_public": false}, "Event": {".class": "SymbolTableNode", "cross_ref": "websockets.protocol.Event", "kind": "Gdef", "module_public": false}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_public": false}, "HTTPProxyConnection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.protocols.Protocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.asyncio.client.HTTPProxyConnection", "name": "HTTPProxyConnection", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.asyncio.client.HTTPProxyConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.asyncio.client", "mro": ["websockets.asyncio.client.HTTPProxyConnection", "asyncio.protocols.Protocol", "asyncio.protocols.BaseProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "ws_uri", "proxy", "user_agent_header"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.asyncio.client.HTTPProxyConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "ws_uri", "proxy", "user_agent_header"], "arg_types": ["websockets.asyncio.client.HTTPProxyConnection", "websockets.uri.WebSocketURI", "websockets.uri.Proxy", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPProxyConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connection_lost": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.asyncio.client.HTTPProxyConnection.connection_lost", "name": "connection_lost", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "arg_types": ["websockets.asyncio.client.HTTPProxyConnection", {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_lost of HTTPProxyConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connection_made": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.asyncio.client.HTTPProxyConnection.connection_made", "name": "connection_made", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "arg_types": ["websockets.asyncio.client.HTTPProxyConnection", "asyncio.transports.BaseTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_made of HTTPProxyConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "data_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.asyncio.client.HTTPProxyConnection.data_received", "name": "data_received", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["websockets.asyncio.client.HTTPProxyConnection", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data_received of HTTPProxyConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "eof_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.asyncio.client.HTTPProxyConnection.eof_received", "name": "eof_received", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.asyncio.client.HTTPProxyConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eof_received of HTTPProxyConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.HTTPProxyConnection.parser", "name": "parser", "type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, "websockets.http11.Response"], "extra_attrs": null, "type_ref": "typing.Generator"}}}, "proxy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.HTTPProxyConnection.proxy", "name": "proxy", "type": "websockets.uri.Proxy"}}, "reader": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.HTTPProxyConnection.reader", "name": "reader", "type": "websockets.streams.StreamReader"}}, "response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "websockets.asyncio.client.HTTPProxyConnection.response", "name": "response", "type": {".class": "Instance", "args": ["websockets.http11.Response"], "extra_attrs": null, "type_ref": "asyncio.futures.Future"}}}, "run_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.asyncio.client.HTTPProxyConnection.run_parser", "name": "run_parser", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.asyncio.client.HTTPProxyConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_parser of HTTPProxyConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transport": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.HTTPProxyConnection.transport", "name": "transport", "type": "asyncio.transports.Transport"}}, "user_agent_header": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.HTTPProxyConnection.user_agent_header", "name": "user_agent_header", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "ws_uri": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.HTTPProxyConnection.ws_uri", "name": "ws_uri", "type": "websockets.uri.WebSocketURI"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.asyncio.client.HTTPProxyConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.asyncio.client.HTTPProxyConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Headers": {".class": "SymbolTableNode", "cross_ref": "websockets.datastructures.Headers", "kind": "Gdef", "module_public": false}, "HeadersLike": {".class": "SymbolTableNode", "cross_ref": "websockets.datastructures.HeadersLike", "kind": "Gdef", "module_public": false}, "InvalidMessage": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidMessage", "kind": "Gdef", "module_public": false}, "InvalidProxyMessage": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidProxyMessage", "kind": "Gdef", "module_public": false}, "InvalidProxyStatus": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidProxyStatus", "kind": "Gdef", "module_public": false}, "InvalidStatus": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidStatus", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "LoggerLike": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.LoggerLike", "kind": "Gdef", "module_public": false}, "MAX_REDIRECTS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.asyncio.client.MAX_REDIRECTS", "name": "MAX_REDIRECTS", "type": "builtins.int"}}, "Origin": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.Origin", "kind": "Gdef", "module_public": false}, "Proxy": {".class": "SymbolTableNode", "cross_ref": "websockets.uri.Proxy", "kind": "Gdef", "module_public": false}, "ProxyError": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.ProxyError", "kind": "Gdef", "module_public": false}, "ProxyType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "websockets.asyncio.client.ProxyType", "name": "ProxyType", "type": {".class": "AnyType", "missing_import_name": "websockets.asyncio.client.ProxyType", "source_any": null, "type_of_any": 3}}}, "Response": {".class": "SymbolTableNode", "cross_ref": "websockets.http11.Response", "kind": "Gdef", "module_public": false}, "SOCKS_PROXY_RDNS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.asyncio.client.SOCKS_PROXY_RDNS", "name": "SOCKS_PROXY_RDNS", "type": {".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "SOCKS_PROXY_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.asyncio.client.SOCKS_PROXY_TYPES", "name": "SOCKS_PROXY_TYPES", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "websockets.asyncio.client.ProxyType", "source_any": {".class": "AnyType", "missing_import_name": "websockets.asyncio.client.ProxyType", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "SecurityError": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.SecurityError", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "SocksProxy": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "websockets.asyncio.client.SocksProxy", "name": "SocksProxy", "type": {".class": "AnyType", "missing_import_name": "websockets.asyncio.client.SocksProxy", "source_any": null, "type_of_any": 3}}}, "StreamReader": {".class": "SymbolTableNode", "cross_ref": "websockets.streams.StreamReader", "kind": "Gdef", "module_public": false}, "Subprotocol": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.Subprotocol", "kind": "Gdef", "module_public": false}, "TimeoutError": {".class": "SymbolTableNode", "cross_ref": "builtins.TimeoutError", "kind": "Gdef", "module_public": false}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_public": false}, "USER_AGENT": {".class": "SymbolTableNode", "cross_ref": "websockets.http11.USER_AGENT", "kind": "Gdef", "module_public": false}, "WebSocketURI": {".class": "SymbolTableNode", "cross_ref": "websockets.uri.WebSocketURI", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.asyncio.client.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.asyncio.client.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.asyncio.client.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.asyncio.client.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.asyncio.client.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.asyncio.client.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.asyncio.client.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_public": false}, "asyncio_timeout": {".class": "SymbolTableNode", "cross_ref": "asyncio.timeouts.timeout", "kind": "Gdef", "module_public": false}, "backoff": {".class": "SymbolTableNode", "cross_ref": "websockets.client.backoff", "kind": "Gdef", "module_public": false}, "build_authorization_basic": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.build_authorization_basic", "kind": "Gdef", "module_public": false}, "build_host": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.build_host", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "connect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.asyncio.client.connect", "name": "connect", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "websockets.asyncio.client.connect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "websockets.asyncio.client", "mro": ["websockets.asyncio.client.connect", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "websockets.asyncio.client.connect.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.asyncio.client.connect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aenter__ of connect", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "websockets.asyncio.client.ClientConnection"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "websockets.asyncio.client.connect.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "arg_types": ["websockets.asyncio.client.connect", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aexit__ of connect", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_generator", "is_coroutine", "is_async_generator"], "fullname": "websockets.asyncio.client.connect.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.asyncio.client.connect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aiter__ of connect", "ret_type": {".class": "Instance", "args": ["websockets.asyncio.client.ClientConnection"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__await__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.asyncio.client.connect.__await__", "name": "__await__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.asyncio.client.connect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__await__ of connect", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}, "websockets.asyncio.client.ClientConnection"], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__await_impl__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "websockets.asyncio.client.connect.__await_impl__", "name": "__await_impl__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.asyncio.client.connect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__await_impl__ of connect", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "websockets.asyncio.client.ClientConnection"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "uri", "origin", "extensions", "subprotocols", "compression", "additional_headers", "user_agent_header", "proxy", "process_exception", "open_timeout", "ping_interval", "ping_timeout", "close_timeout", "max_size", "max_queue", "write_limit", "logger", "create_connection", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.asyncio.client.connect.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "uri", "origin", "extensions", "subprotocols", "compression", "additional_headers", "user_agent_header", "proxy", "process_exception", "open_timeout", "ping_interval", "ping_timeout", "close_timeout", "max_size", "max_queue", "write_limit", "logger", "create_connection", "kwargs"], "arg_types": ["websockets.asyncio.client.connect", "builtins.str", {".class": "UnionType", "items": ["websockets.typing.Origin", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.extensions.base.ClientExtensionFactory"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.typing.Subprotocol"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.datastructures.HeadersLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.Exception"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.LoggerLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "websockets.asyncio.client.ClientConnection"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of connect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "websockets.asyncio.client.connect.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.asyncio.client.connect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}, "websockets.asyncio.client.ClientConnection"], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "additional_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.connect.additional_headers", "name": "additional_headers", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.datastructures.HeadersLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "connection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.connect.connection", "name": "connection", "type": "websockets.asyncio.client.ClientConnection"}}, "connection_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.connect.connection_kwargs", "name": "connection_kwargs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "create_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "websockets.asyncio.client.connect.create_connection", "name": "create_connection", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["websockets.asyncio.client.connect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_connection of connect", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "websockets.asyncio.client.ClientConnection"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.connect.logger", "name": "logger", "type": {".class": "UnionType", "items": ["logging.Logger", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "logging.LoggerAdapter"}], "uses_pep604_syntax": false}}}, "open_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.connect.open_timeout", "name": "open_timeout", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "process_exception": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.connect.process_exception", "name": "process_exception", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.Exception"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_redirect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.asyncio.client.connect.process_redirect", "name": "process_redirect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "arg_types": ["websockets.asyncio.client.connect", "builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_redirect of connect", "ret_type": {".class": "UnionType", "items": ["builtins.Exception", "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "protocol_factory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.connect.protocol_factory", "name": "protocol_factory", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["uri"], "arg_types": ["websockets.uri.WebSocketURI"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "websockets.asyncio.client.ClientConnection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "proxy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.connect.proxy", "name": "proxy", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "uri": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.connect.uri", "name": "uri", "type": "builtins.str"}}, "user_agent_header": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "websockets.asyncio.client.connect.user_agent_header", "name": "user_agent_header", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.asyncio.client.connect.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "websockets.asyncio.client.connect", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "connect_http_proxy": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["proxy", "ws_uri", "user_agent_header", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "websockets.asyncio.client.connect_http_proxy", "name": "connect_http_proxy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["proxy", "ws_uri", "user_agent_header", "kwargs"], "arg_types": ["websockets.uri.Proxy", "websockets.uri.WebSocketURI", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_http_proxy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "asyncio.transports.Transport"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect_socks_proxy": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["proxy", "ws_uri", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "websockets.asyncio.client.connect_socks_proxy", "name": "connect_socks_proxy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["proxy", "ws_uri", "kwargs"], "arg_types": ["websockets.uri.Proxy", "websockets.uri.WebSocketURI", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_socks_proxy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "socket.socket"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enable_client_permessage_deflate": {".class": "SymbolTableNode", "cross_ref": "websockets.extensions.permessage_deflate.enable_client_permessage_deflate", "kind": "Gdef", "module_public": false}, "get_proxy": {".class": "SymbolTableNode", "cross_ref": "websockets.uri.get_proxy", "kind": "Gdef", "module_public": false}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "parse_proxy": {".class": "SymbolTableNode", "cross_ref": "websockets.uri.parse_proxy", "kind": "Gdef", "module_public": false}, "parse_uri": {".class": "SymbolTableNode", "cross_ref": "websockets.uri.parse_uri", "kind": "Gdef", "module_public": false}, "prepare_connect_request": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["proxy", "ws_uri", "user_agent_header"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.asyncio.client.prepare_connect_request", "name": "prepare_connect_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["proxy", "ws_uri", "user_agent_header"], "arg_types": ["websockets.uri.Proxy", "websockets.uri.WebSocketURI", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_connect_request", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_exception": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["exc"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.asyncio.client.process_exception", "name": "process_exception", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["exc"], "arg_types": ["builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_exception", "ret_type": {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef", "module_public": false}, "ssl_module": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef", "module_public": false}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef", "module_public": false}, "unix_connect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 4], "arg_names": ["path", "uri", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "websockets.asyncio.client.unix_connect", "name": "unix_connect", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["path", "uri", "kwargs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unix_connect", "ret_type": "websockets.asyncio.client.connect", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "urllib": {".class": "SymbolTableNode", "cross_ref": "urllib", "kind": "Gdef", "module_public": false}, "validate_subprotocols": {".class": "SymbolTableNode", "cross_ref": "websockets.headers.validate_subprotocols", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/websockets/asyncio/client.py"}