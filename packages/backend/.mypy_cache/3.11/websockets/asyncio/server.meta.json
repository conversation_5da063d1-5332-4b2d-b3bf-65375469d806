{"data_mtime": 1751259990, "dep_lines": [15, 16, 27, 28, 10, 14, 17, 18, 23, 24, 25, 26, 1, 3, 4, 5, 6, 7, 8, 9, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["websockets.extensions.base", "websockets.extensions.permessage_deflate", "websockets.asyncio.compatibility", "websockets.asyncio.connection", "collections.abc", "websockets.exceptions", "websockets.frames", "websockets.headers", "websockets.http11", "websockets.protocol", "websockets.server", "websockets.typing", "__future__", "asyncio", "hmac", "http", "logging", "re", "socket", "sys", "types", "typing", "builtins", "_socket", "_typeshed", "abc", "asyncio.base_events", "asyncio.events", "asyncio.futures", "asyncio.protocols", "asyncio.tasks", "asyncio.transports", "contextlib", "<PERSON><PERSON><PERSON>", "enum", "importlib", "importlib.machinery", "ssl", "typing_extensions", "websockets.datastructures", "websockets.extensions"], "hash": "539de688810ce034fadae563f593196661f22783", "id": "websockets.asyncio.server", "ignore_all": true, "interface_hash": "409730b9cc028346b7f2f6bf4764d92a3ee31252", "mtime": 1751256088, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/websockets/asyncio/server.py", "plugin_data": null, "size": 37385, "suppressed": [], "version_id": "1.13.0"}