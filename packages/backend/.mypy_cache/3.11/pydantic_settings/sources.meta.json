{"data_mtime": 1751259987, "dep_lines": [41, 42, 43, 58, 44, 45, 49, 60, 1, 3, 4, 5, 6, 7, 8, 9, 10, 13, 15, 16, 17, 18, 19, 20, 38, 39, 40, 46, 53, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 56, 57], "dep_prios": [5, 5, 5, 25, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25, 25], "dependencies": ["pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic._internal._dataclasses", "pydantic.dataclasses", "pydantic.fields", "pydantic_settings.utils", "pydantic_settings.main", "__future__", "json", "os", "re", "shlex", "sys", "typing", "warnings", "abc", "<PERSON><PERSON><PERSON><PERSON>", "collections", "dataclasses", "enum", "pathlib", "textwrap", "types", "typing_extensions", "dotenv", "pydantic", "pydantic_core", "<PERSON><PERSON><PERSON><PERSON>", "builtins", "_collections_abc", "_typeshed", "_warnings", "importlib", "importlib.machinery", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic.aliases", "pydantic.main", "pydantic_core._pydantic_core"], "hash": "592d89a55d74c9c2b89dd7d856cdd44db868d6a0", "id": "pydantic_settings.sources", "ignore_all": true, "interface_hash": "a1d0e570ea576ea81e0345566ee731a14e5a739b", "mtime": 1751256099, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic_settings/sources.py", "plugin_data": null, "size": 98781, "suppressed": ["to<PERSON>li", "yaml"], "version_id": "1.13.0"}