{"data_mtime": 1751259987, "dep_lines": [8, 9, 10, 11, 12, 14, 1, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._signature", "pydantic._internal._utils", "pydantic.dataclasses", "pydantic.main", "pydantic_settings.sources", "__future__", "<PERSON><PERSON><PERSON><PERSON>", "types", "typing", "pydantic", "builtins", "_collections_abc", "_typeshed", "abc", "importlib", "importlib.machinery", "os", "pathlib", "pydantic._internal", "pydantic._internal._dataclasses", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "re"], "hash": "8a8c48ad5ddc1eca5f3563175b7b7a5ff2d8d34f", "id": "pydantic_settings.main", "ignore_all": true, "interface_hash": "e801a4bb5f26710ad05909e47c873a015708979c", "mtime": 1751256099, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic_settings/main.py", "plugin_data": null, "size": 25756, "suppressed": [], "version_id": "1.13.0"}