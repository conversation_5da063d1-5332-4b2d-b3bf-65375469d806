{".class": "MypyFile", "_fullname": "httpx._exceptions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CloseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.NetworkError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.CloseError", "name": "CloseError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.CloseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.CloseError", "httpx._exceptions.NetworkError", "httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.CloseError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.CloseError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.NetworkError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.ConnectError", "name": "ConnectError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.ConnectError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.ConnectError", "httpx._exceptions.NetworkError", "httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.ConnectError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.ConnectError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.TimeoutException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.ConnectTimeout", "name": "ConnectTimeout", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.ConnectTimeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.ConnectTimeout", "httpx._exceptions.TimeoutException", "httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.ConnectTimeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.ConnectTimeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CookieConflict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.CookieConflict", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.CookieConflict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.CookieConflict", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._exceptions.CookieConflict.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["httpx._exceptions.CookieConflict", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CookieConflict", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.CookieConflict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.CookieConflict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DecodingError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.RequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.DecodingError", "name": "DecodingError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.DecodingError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.DecodingError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.DecodingError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.DecodingError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.HTTPError", "name": "HTTPError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.HTTPError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._exceptions.HTTPError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["httpx._exceptions.HTTPError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "httpx._exceptions.HTTPError._request", "name": "_request", "type": {".class": "UnionType", "items": ["httpx._models.Request", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": ["is_property"], "fullname": "httpx._exceptions.HTTPError.request", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "httpx._exceptions.HTTPError.request", "name": "request", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._exceptions.HTTPError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request of HTTPError", "ret_type": "httpx._models.Request", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "httpx._exceptions.HTTPError.request", "name": "request", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._exceptions.HTTPError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request of HTTPError", "ret_type": "httpx._models.Request", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "httpx._exceptions.HTTPError.request", "name": "request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["httpx._exceptions.HTTPError", "httpx._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request of HTTPError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "request", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._exceptions.HTTPError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request of HTTPError", "ret_type": "httpx._models.Request", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.HTTPError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.HTTPError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPStatusError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.HTTPStatusError", "name": "HTTPStatusError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.HTTPStatusError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.HTTPStatusError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "message", "request", "response"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._exceptions.HTTPStatusError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "message", "request", "response"], "arg_types": ["httpx._exceptions.HTTPStatusError", "builtins.str", "httpx._models.Request", "httpx._models.Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPStatusError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._exceptions.HTTPStatusError.response", "name": "response", "type": "httpx._models.Response"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.HTTPStatusError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.HTTPStatusError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidURL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.InvalidURL", "name": "InvalidURL", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.InvalidURL", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.InvalidURL", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._exceptions.InvalidURL.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["httpx._exceptions.InvalidURL", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidURL", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.InvalidURL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.InvalidURL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LocalProtocolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.ProtocolError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.LocalProtocolError", "name": "LocalProtocolError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.LocalProtocolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.LocalProtocolError", "httpx._exceptions.ProtocolError", "httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.LocalProtocolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.LocalProtocolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NetworkError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.TransportError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.NetworkError", "name": "NetworkError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.NetworkError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.NetworkError", "httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.NetworkError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.NetworkError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PoolTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.TimeoutException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.PoolTimeout", "name": "PoolTimeout", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.PoolTimeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.PoolTimeout", "httpx._exceptions.TimeoutException", "httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.PoolTimeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.PoolTimeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProtocolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.TransportError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.ProtocolError", "name": "ProtocolError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.ProtocolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.ProtocolError", "httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.ProtocolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.ProtocolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProxyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.TransportError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.ProxyError", "name": "ProxyError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.ProxyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.ProxyError", "httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.ProxyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.ProxyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.NetworkError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.ReadError", "name": "ReadError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.ReadError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.ReadError", "httpx._exceptions.NetworkError", "httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.ReadError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.ReadError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.TimeoutException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.ReadTimeout", "name": "ReadTimeout", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.ReadTimeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.ReadTimeout", "httpx._exceptions.TimeoutException", "httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.ReadTimeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.ReadTimeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RemoteProtocolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.ProtocolError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.RemoteProtocolError", "name": "RemoteProtocolError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.RemoteProtocolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.RemoteProtocolError", "httpx._exceptions.ProtocolError", "httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.RemoteProtocolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.RemoteProtocolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Request": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Request", "kind": "Gdef", "module_public": false}, "RequestError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.RequestError", "name": "RequestError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.RequestError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "message", "request"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._exceptions.RequestError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "message", "request"], "arg_types": ["httpx._exceptions.RequestError", "builtins.str", {".class": "UnionType", "items": ["httpx._models.Request", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RequestError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.RequestError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.RequestError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RequestNotRead": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.StreamError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.RequestNotRead", "name": "RequestNotRead", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.RequestNotRead", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.RequestNotRead", "httpx._exceptions.StreamError", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._exceptions.RequestNotRead.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._exceptions.RequestNotRead"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RequestNotRead", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.RequestNotRead.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.RequestNotRead", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Response": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Response", "kind": "Gdef", "module_public": false}, "ResponseNotRead": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.StreamError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.ResponseNotRead", "name": "ResponseNotRead", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.ResponseNotRead", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.ResponseNotRead", "httpx._exceptions.StreamError", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._exceptions.ResponseNotRead.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._exceptions.ResponseNotRead"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResponseNotRead", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.ResponseNotRead.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.ResponseNotRead", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamClosed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.StreamError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.StreamClosed", "name": "StreamClosed", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.StreamClosed", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.StreamClosed", "httpx._exceptions.StreamError", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._exceptions.StreamClosed.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._exceptions.StreamClosed"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StreamClosed", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.StreamClosed.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.StreamClosed", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamConsumed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.StreamError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.StreamConsumed", "name": "StreamConsumed", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.StreamConsumed", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.StreamConsumed", "httpx._exceptions.StreamError", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._exceptions.StreamConsumed.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._exceptions.StreamConsumed"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StreamConsumed", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.StreamConsumed.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.StreamConsumed", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.RuntimeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.StreamError", "name": "StreamError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.StreamError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.StreamError", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._exceptions.StreamError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["httpx._exceptions.StreamError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StreamError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.StreamError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.StreamError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeoutException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.TransportError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.TimeoutException", "name": "TimeoutException", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.TimeoutException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.TimeoutException", "httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.TimeoutException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.TimeoutException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TooManyRedirects": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.RequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.TooManyRedirects", "name": "TooManyRedirects", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.TooManyRedirects", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.TooManyRedirects", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.TooManyRedirects.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.TooManyRedirects", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransportError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.RequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.TransportError", "name": "TransportError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.TransportError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.TransportError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.TransportError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.TransportError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.UnsupportedProtocol", "name": "UnsupportedProtocol", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.UnsupportedProtocol", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.UnsupportedProtocol", "httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.UnsupportedProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.UnsupportedProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WriteError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.NetworkError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.WriteError", "name": "WriteError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.WriteError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.WriteError", "httpx._exceptions.NetworkError", "httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.WriteError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.WriteError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WriteTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._exceptions.TimeoutException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._exceptions.WriteTimeout", "name": "WriteTimeout", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._exceptions.WriteTimeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._exceptions", "mro": ["httpx._exceptions.WriteTimeout", "httpx._exceptions.TimeoutException", "httpx._exceptions.TransportError", "httpx._exceptions.RequestError", "httpx._exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._exceptions.WriteTimeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._exceptions.WriteTimeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpx._exceptions.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._exceptions.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef", "module_public": false}, "request_context": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["request"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "httpx._exceptions.request_context", "name": "request_context", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["request"], "arg_types": [{".class": "UnionType", "items": ["httpx._models.Request", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request_context", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "httpx._exceptions.request_context", "name": "request_context", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["request"], "arg_types": [{".class": "UnionType", "items": ["httpx._models.Request", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request_context", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/httpx/_exceptions.py"}