{".class": "MypyFile", "_fullname": "httpx._api", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AuthTypes": {".class": "SymbolTableNode", "cross_ref": "httpx._types.AuthTypes", "kind": "Gdef", "module_public": false}, "Client": {".class": "SymbolTableNode", "cross_ref": "httpx._client.Client", "kind": "Gdef", "module_public": false}, "CookieTypes": {".class": "SymbolTableNode", "cross_ref": "httpx._types.CookieTypes", "kind": "Gdef", "module_public": false}, "DEFAULT_TIMEOUT_CONFIG": {".class": "SymbolTableNode", "cross_ref": "httpx._config.DEFAULT_TIMEOUT_CONFIG", "kind": "Gdef", "module_public": false}, "HeaderTypes": {".class": "SymbolTableNode", "cross_ref": "httpx._types.HeaderTypes", "kind": "Gdef", "module_public": false}, "ProxyTypes": {".class": "SymbolTableNode", "cross_ref": "httpx._types.ProxyTypes", "kind": "Gdef", "module_public": false}, "QueryParamTypes": {".class": "SymbolTableNode", "cross_ref": "httpx._types.QueryParamTypes", "kind": "Gdef", "module_public": false}, "RequestContent": {".class": "SymbolTableNode", "cross_ref": "httpx._types.RequestContent", "kind": "Gdef", "module_public": false}, "RequestData": {".class": "SymbolTableNode", "cross_ref": "httpx._types.RequestData", "kind": "Gdef", "module_public": false}, "RequestFiles": {".class": "SymbolTableNode", "cross_ref": "httpx._types.RequestFiles", "kind": "Gdef", "module_public": false}, "Response": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Response", "kind": "Gdef", "module_public": false}, "TimeoutTypes": {".class": "SymbolTableNode", "cross_ref": "httpx._types.TimeoutTypes", "kind": "Gdef", "module_public": false}, "URL": {".class": "SymbolTableNode", "cross_ref": "httpx._urls.URL", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpx._api.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._api.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._api.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._api.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._api.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._api.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._api.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef", "module_public": false}, "delete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "headers", "cookies", "auth", "proxy", "follow_redirects", "timeout", "verify", "trust_env"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._api.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "headers", "cookies", "auth", "proxy", "follow_redirects", "timeout", "verify", "trust_env"], "arg_types": [{".class": "UnionType", "items": ["httpx._urls.URL", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.QueryParamTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.HeaderTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.CookieTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.AuthTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.ProxyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.TimeoutTypes"}, {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete", "ret_type": "httpx._models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "headers", "cookies", "auth", "proxy", "follow_redirects", "verify", "timeout", "trust_env"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._api.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "headers", "cookies", "auth", "proxy", "follow_redirects", "verify", "timeout", "trust_env"], "arg_types": [{".class": "UnionType", "items": ["httpx._urls.URL", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.QueryParamTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.HeaderTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.CookieTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.AuthTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.ProxyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.TimeoutTypes"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get", "ret_type": "httpx._models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "head": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "headers", "cookies", "auth", "proxy", "follow_redirects", "verify", "timeout", "trust_env"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._api.head", "name": "head", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "headers", "cookies", "auth", "proxy", "follow_redirects", "verify", "timeout", "trust_env"], "arg_types": [{".class": "UnionType", "items": ["httpx._urls.URL", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.QueryParamTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.HeaderTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.CookieTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.AuthTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.ProxyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.TimeoutTypes"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "head", "ret_type": "httpx._models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "options": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "headers", "cookies", "auth", "proxy", "follow_redirects", "verify", "timeout", "trust_env"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._api.options", "name": "options", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "params", "headers", "cookies", "auth", "proxy", "follow_redirects", "verify", "timeout", "trust_env"], "arg_types": [{".class": "UnionType", "items": ["httpx._urls.URL", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.QueryParamTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.HeaderTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.CookieTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.AuthTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.ProxyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.TimeoutTypes"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "options", "ret_type": "httpx._models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "patch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "content", "data", "files", "json", "params", "headers", "cookies", "auth", "proxy", "follow_redirects", "verify", "timeout", "trust_env"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._api.patch", "name": "patch", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "content", "data", "files", "json", "params", "headers", "cookies", "auth", "proxy", "follow_redirects", "verify", "timeout", "trust_env"], "arg_types": [{".class": "UnionType", "items": ["httpx._urls.URL", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestContent"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestData"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestFiles"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.QueryParamTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.HeaderTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.CookieTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.AuthTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.ProxyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.TimeoutTypes"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "patch", "ret_type": "httpx._models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "content", "data", "files", "json", "params", "headers", "cookies", "auth", "proxy", "follow_redirects", "verify", "timeout", "trust_env"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._api.post", "name": "post", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "content", "data", "files", "json", "params", "headers", "cookies", "auth", "proxy", "follow_redirects", "verify", "timeout", "trust_env"], "arg_types": [{".class": "UnionType", "items": ["httpx._urls.URL", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestContent"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestData"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestFiles"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.QueryParamTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.HeaderTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.CookieTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.AuthTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.ProxyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.TimeoutTypes"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "post", "ret_type": "httpx._models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "put": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "content", "data", "files", "json", "params", "headers", "cookies", "auth", "proxy", "follow_redirects", "verify", "timeout", "trust_env"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._api.put", "name": "put", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["url", "content", "data", "files", "json", "params", "headers", "cookies", "auth", "proxy", "follow_redirects", "verify", "timeout", "trust_env"], "arg_types": [{".class": "UnionType", "items": ["httpx._urls.URL", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestContent"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestData"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestFiles"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.QueryParamTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.HeaderTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.CookieTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.AuthTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.ProxyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.TimeoutTypes"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put", "ret_type": "httpx._models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "request": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["method", "url", "params", "content", "data", "files", "json", "headers", "cookies", "auth", "proxy", "timeout", "follow_redirects", "verify", "trust_env"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._api.request", "name": "request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["method", "url", "params", "content", "data", "files", "json", "headers", "cookies", "auth", "proxy", "timeout", "follow_redirects", "verify", "trust_env"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["httpx._urls.URL", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.QueryParamTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestContent"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestData"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestFiles"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.HeaderTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.CookieTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.AuthTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.ProxyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.TimeoutTypes"}, "builtins.bool", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request", "ret_type": "httpx._models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef", "module_public": false}, "stream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["method", "url", "params", "content", "data", "files", "json", "headers", "cookies", "auth", "proxy", "timeout", "follow_redirects", "verify", "trust_env"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "httpx._api.stream", "name": "stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["method", "url", "params", "content", "data", "files", "json", "headers", "cookies", "auth", "proxy", "timeout", "follow_redirects", "verify", "trust_env"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["httpx._urls.URL", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.QueryParamTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestContent"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestData"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestFiles"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.HeaderTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.CookieTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.AuthTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.ProxyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.TimeoutTypes"}, "builtins.bool", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream", "ret_type": {".class": "Instance", "args": ["httpx._models.Response"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "httpx._api.stream", "name": "stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["method", "url", "params", "content", "data", "files", "json", "headers", "cookies", "auth", "proxy", "timeout", "follow_redirects", "verify", "trust_env"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["httpx._urls.URL", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.QueryParamTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestContent"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestData"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestFiles"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.HeaderTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.CookieTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.AuthTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.ProxyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.TimeoutTypes"}, "builtins.bool", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream", "ret_type": {".class": "Instance", "args": ["httpx._models.Response"], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/httpx/_api.py"}