{".class": "MypyFile", "_fullname": "httpx._multipart", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncByteStream": {".class": "SymbolTableNode", "cross_ref": "httpx._types.AsyncByteStream", "kind": "Gdef"}, "DataField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._multipart.DataField", "name": "DataField", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._multipart.DataField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._multipart", "mro": ["httpx._multipart.DataField", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.DataField.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": ["httpx._multipart.DataField", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "builtins.int", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DataField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._multipart.DataField._data", "name": "_data", "type": "builtins.bytes"}}, "_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._multipart.DataField._headers", "name": "_headers", "type": "builtins.bytes"}}, "get_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.DataField.get_length", "name": "get_length", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._multipart.DataField"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_length of DataField", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._multipart.DataField.name", "name": "name", "type": "builtins.str"}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.DataField.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._multipart.DataField"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of DataField", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.DataField.render_data", "name": "render_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._multipart.DataField"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_data of DataField", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.DataField.render_headers", "name": "render_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._multipart.DataField"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_headers of DataField", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "httpx._multipart.DataField.value", "name": "value", "type": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._multipart.DataField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._multipart.DataField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FileContent": {".class": "SymbolTableNode", "cross_ref": "httpx._types.FileContent", "kind": "Gdef"}, "FileField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._multipart.FileField", "name": "FileField", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._multipart.FileField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._multipart", "mro": ["httpx._multipart.FileField", "builtins.object"], "names": {".class": "SymbolTable", "CHUNK_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "httpx._multipart.FileField.CHUNK_SIZE", "name": "CHUNK_SIZE", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.FileField.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": ["httpx._multipart.FileField", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.FileTypes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FileField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._multipart.FileField._headers", "name": "_headers", "type": "builtins.bytes"}}, "file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._multipart.FileField.file", "name": "file", "type": {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.FileContent"}}}, "filename": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._multipart.FileField.filename", "name": "filename", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "get_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.FileField.get_length", "name": "get_length", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._multipart.FileField"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_length of FileField", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._multipart.FileField.headers", "name": "headers", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._multipart.FileField.name", "name": "name", "type": "builtins.str"}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.FileField.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._multipart.FileField"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of FileField", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.FileField.render_data", "name": "render_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._multipart.FileField"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_data of FileField", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.FileField.render_headers", "name": "render_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._multipart.FileField"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_headers of FileField", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._multipart.FileField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._multipart.FileField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FileTypes": {".class": "SymbolTableNode", "cross_ref": "httpx._types.FileTypes", "kind": "Gdef"}, "MultipartStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._types.SyncByteStream", "httpx._types.AsyncByteStream"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._multipart.MultipartStream", "name": "MultipartStream", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._multipart.MultipartStream", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._multipart", "mro": ["httpx._multipart.MultipartStream", "httpx._types.SyncByteStream", "httpx._types.AsyncByteStream", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_generator", "is_coroutine", "is_async_generator"], "fullname": "httpx._multipart.MultipartStream.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._multipart.MultipartStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aiter__ of MultipartStream", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "files", "boundary"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.MultipartStream.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "files", "boundary"], "arg_types": ["httpx._multipart.MultipartStream", {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestData"}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestFiles"}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MultipartStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.MultipartStream.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["httpx._multipart.MultipartStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of MultipartStream", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iter_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "files"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.MultipartStream._iter_fields", "name": "_iter_fields", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "files"], "arg_types": ["httpx._multipart.MultipartStream", {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestData"}, {".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.RequestFiles"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_iter_fields of MultipartStream", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["httpx._multipart.FileField", "httpx._multipart.DataField"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "boundary": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._multipart.MultipartStream.boundary", "name": "boundary", "type": "builtins.bytes"}}, "content_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._multipart.MultipartStream.content_type", "name": "content_type", "type": "builtins.str"}}, "fields": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._multipart.MultipartStream.fields", "name": "fields", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["httpx._multipart.FileField", "httpx._multipart.DataField"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "get_content_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.MultipartStream.get_content_length", "name": "get_content_length", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._multipart.MultipartStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_content_length of MultipartStream", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.MultipartStream.get_headers", "name": "get_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._multipart.MultipartStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_headers of MultipartStream", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_chunks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.MultipartStream.iter_chunks", "name": "iter_chunks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._multipart.MultipartStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_chunks of MultipartStream", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._multipart.MultipartStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._multipart.MultipartStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "RequestData": {".class": "SymbolTableNode", "cross_ref": "httpx._types.RequestData", "kind": "Gdef"}, "RequestFiles": {".class": "SymbolTableNode", "cross_ref": "httpx._types.RequestFiles", "kind": "Gdef"}, "SyncByteStream": {".class": "SymbolTableNode", "cross_ref": "httpx._types.SyncByteStream", "kind": "Gdef"}, "_HTML5_FORM_ENCODING_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpx._multipart._HTML5_FORM_ENCODING_RE", "name": "_HTML5_FORM_ENCODING_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_HTML5_FORM_ENCODING_REPLACEMENTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpx._multipart._HTML5_FORM_ENCODING_REPLACEMENTS", "name": "_HTML5_FORM_ENCODING_REPLACEMENTS", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._multipart.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._multipart.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._multipart.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._multipart.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._multipart.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._multipart.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_format_form_param": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["name", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart._format_form_param", "name": "_format_form_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["name", "value"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_form_param", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_guess_content_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filename"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart._guess_content_type", "name": "_guess_content_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_guess_content_type", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "get_multipart_boundary_from_content_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["content_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._multipart.get_multipart_boundary_from_content_type", "name": "get_multipart_boundary_from_content_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["content_type"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_multipart_boundary_from_content_type", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "mimetypes": {".class": "SymbolTableNode", "cross_ref": "mimetypes", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "peek_filelike_length": {".class": "SymbolTableNode", "cross_ref": "httpx._utils.peek_filelike_length", "kind": "Gdef"}, "primitive_value_to_str": {".class": "SymbolTableNode", "cross_ref": "httpx._utils.primitive_value_to_str", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "to_bytes": {".class": "SymbolTableNode", "cross_ref": "httpx._utils.to_bytes", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/httpx/_multipart.py"}