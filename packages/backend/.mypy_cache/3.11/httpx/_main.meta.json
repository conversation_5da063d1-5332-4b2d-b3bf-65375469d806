{"data_mtime": 1751259992, "dep_lines": [17, 18, 19, 20, 1, 3, 4, 5, 6, 8, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9, 10, 11, 12, 13, 14, 15, 9, 11], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 10, 10, 10, 10, 10, 20, 20], "dependencies": ["httpx._client", "httpx._exceptions", "httpx._models", "httpx._status_codes", "__future__", "functools", "json", "sys", "typing", "click", "httpcore", "builtins", "_typeshed", "abc", "click.core", "click.decorators", "click.types", "httpcore._models", "importlib", "importlib.machinery", "typing_extensions"], "hash": "f1e376e6e69a9013fe57cced3181ff8b9bc1a95a", "id": "httpx._main", "ignore_all": true, "interface_hash": "83e54e165b04fde30ef0b791be83472050a12424", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/httpx/_main.py", "plugin_data": null, "size": 15626, "suppressed": ["pygments.lexers", "pygments.util", "rich.console", "rich.markup", "rich.progress", "rich.syntax", "rich.table", "pygments", "rich"], "version_id": "1.13.0"}