{"data_mtime": 1751259987, "dep_lines": [14, 7, 9, 10, 11, 12, 1, 1, 1, 1, 1, 1, 1, 24, 19, 31], "dep_prios": [5, 5, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 10, 10, 10], "dependencies": ["httpx._exceptions", "__future__", "codecs", "io", "typing", "zlib", "builtins", "_typeshed", "abc", "httpx._models", "importlib", "importlib.machinery", "typing_extensions"], "hash": "556d0e46aa956b95f9ac134e38ab42337afa0cb7", "id": "httpx._decoders", "ignore_all": true, "interface_hash": "b33f376b16dfab666abdc0e699bc68d0adc632c1", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/httpx/_decoders.py", "plugin_data": null, "size": 12041, "suppressed": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "brotli", "zstandard"], "version_id": "1.13.0"}