{".class": "MypyFile", "_fullname": "httpx._auth", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Auth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._auth.Auth", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._auth.Auth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._auth", "mro": ["httpx._auth.Auth", "builtins.object"], "names": {".class": "SymbolTable", "async_auth_flow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": ["is_generator", "is_coroutine", "is_async_generator"], "fullname": "httpx._auth.Auth.async_auth_flow", "name": "async_auth_flow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["httpx._auth.Auth", "httpx._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "async_auth_flow of Auth", "ret_type": {".class": "Instance", "args": ["httpx._models.Request", "httpx._models.Response"], "extra_attrs": null, "type_ref": "typing.AsyncGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auth_flow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.Auth.auth_flow", "name": "auth_flow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["httpx._auth.Auth", "httpx._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "auth_flow of Auth", "ret_type": {".class": "Instance", "args": ["httpx._models.Request", "httpx._models.Response", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "requires_request_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "httpx._auth.Auth.requires_request_body", "name": "requires_request_body", "type": "builtins.bool"}}, "requires_response_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "httpx._auth.Auth.requires_response_body", "name": "requires_response_body", "type": "builtins.bool"}}, "sync_auth_flow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.Auth.sync_auth_flow", "name": "sync_auth_flow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["httpx._auth.Auth", "httpx._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sync_auth_flow of Auth", "ret_type": {".class": "Instance", "args": ["httpx._models.Request", "httpx._models.Response", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth.Auth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._auth.Auth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BasicAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._auth.Auth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._auth.BasicAuth", "name": "BasicAuth", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._auth.BasicAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._auth", "mro": ["httpx._auth.BasicAuth", "httpx._auth.Auth", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "username", "password"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.BasicAuth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "username", "password"], "arg_types": ["httpx._auth.BasicAuth", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BasicAuth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_auth_header": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._auth.BasicAuth._auth_header", "name": "_auth_header", "type": "builtins.str"}}, "_build_auth_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "username", "password"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.BasicAuth._build_auth_header", "name": "_build_auth_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "username", "password"], "arg_types": ["httpx._auth.BasicAuth", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_auth_header of BasicAuth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auth_flow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.BasicAuth.auth_flow", "name": "auth_flow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["httpx._auth.BasicAuth", "httpx._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "auth_flow of BasicAuth", "ret_type": {".class": "Instance", "args": ["httpx._models.Request", "httpx._models.Response", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth.BasicAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._auth.BasicAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Cookies": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Cookies", "kind": "Gdef", "module_public": false}, "DigestAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._auth.Auth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._auth.DigestAuth", "name": "DigestAuth", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._auth.DigestAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._auth", "mro": ["httpx._auth.DigestAuth", "httpx._auth.Auth", "builtins.object"], "names": {".class": "SymbolTable", "_ALGORITHM_TO_HASH_FUNCTION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "httpx._auth.DigestAuth._ALGORITHM_TO_HASH_FUNCTION", "name": "_ALGORITHM_TO_HASH_FUNCTION", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "hashlib._Hash", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "username", "password"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.DigestAuth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "username", "password"], "arg_types": ["httpx._auth.DigestAuth", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DigestAuth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_auth_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "challenge"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.DigestAuth._build_auth_header", "name": "_build_auth_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "challenge"], "arg_types": ["httpx._auth.DigestAuth", "httpx._models.Request", {".class": "TypeAliasType", "args": [], "type_ref": "httpx._auth._DigestAuthChallenge"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_auth_header of DigestAuth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_client_nonce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "nonce_count", "nonce"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.DigestAuth._get_client_nonce", "name": "_get_client_nonce", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "nonce_count", "nonce"], "arg_types": ["httpx._auth.DigestAuth", "builtins.int", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_client_nonce of DigestAuth", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_header_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "header_fields"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.DigestAuth._get_header_value", "name": "_get_header_value", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "header_fields"], "arg_types": ["httpx._auth.DigestAuth", {".class": "Instance", "args": ["builtins.str", "builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_header_value of DigestAuth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_last_challenge": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "httpx._auth.DigestAuth._last_challenge", "name": "_last_challenge", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._auth._DigestAuthChallenge"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_nonce_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._auth.DigestAuth._nonce_count", "name": "_nonce_count", "type": "builtins.int"}}, "_parse_challenge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "response", "auth_header"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.DigestAuth._parse_challenge", "name": "_parse_challenge", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "response", "auth_header"], "arg_types": ["httpx._auth.DigestAuth", "httpx._models.Request", "httpx._models.Response", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_challenge of DigestAuth", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "httpx._auth._DigestAuthChallenge"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_password": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._auth.DigestAuth._password", "name": "_password", "type": "builtins.bytes"}}, "_resolve_qop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "qop", "request"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.DigestAuth._resolve_qop", "name": "_resolve_qop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "qop", "request"], "arg_types": ["httpx._auth.DigestAuth", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "httpx._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_qop of DigestAuth", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_username": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._auth.DigestAuth._username", "name": "_username", "type": "builtins.bytes"}}, "auth_flow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": ["is_generator"], "fullname": "httpx._auth.DigestAuth.auth_flow", "name": "auth_flow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["httpx._auth.DigestAuth", "httpx._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "auth_flow of DigestAuth", "ret_type": {".class": "Instance", "args": ["httpx._models.Request", "httpx._models.Response", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth.DigestAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._auth.DigestAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FunctionAuth": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._auth.Auth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._auth.FunctionAuth", "name": "FunctionAuth", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._auth.FunctionAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._auth", "mro": ["httpx._auth.FunctionAuth", "httpx._auth.Auth", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "func"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.FunctionAuth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "func"], "arg_types": ["httpx._auth.FunctionAuth", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["httpx._models.Request"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "httpx._models.Request", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FunctionAuth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_func": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._auth.FunctionAuth._func", "name": "_func", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["httpx._models.Request"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "httpx._models.Request", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auth_flow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.FunctionAuth.auth_flow", "name": "auth_flow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["httpx._auth.FunctionAuth", "httpx._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "auth_flow of FunctionAuth", "ret_type": {".class": "Instance", "args": ["httpx._models.Request", "httpx._models.Response", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth.FunctionAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._auth.FunctionAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NetRCAuth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._auth.Auth"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._auth.NetRCAuth", "name": "NetRCAuth", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._auth.NetRCAuth", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._auth", "mro": ["httpx._auth.NetRCAuth", "httpx._auth.Auth", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "file"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.NetRCAuth.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "file"], "arg_types": ["httpx._auth.NetRCAuth", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NetRCAuth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_auth_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "username", "password"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.NetRCAuth._build_auth_header", "name": "_build_auth_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "username", "password"], "arg_types": ["httpx._auth.NetRCAuth", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_auth_header of NetRCAuth", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_netrc_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._auth.NetRCAuth._netrc_info", "name": "_netrc_info", "type": "netrc.netrc"}}, "auth_flow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth.NetRCAuth.auth_flow", "name": "auth_flow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["httpx._auth.NetRCAuth", "httpx._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "auth_flow of NetRCAuth", "ret_type": {".class": "Instance", "args": ["httpx._models.Request", "httpx._models.Response", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth.NetRCAuth.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._auth.NetRCAuth", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProtocolError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.ProtocolError", "kind": "Gdef", "module_public": false}, "Request": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Request", "kind": "Gdef", "module_public": false}, "Response": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Response", "kind": "Gdef", "module_public": false}, "_DigestAuthChallenge": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._auth._DigestAuthChallenge", "name": "_DigestAuthChallenge", "type_vars": []}, "deletable_attributes": [], "flags": ["is_named_tuple"], "fullname": "httpx._auth._DigestAuthChallenge", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["realm", "nonce", "algorithm", "opaque", "qop"]}}, "module_name": "httpx._auth", "mro": ["httpx._auth._DigestAuthChallenge", "builtins.tuple", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "httpx._auth._DigestAuthChallenge.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "httpx._auth._DigestAuthChallenge.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "httpx._auth._DigestAuthChallenge.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "realm"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nonce"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "algorithm"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "opaque"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "qop"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "realm", "nonce", "algorithm", "opaque", "qop"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "httpx._auth._DigestAuthChallenge.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "realm", "nonce", "algorithm", "opaque", "qop"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge._NT", "id": -1, "name": "_NT", "namespace": "httpx._auth._DigestAuthChallenge.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _DigestAuthChallenge", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge._NT", "id": -1, "name": "_NT", "namespace": "httpx._auth._DigestAuthChallenge.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge._NT", "id": -1, "name": "_NT", "namespace": "httpx._auth._DigestAuthChallenge.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth._DigestAuthChallenge._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge._NT", "id": -1, "name": "_NT", "namespace": "httpx._auth._DigestAuthChallenge._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _DigestAuthChallenge", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge._NT", "id": -1, "name": "_NT", "namespace": "httpx._auth._DigestAuthChallenge._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "httpx._auth._DigestAuthChallenge._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "httpx._auth._DigestAuthChallenge._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "httpx._auth._DigestAuthChallenge._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "httpx._auth._DigestAuthChallenge._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge._NT", "id": -1, "name": "_NT", "namespace": "httpx._auth._DigestAuthChallenge._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _DigestAuthChallenge", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge._NT", "id": -1, "name": "_NT", "namespace": "httpx._auth._DigestAuthChallenge._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge._NT", "id": -1, "name": "_NT", "namespace": "httpx._auth._DigestAuthChallenge._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "httpx._auth._DigestAuthChallenge._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge._NT", "id": -1, "name": "_NT", "namespace": "httpx._auth._DigestAuthChallenge._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _DigestAuthChallenge", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge._NT", "id": -1, "name": "_NT", "namespace": "httpx._auth._DigestAuthChallenge._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge._NT", "id": -1, "name": "_NT", "namespace": "httpx._auth._DigestAuthChallenge._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["_self", "realm", "nonce", "algorithm", "opaque", "qop"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._auth._DigestAuthChallenge._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["_self", "realm", "nonce", "algorithm", "opaque", "qop"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge._NT", "id": -1, "name": "_NT", "namespace": "httpx._auth._DigestAuthChallenge._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _DigestAuthChallenge", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge._NT", "id": -1, "name": "_NT", "namespace": "httpx._auth._DigestAuthChallenge._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge._NT", "id": -1, "name": "_NT", "namespace": "httpx._auth._DigestAuthChallenge._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "httpx._auth._DigestAuthChallenge._source", "name": "_source", "type": "builtins.str"}}, "algorithm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "httpx._auth._DigestAuthChallenge.algorithm", "name": "algorithm", "type": "builtins.str"}}, "algorithm-redefinition": {".class": "SymbolTableNode", "cross_ref": "httpx._auth._DigestAuthChallenge.algorithm", "kind": "<PERSON><PERSON><PERSON>"}, "nonce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "httpx._auth._DigestAuthChallenge.nonce", "name": "nonce", "type": "builtins.bytes"}}, "nonce-redefinition": {".class": "SymbolTableNode", "cross_ref": "httpx._auth._DigestAuthChallenge.nonce", "kind": "<PERSON><PERSON><PERSON>"}, "opaque": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "httpx._auth._DigestAuthChallenge.opaque", "name": "opaque", "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "opaque-redefinition": {".class": "SymbolTableNode", "cross_ref": "httpx._auth._DigestAuthChallenge.opaque", "kind": "<PERSON><PERSON><PERSON>"}, "qop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "httpx._auth._DigestAuthChallenge.qop", "name": "qop", "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "qop-redefinition": {".class": "SymbolTableNode", "cross_ref": "httpx._auth._DigestAuthChallenge.qop", "kind": "<PERSON><PERSON><PERSON>"}, "realm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "httpx._auth._DigestAuthChallenge.realm", "name": "realm", "type": "builtins.bytes"}}, "realm-redefinition": {".class": "SymbolTableNode", "cross_ref": "httpx._auth._DigestAuthChallenge.realm", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._auth._DigestAuthChallenge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": "httpx._auth._DigestAuthChallenge"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "_Hash": {".class": "SymbolTableNode", "cross_ref": "hashlib._Hash", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpx._auth.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._auth.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._auth.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._auth.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._auth.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._auth.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._auth.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "b64encode": {".class": "SymbolTableNode", "cross_ref": "base64.b64encode", "kind": "Gdef", "module_public": false}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "parse_http_list": {".class": "SymbolTableNode", "cross_ref": "urllib.request.parse_http_list", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}, "to_bytes": {".class": "SymbolTableNode", "cross_ref": "httpx._utils.to_bytes", "kind": "Gdef", "module_public": false}, "to_str": {".class": "SymbolTableNode", "cross_ref": "httpx._utils.to_str", "kind": "Gdef", "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}, "unquote": {".class": "SymbolTableNode", "cross_ref": "httpx._utils.unquote", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/httpx/_auth.py"}