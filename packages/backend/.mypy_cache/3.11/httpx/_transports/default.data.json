{".class": "MypyFile", "_fullname": "httpx._transports.default", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "A": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._transports.default.A", "name": "A", "upper_bound": "httpx._transports.default.AsyncHTTPTransport", "values": [], "variance": 0}}, "AsyncBaseTransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.base.AsyncBaseTransport", "kind": "Gdef", "module_public": false}, "AsyncByteStream": {".class": "SymbolTableNode", "cross_ref": "httpx._types.AsyncByteStream", "kind": "Gdef", "module_public": false}, "AsyncHTTPTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._transports.base.AsyncBaseTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._transports.default.AsyncHTTPTransport", "name": "AsyncHTTPTransport", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._transports.default.AsyncHTTPTransport", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._transports.default", "mro": ["httpx._transports.default.AsyncHTTPTransport", "httpx._transports.base.AsyncBaseTransport", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "httpx._transports.default.AsyncHTTPTransport.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._transports.default.A", "id": -1, "name": "A", "namespace": "httpx._transports.default.AsyncHTTPTransport.__aenter__", "upper_bound": "httpx._transports.default.AsyncHTTPTransport", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aenter__ of AsyncHTTPTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._transports.default.A", "id": -1, "name": "A", "namespace": "httpx._transports.default.AsyncHTTPTransport.__aenter__", "upper_bound": "httpx._transports.default.AsyncHTTPTransport", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._transports.default.A", "id": -1, "name": "A", "namespace": "httpx._transports.default.AsyncHTTPTransport.__aenter__", "upper_bound": "httpx._transports.default.AsyncHTTPTransport", "values": [], "variance": 0}]}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "httpx._transports.default.AsyncHTTPTransport.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "arg_types": ["httpx._transports.default.AsyncHTTPTransport", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aexit__ of AsyncHTTPTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "verify", "cert", "trust_env", "http1", "http2", "limits", "proxy", "uds", "local_address", "retries", "socket_options"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._transports.default.AsyncHTTPTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "verify", "cert", "trust_env", "http1", "http2", "limits", "proxy", "uds", "local_address", "retries", "socket_options"], "arg_types": ["httpx._transports.default.AsyncHTTPTransport", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.CertTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "httpx._config.Limits", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.ProxyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._transports.default.SOCKET_OPTION"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncHTTPTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pool": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._transports.default.AsyncHTTPTransport._pool", "name": "_pool", "type": "httpcore._async.connection_pool.AsyncConnectionPool"}}, "aclose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "httpx._transports.default.AsyncHTTPTransport.aclose", "name": "aclose", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._transports.default.AsyncHTTPTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aclose of AsyncHTTPTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_async_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "httpx._transports.default.AsyncHTTPTransport.handle_async_request", "name": "handle_async_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["httpx._transports.default.AsyncHTTPTransport", "httpx._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_async_request of AsyncHTTPTransport", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "httpx._models.Response"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._transports.default.AsyncHTTPTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._transports.default.AsyncHTTPTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncResponseStream": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._types.AsyncByteStream"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._transports.default.AsyncResponseStream", "name": "AsyncResponseStream", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._transports.default.AsyncResponseStream", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._transports.default", "mro": ["httpx._transports.default.AsyncResponseStream", "httpx._types.AsyncByteStream", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_generator", "is_coroutine", "is_async_generator"], "fullname": "httpx._transports.default.AsyncResponseStream.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._transports.default.AsyncResponseStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aiter__ of AsyncResponseStream", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "httpcore_stream"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._transports.default.AsyncResponseStream.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "httpcore_stream"], "arg_types": ["httpx._transports.default.AsyncResponseStream", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.AsyncIterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncResponseStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_httpcore_stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._transports.default.AsyncResponseStream._httpcore_stream", "name": "_httpcore_stream", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.AsyncIterable"}}}, "aclose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "httpx._transports.default.AsyncResponseStream.aclose", "name": "aclose", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._transports.default.AsyncResponseStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aclose of AsyncResponseStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._transports.default.AsyncResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._transports.default.AsyncResponseStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseTransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.base.BaseTransport", "kind": "Gdef", "module_public": false}, "CertTypes": {".class": "SymbolTableNode", "cross_ref": "httpx._types.CertTypes", "kind": "Gdef", "module_public": false}, "ConnectError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.ConnectError", "kind": "Gdef", "module_public": false}, "ConnectTimeout": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.ConnectTimeout", "kind": "Gdef", "module_public": false}, "DEFAULT_LIMITS": {".class": "SymbolTableNode", "cross_ref": "httpx._config.DEFAULT_LIMITS", "kind": "Gdef", "module_public": false}, "HTTPCORE_EXC_MAP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "httpx._transports.default.HTTPCORE_EXC_MAP", "name": "HTTPCORE_EXC_MAP", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.Exception"}, {".class": "TypeType", "item": "httpx._exceptions.HTTPError"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "HTTPTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._transports.base.BaseTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._transports.default.HTTPTransport", "name": "HTTPTransport", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._transports.default.HTTPTransport", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._transports.default", "mro": ["httpx._transports.default.HTTPTransport", "httpx._transports.base.BaseTransport", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._transports.default.HTTPTransport.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._transports.default.T", "id": -1, "name": "T", "namespace": "httpx._transports.default.HTTPTransport.__enter__", "upper_bound": "httpx._transports.default.HTTPTransport", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of HTTPTransport", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._transports.default.T", "id": -1, "name": "T", "namespace": "httpx._transports.default.HTTPTransport.__enter__", "upper_bound": "httpx._transports.default.HTTPTransport", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._transports.default.T", "id": -1, "name": "T", "namespace": "httpx._transports.default.HTTPTransport.__enter__", "upper_bound": "httpx._transports.default.HTTPTransport", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._transports.default.HTTPTransport.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": [null, null, null, null], "arg_types": ["httpx._transports.default.HTTPTransport", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of HTTPTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "verify", "cert", "trust_env", "http1", "http2", "limits", "proxy", "uds", "local_address", "retries", "socket_options"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._transports.default.HTTPTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "verify", "cert", "trust_env", "http1", "http2", "limits", "proxy", "uds", "local_address", "retries", "socket_options"], "arg_types": ["httpx._transports.default.HTTPTransport", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.CertTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "httpx._config.Limits", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._types.ProxyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "httpx._transports.default.SOCKET_OPTION"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pool": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._transports.default.HTTPTransport._pool", "name": "_pool", "type": "httpcore._sync.connection_pool.ConnectionPool"}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._transports.default.HTTPTransport.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._transports.default.HTTPTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of HTTPTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._transports.default.HTTPTransport.handle_request", "name": "handle_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["httpx._transports.default.HTTPTransport", "httpx._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_request of HTTPTransport", "ret_type": "httpx._models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._transports.default.HTTPTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._transports.default.HTTPTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Limits": {".class": "SymbolTableNode", "cross_ref": "httpx._config.Limits", "kind": "Gdef", "module_public": false}, "LocalProtocolError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.LocalProtocolError", "kind": "Gdef", "module_public": false}, "NetworkError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.NetworkError", "kind": "Gdef", "module_public": false}, "PoolTimeout": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.PoolTimeout", "kind": "Gdef", "module_public": false}, "ProtocolError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.ProtocolError", "kind": "Gdef", "module_public": false}, "Proxy": {".class": "SymbolTableNode", "cross_ref": "httpx._config.Proxy", "kind": "Gdef", "module_public": false}, "ProxyError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.ProxyError", "kind": "Gdef", "module_public": false}, "ProxyTypes": {".class": "SymbolTableNode", "cross_ref": "httpx._types.ProxyTypes", "kind": "Gdef", "module_public": false}, "ReadError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.ReadError", "kind": "Gdef", "module_public": false}, "ReadTimeout": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.ReadTimeout", "kind": "Gdef", "module_public": false}, "RemoteProtocolError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.RemoteProtocolError", "kind": "Gdef", "module_public": false}, "Request": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Request", "kind": "Gdef", "module_public": false}, "Response": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Response", "kind": "Gdef", "module_public": false}, "ResponseStream": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._types.SyncByteStream"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpx._transports.default.ResponseStream", "name": "ResponseStream", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "httpx._transports.default.ResponseStream", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpx._transports.default", "mro": ["httpx._transports.default.ResponseStream", "httpx._types.SyncByteStream", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "httpcore_stream"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._transports.default.ResponseStream.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "httpcore_stream"], "arg_types": ["httpx._transports.default.ResponseStream", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResponseStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._transports.default.ResponseStream.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["httpx._transports.default.ResponseStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of ResponseStream", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_httpcore_stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpx._transports.default.ResponseStream._httpcore_stream", "name": "_httpcore_stream", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._transports.default.ResponseStream.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpx._transports.default.ResponseStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of ResponseStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._transports.default.ResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpx._transports.default.ResponseStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SOCKET_OPTION": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "httpx._transports.default.SOCKET_OPTION", "line": 63, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "NoneType"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}}}, "SyncByteStream": {".class": "SymbolTableNode", "cross_ref": "httpx._types.SyncByteStream", "kind": "Gdef", "module_public": false}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpx._transports.default.T", "name": "T", "upper_bound": "httpx._transports.default.HTTPTransport", "values": [], "variance": 0}}, "TimeoutException": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.TimeoutException", "kind": "Gdef", "module_public": false}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_public": false}, "URL": {".class": "SymbolTableNode", "cross_ref": "httpx._urls.URL", "kind": "Gdef", "module_public": false}, "UnsupportedProtocol": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.UnsupportedProtocol", "kind": "Gdef", "module_public": false}, "WriteError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.WriteError", "kind": "Gdef", "module_public": false}, "WriteTimeout": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.WriteTimeout", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpx._transports.default.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.default.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.default.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.default.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.default.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.default.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.default.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_load_httpcore_exceptions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "httpx._transports.default._load_httpcore_exceptions", "name": "_load_httpcore_exceptions", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_httpcore_exceptions", "ret_type": {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.Exception"}, {".class": "TypeType", "item": "httpx._exceptions.HTTPError"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef", "module_public": false}, "create_ssl_context": {".class": "SymbolTableNode", "cross_ref": "httpx._config.create_ssl_context", "kind": "Gdef", "module_public": false}, "httpx": {".class": "SymbolTableNode", "cross_ref": "httpx", "kind": "Gdef", "module_public": false}, "map_httpcore_exceptions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "httpx._transports.default.map_httpcore_exceptions", "name": "map_httpcore_exceptions", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "map_httpcore_exceptions", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "httpx._transports.default.map_httpcore_exceptions", "name": "map_httpcore_exceptions", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "map_httpcore_exceptions", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef", "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/httpx/_transports/default.py"}