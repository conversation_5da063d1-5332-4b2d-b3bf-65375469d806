{"data_mtime": 1751259987, "dep_lines": [6, 5, 1, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 30, 30, 30], "dependencies": ["httpx._transports.base", "httpx._models", "__future__", "typing", "builtins", "abc", "importlib", "importlib.machinery"], "hash": "756bb63ba09e596ecdebdc4b60932f3e464d4806", "id": "httpx._transports.mock", "ignore_all": true, "interface_hash": "c846ce872b244d9117750cc214e3066f88c45597", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/httpx/_transports/mock.py", "plugin_data": null, "size": 1232, "suppressed": [], "version_id": "1.13.0"}