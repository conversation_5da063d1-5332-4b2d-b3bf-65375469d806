{"data_mtime": 1751259987, "dep_lines": [60, 61, 62, 5, 6, 12, 20, 21, 22, 23, 24, 25, 26, 33, 44, 45, 46, 47, 48, 49, 50, 63, 1, 2, 3, 11, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 10, 5, 5, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic.deprecated.class_validators", "pydantic.deprecated.config", "pydantic.deprecated.tools", "pydantic._migration", "pydantic.version", "pydantic_core.core_schema", "pydantic.dataclasses", "pydantic.aliases", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.fields", "pydantic.functional_serializers", "pydantic.functional_validators", "pydantic.json_schema", "pydantic.main", "pydantic.networks", "pydantic.type_adapter", "pydantic.types", "pydantic.validate_call_decorator", "pydantic.warnings", "pydantic.root_model", "typing", "importlib", "warnings", "pydantic_core", "builtins", "abc", "importlib.machinery", "pydantic_core._pydantic_core", "starlette", "starlette.exceptions"], "hash": "579ef66c69b9d8d490d5a372b0fb379ea2890347", "id": "pydantic", "ignore_all": true, "interface_hash": "5df8e788c62a7b40289f27413f9f9ed6985c715f", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/__init__.py", "plugin_data": null, "size": 14860, "suppressed": [], "version_id": "1.13.0"}