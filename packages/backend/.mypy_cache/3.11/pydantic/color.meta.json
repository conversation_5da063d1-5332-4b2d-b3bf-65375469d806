{"data_mtime": 1751259987, "dep_lines": [23, 24, 20, 23, 25, 26, 15, 16, 17, 18, 20, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 20, 5, 5, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._repr", "pydantic._internal._schema_generation_shared", "pydantic_core.core_schema", "pydantic._internal", "pydantic.json_schema", "pydantic.warnings", "math", "re", "colorsys", "typing", "pydantic_core", "typing_extensions", "builtins", "_collections_abc", "_typeshed", "abc", "importlib", "importlib.machinery", "pydantic.annotated_handlers", "pydantic.v1", "pydantic.v1.color"], "hash": "6d39ce7f6a53e604ce3251e79c8908da3d174d6d", "id": "pydantic.color", "ignore_all": true, "interface_hash": "6946a2aed03fd3263b4656dc15331c6e508c0974", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/color.py", "plugin_data": null, "size": 21494, "suppressed": [], "version_id": "1.13.0"}