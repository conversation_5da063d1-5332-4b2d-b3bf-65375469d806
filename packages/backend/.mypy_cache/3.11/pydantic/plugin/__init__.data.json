{".class": "MypyFile", "_fullname": "pydantic.plugin", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BaseValidateHandlerProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["on_enter", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.plugin.BaseValidateHandlerProtocol", "name": "BaseValidateHandlerProtocol", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.plugin.BaseValidateHandlerProtocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.plugin", "mro": ["pydantic.plugin.BaseValidateHandlerProtocol", "builtins.object"], "names": {".class": "SymbolTable", "on_enter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "pydantic.plugin.BaseValidateHandlerProtocol.on_enter", "name": "on_enter", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "error"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.plugin.BaseValidateHandlerProtocol.on_error", "name": "on_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "error"], "arg_types": ["pydantic.plugin.BaseValidateHandlerProtocol", "pydantic_core._pydantic_core.ValidationError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_error of BaseValidateHandlerProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.plugin.BaseValidateHandlerProtocol.on_exception", "name": "on_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": ["pydantic.plugin.BaseValidateHandlerProtocol", "builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_exception of BaseValidateHandlerProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.plugin.BaseValidateHandlerProtocol.on_success", "name": "on_success", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": ["pydantic.plugin.BaseValidateHandlerProtocol", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_success of BaseValidateHandlerProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.BaseValidateHandlerProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.plugin.BaseValidateHandlerProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "CoreConfig": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.CoreConfig", "kind": "Gdef", "module_public": false}, "CoreSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.CoreSchema", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_public": false}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_public": false}, "NewSchemaReturns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.plugin.NewSchemaReturns", "line": 24, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["pydantic.plugin.ValidatePythonHandlerProtocol", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["pydantic.plugin.ValidateJsonHandlerProtocol", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["pydantic.plugin.ValidateStringsHandlerProtocol", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef", "module_public": false}, "PydanticPluginProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["new_schema_validator", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.plugin.PydanticPluginProtocol", "name": "PydanticPluginProtocol", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.plugin.PydanticPluginProtocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.plugin", "mro": ["pydantic.plugin.PydanticPluginProtocol", "builtins.object"], "names": {".class": "SymbolTable", "new_schema_validator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "schema", "schema_type", "schema_type_path", "schema_kind", "config", "plugin_settings"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "pydantic.plugin.PydanticPluginProtocol.new_schema_validator", "name": "new_schema_validator", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "schema", "schema_type", "schema_type_path", "schema_kind", "config", "plugin_settings"], "arg_types": ["pydantic.plugin.PydanticPluginProtocol", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.plugin.SchemaTypePath"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.plugin.SchemaKind"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_schema_validator of PydanticPluginProtocol", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["pydantic.plugin.ValidatePythonHandlerProtocol", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["pydantic.plugin.ValidateJsonHandlerProtocol", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["pydantic.plugin.ValidateStringsHandlerProtocol", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.PydanticPluginProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.plugin.PydanticPluginProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SchemaKind": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.plugin.SchemaKind", "line": 34, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "BaseModel"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "TypeAdapter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclass"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "create_model"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "validate_call"}], "uses_pep604_syntax": false}}}, "SchemaTypePath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.plugin.SchemaTypePath", "name": "SchemaTypePath", "type_vars": []}, "deletable_attributes": [], "flags": ["is_named_tuple"], "fullname": "pydantic.plugin.SchemaTypePath", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["module", "name"]}}, "module_name": "pydantic.plugin", "mro": ["pydantic.plugin.SchemaTypePath", "builtins.tuple", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.plugin.SchemaTypePath.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.plugin.SchemaTypePath.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.plugin.SchemaTypePath.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "module"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "module", "name"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "pydantic.plugin.SchemaTypePath.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "module", "name"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath._NT", "id": -1, "name": "_NT", "namespace": "pydantic.plugin.SchemaTypePath.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of SchemaTypePath", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath._NT", "id": -1, "name": "_NT", "namespace": "pydantic.plugin.SchemaTypePath.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath._NT", "id": -1, "name": "_NT", "namespace": "pydantic.plugin.SchemaTypePath.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.plugin.SchemaTypePath._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath._NT", "id": -1, "name": "_NT", "namespace": "pydantic.plugin.SchemaTypePath._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of SchemaTypePath", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath._NT", "id": -1, "name": "_NT", "namespace": "pydantic.plugin.SchemaTypePath._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.plugin.SchemaTypePath._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.plugin.SchemaTypePath._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.plugin.SchemaTypePath._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.plugin.SchemaTypePath._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath._NT", "id": -1, "name": "_NT", "namespace": "pydantic.plugin.SchemaTypePath._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of SchemaTypePath", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath._NT", "id": -1, "name": "_NT", "namespace": "pydantic.plugin.SchemaTypePath._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath._NT", "id": -1, "name": "_NT", "namespace": "pydantic.plugin.SchemaTypePath._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "pydantic.plugin.SchemaTypePath._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath._NT", "id": -1, "name": "_NT", "namespace": "pydantic.plugin.SchemaTypePath._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of SchemaTypePath", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath._NT", "id": -1, "name": "_NT", "namespace": "pydantic.plugin.SchemaTypePath._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath._NT", "id": -1, "name": "_NT", "namespace": "pydantic.plugin.SchemaTypePath._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "module", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.plugin.SchemaTypePath._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "module", "name"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath._NT", "id": -1, "name": "_NT", "namespace": "pydantic.plugin.SchemaTypePath._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of SchemaTypePath", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath._NT", "id": -1, "name": "_NT", "namespace": "pydantic.plugin.SchemaTypePath._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath._NT", "id": -1, "name": "_NT", "namespace": "pydantic.plugin.SchemaTypePath._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.plugin.SchemaTypePath._source", "name": "_source", "type": "builtins.str"}}, "module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "pydantic.plugin.SchemaTypePath.module", "name": "module", "type": "builtins.str"}}, "module-redefinition": {".class": "SymbolTableNode", "cross_ref": "pydantic.plugin.SchemaTypePath.module", "kind": "<PERSON><PERSON><PERSON>"}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "pydantic.plugin.SchemaTypePath.name", "name": "name", "type": "builtins.str"}}, "name-redefinition": {".class": "SymbolTableNode", "cross_ref": "pydantic.plugin.SchemaTypePath.name", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.SchemaTypePath.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": "pydantic.plugin.SchemaTypePath"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "StringInput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.plugin.StringInput", "line": 155, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.plugin.StringInput"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_public": false}, "ValidateJsonHandlerProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["on_enter", 2]], "alt_promote": null, "bases": ["pydantic.plugin.BaseValidateHandlerProtocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.plugin.ValidateJsonHandlerProtocol", "name": "ValidateJsonHandlerProtocol", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.plugin.ValidateJsonHandlerProtocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.plugin", "mro": ["pydantic.plugin.ValidateJsonHandlerProtocol", "pydantic.plugin.BaseValidateHandlerProtocol", "builtins.object"], "names": {".class": "SymbolTable", "on_enter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "input", "strict", "context", "self_instance"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "pydantic.plugin.ValidateJsonHandlerProtocol.on_enter", "name": "on_enter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "input", "strict", "context", "self_instance"], "arg_types": ["pydantic.plugin.ValidateJsonHandlerProtocol", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_enter of ValidateJsonHandlerProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.ValidateJsonHandlerProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.plugin.ValidateJsonHandlerProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ValidatePythonHandlerProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["on_enter", 2]], "alt_promote": null, "bases": ["pydantic.plugin.BaseValidateHandlerProtocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.plugin.ValidatePythonHandlerProtocol", "name": "ValidatePythonHandlerProtocol", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.plugin.ValidatePythonHandlerProtocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.plugin", "mro": ["pydantic.plugin.ValidatePythonHandlerProtocol", "pydantic.plugin.BaseValidateHandlerProtocol", "builtins.object"], "names": {".class": "SymbolTable", "on_enter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "input", "strict", "from_attributes", "context", "self_instance"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "pydantic.plugin.ValidatePythonHandlerProtocol.on_enter", "name": "on_enter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "input", "strict", "from_attributes", "context", "self_instance"], "arg_types": ["pydantic.plugin.ValidatePythonHandlerProtocol", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_enter of ValidatePythonHandlerProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.ValidatePythonHandlerProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.plugin.ValidatePythonHandlerProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ValidateStringsHandlerProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["on_enter", 2]], "alt_promote": null, "bases": ["pydantic.plugin.BaseValidateHandlerProtocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.plugin.ValidateStringsHandlerProtocol", "name": "ValidateStringsHandlerProtocol", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.plugin.ValidateStringsHandlerProtocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.plugin", "mro": ["pydantic.plugin.ValidateStringsHandlerProtocol", "pydantic.plugin.BaseValidateHandlerProtocol", "builtins.object"], "names": {".class": "SymbolTable", "on_enter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "input", "strict", "context"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "pydantic.plugin.ValidateStringsHandlerProtocol.on_enter", "name": "on_enter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "input", "strict", "context"], "arg_types": ["pydantic.plugin.ValidateStringsHandlerProtocol", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.plugin.StringInput"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_enter of ValidateStringsHandlerProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.plugin.ValidateStringsHandlerProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.plugin.ValidateStringsHandlerProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.ValidationError", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.plugin.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.plugin.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.plugin.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.plugin.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.plugin.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.plugin.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.plugin.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.plugin.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/plugin/__init__.py"}