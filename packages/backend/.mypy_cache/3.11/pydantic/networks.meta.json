{"data_mtime": 1751259987, "dep_lines": [20, 20, 9, 13, 18, 20, 21, 22, 23, 24, 3, 5, 6, 8, 10, 11, 13, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 27], "dep_prios": [10, 10, 5, 10, 5, 20, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 25], "dependencies": ["pydantic._internal._repr", "pydantic._internal._schema_generation_shared", "importlib.metadata", "pydantic_core.core_schema", "pydantic.errors", "pydantic._internal", "pydantic._migration", "pydantic.annotated_handlers", "pydantic.json_schema", "pydantic.type_adapter", "__future__", "dataclasses", "re", "functools", "ipaddress", "typing", "pydantic_core", "typing_extensions", "builtins", "_typeshed", "abc", "importlib", "importlib.machinery", "pydantic.v1", "pydantic.v1.networks", "pydantic_core._pydantic_core", "types"], "hash": "4b792435809b2c6754758117404d7b08132b1320", "id": "pydantic.networks", "ignore_all": true, "interface_hash": "f3e5f0aadb59d6757977f9a7d9fd640efee62dec", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/networks.py", "plugin_data": null, "size": 39766, "suppressed": ["email_validator"], "version_id": "1.13.0"}