{"data_mtime": 1751259987, "dep_lines": [48, 48, 48, 48, 48, 48, 48, 48, 65, 42, 46, 48, 58, 59, 60, 67, 1502, 2161, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 41, 44, 63, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 20, 5, 5, 20, 5, 5, 5, 20, 20, 20, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._core_metadata", "pydantic._internal._core_utils", "pydantic._internal._decorators", "pydantic._internal._internal_dataclass", "pydantic._internal._mock_val_ser", "pydantic._internal._schema_generation_shared", "pydantic._internal._typing_extra", "pydantic._internal._dataclasses", "pydantic_core.core_schema", "pydantic.warnings", "pydantic._internal", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.main", "pydantic.root_model", "pydantic.type_adapter", "__future__", "dataclasses", "inspect", "math", "os", "re", "warnings", "collections", "copy", "enum", "typing", "pydantic_core", "typing_extensions", "pydantic", "builtins", "_collections_abc", "_decimal", "_operator", "_typeshed", "_warnings", "abc", "datetime", "importlib", "importlib.machinery", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "types"], "hash": "4bd7977553dd15dbdfa1972c68ad4f04269e5540", "id": "pydantic.json_schema", "ignore_all": true, "interface_hash": "130fe88b77f92ef6f636dcd5551a0212dbe7bcbc", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/json_schema.py", "plugin_data": null, "size": 112669, "suppressed": [], "version_id": "1.13.0"}