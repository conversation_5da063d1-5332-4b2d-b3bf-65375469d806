{"data_mtime": 1751259987, "dep_lines": [9, 8, 12, 13, 14, 1, 3, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 25, 25, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.plugin._schema_validator", "pydantic.errors", "pydantic.dataclasses", "pydantic.main", "pydantic.type_adapter", "__future__", "typing", "pydantic_core", "typing_extensions", "builtins", "_typeshed", "abc", "importlib", "importlib.machinery", "pydantic._internal._dataclasses", "pydantic._internal._model_construction", "pydantic.plugin", "pydantic_core._pydantic_core", "pydantic_core.core_schema"], "hash": "061c3758824d0ad7db6c3c3029713e13bef09a7e", "id": "pydantic._internal._mock_val_ser", "ignore_all": true, "interface_hash": "f9d0d18f87a4206069af94812c89d678307e3912", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/_internal/_mock_val_ser.py", "plugin_data": null, "size": 9166, "suppressed": [], "version_id": "1.13.0"}