{"data_mtime": 1751259987, "dep_lines": [8, 7, 1, 3, 4, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._schema_generation_shared", "pydantic.config", "__future__", "typing", "warnings", "builtins", "abc", "importlib", "importlib.machinery", "pydantic.annotated_handlers", "pydantic_core", "pydantic_core.core_schema"], "hash": "bce2a186ab4eeeea08e9ffe82045ac98b3b335e0", "id": "pydantic._internal._core_metadata", "ignore_all": true, "interface_hash": "61990ef5062b9e4de09ff2f477b75095f788e3d7", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/_internal/_core_metadata.py", "plugin_data": null, "size": 4655, "suppressed": [], "version_id": "1.13.0"}