{"data_mtime": 1751259987, "dep_lines": [17, 18, 19, 20, 21, 22, 23, 30, 31, 15, 17, 28, 29, 3, 5, 6, 7, 8, 9, 10, 12, 13, 26, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 25, 25, 5, 20, 25, 25, 5, 10, 10, 5, 5, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._typing_extra", "pydantic._internal._config", "pydantic._internal._docs_extraction", "pydantic._internal._import_utils", "pydantic._internal._namespace_utils", "pydantic._internal._repr", "pydantic._internal._utils", "pydantic._internal._dataclasses", "pydantic._internal._decorators", "pydantic.errors", "pydantic._internal", "pydantic.fields", "pydantic.main", "__future__", "dataclasses", "warnings", "copy", "functools", "inspect", "typing", "pydantic_core", "typing_extensions", "annotated_types", "builtins", "_typeshed", "abc", "importlib", "importlib.machinery", "pydantic._internal._model_construction"], "hash": "b30e9eeeb91764974367c46707f56e24bc675250", "id": "pydantic._internal._fields", "ignore_all": true, "interface_hash": "f329f73aec15e7c059f04201de7b9f95be25ea9c", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py", "plugin_data": null, "size": 17060, "suppressed": [], "version_id": "1.13.0"}