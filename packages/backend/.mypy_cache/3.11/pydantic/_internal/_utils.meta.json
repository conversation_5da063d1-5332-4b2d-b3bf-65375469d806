{"data_mtime": 1751259987, "dep_lines": [22, 22, 23, 22, 28, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 20, 25, 5, 10, 10, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal._import_utils", "pydantic._internal", "pydantic.main", "__future__", "dataclasses", "keyword", "typing", "weakref", "collections", "copy", "functools", "inspect", "itertools", "types", "typing_extensions", "builtins", "_typeshed", "_weakref", "abc", "importlib", "importlib.machinery", "pydantic.v1", "pydantic.v1.utils"], "hash": "24d360fc48d9177947dce0f8ecb69d061b84ea34", "id": "pydantic._internal._utils", "ignore_all": true, "interface_hash": "959bf84d69173f75a2f8fb959ac0d0703814060e", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/_internal/_utils.py", "plugin_data": null, "size": 13537, "suppressed": [], "version_id": "1.13.0"}