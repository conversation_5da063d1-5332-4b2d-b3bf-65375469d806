{"data_mtime": 1751259987, "dep_lines": [20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 39, 19, 21, 42, 45, 164, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 25, 20, 5, 10, 10, 10, 5, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.plugin._schema_validator", "pydantic._internal._config", "pydantic._internal._decorators", "pydantic._internal._fields", "pydantic._internal._generate_schema", "pydantic._internal._generics", "pydantic._internal._import_utils", "pydantic._internal._mock_val_ser", "pydantic._internal._namespace_utils", "pydantic._internal._schema_generation_shared", "pydantic._internal._signature", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic.errors", "pydantic.warnings", "pydantic.fields", "pydantic.main", "pydantic.root_model", "__future__", "builtins", "operator", "sys", "typing", "warnings", "weakref", "abc", "functools", "types", "pydantic_core", "typing_extensions", "_collections_abc", "_typeshed", "_warnings", "_weakref", "annotated_types", "importlib", "importlib.machinery", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.types", "re"], "hash": "a161f72c055a77c3def1227c6ba8548c95eedd19", "id": "pydantic._internal._model_construction", "ignore_all": true, "interface_hash": "0090cc23be4f5cbb0f368937916598efac877886", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/_internal/_model_construction.py", "plugin_data": null, "size": 36807, "suppressed": [], "version_id": "1.13.0"}