{".class": "MypyFile", "_fullname": "pydantic._internal._generate_schema", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AfterValidator": {".class": "SymbolTableNode", "cross_ref": "pydantic.functional_validators.AfterValidator", "kind": "Gdef"}, "AliasChoices": {".class": "SymbolTableNode", "cross_ref": "pydantic.aliases.AliasChoices", "kind": "Gdef"}, "AliasGenerator": {".class": "SymbolTableNode", "cross_ref": "pydantic.aliases.AliasGenerator", "kind": "Gdef"}, "AliasPath": {".class": "SymbolTableNode", "cross_ref": "pydantic.aliases.AliasPath", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyFieldDecorator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._generate_schema.AnyFieldDecorator", "line": 114, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["pydantic._internal._decorators.ValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}, {".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}, {".class": "Instance", "args": ["pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "uses_pep604_syntax": false}}}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "BeforeValidator": {".class": "SymbolTableNode", "cross_ref": "pydantic.functional_validators.BeforeValidator", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CallbackGetCoreSchemaHandler": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._schema_generation_shared.CallbackGetCoreSchemaHandler", "kind": "Gdef"}, "ComputedFieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.ComputedFieldInfo", "kind": "Gdef"}, "ConfigDict": {".class": "SymbolTableNode", "cross_ref": "pydantic.config.ConfigDict", "kind": "Gdef"}, "ConfigWrapper": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._config.ConfigWrapper", "kind": "Gdef"}, "ConfigWrapperStack": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._config.ConfigWrapperStack", "kind": "Gdef"}, "CoreSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.CoreSchema", "kind": "Gdef"}, "DEQUE_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.DEQUE_TYPES", "name": "DEQUE_TYPES", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "DICT_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.DICT_TYPES", "name": "DICT_TYPES", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "_decimal.Decimal", "kind": "Gdef"}, "Decorator": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.Decorator", "kind": "Gdef"}, "DecoratorInfos": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.DecoratorInfos", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Discriminator": {".class": "SymbolTableNode", "cross_ref": "pydantic.types.Discriminator", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "FROZEN_SET_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.FROZEN_SET_TYPES", "name": "FROZEN_SET_TYPES", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "FieldDecoratorInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._generate_schema.FieldDecoratorInfo", "line": 112, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "uses_pep604_syntax": false}}}, "FieldDecoratorInfoType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema.FieldDecoratorInfoType", "name": "FieldDecoratorInfoType", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "FieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.FieldInfo", "kind": "Gdef"}, "FieldSerializerDecoratorInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "kind": "Gdef"}, "FieldValidatorDecoratorInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "kind": "Gdef"}, "FieldValidatorModes": {".class": "SymbolTableNode", "cross_ref": "pydantic.functional_validators.FieldValidatorModes", "kind": "Gdef"}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef"}, "ForwardRef": {".class": "SymbolTableNode", "cross_ref": "typing.ForwardRef", "kind": "Gdef"}, "Fraction": {".class": "SymbolTableNode", "cross_ref": "fractions.Fraction", "kind": "Gdef"}, "FunctionType": {".class": "SymbolTableNode", "cross_ref": "types.FunctionType", "kind": "Gdef"}, "GenerateSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._generate_schema.GenerateSchema", "name": "GenerateSchema", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._generate_schema", "mro": ["pydantic._internal._generate_schema.GenerateSchema", "builtins.object"], "names": {".class": "SymbolTable", "CollectedInvalid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._generate_schema.GenerateSchema.CollectedInvalid", "name": "CollectedInvalid", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema.CollectedInvalid", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._generate_schema", "mro": ["pydantic._internal._generate_schema.GenerateSchema.CollectedInvalid", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema.GenerateSchema.CollectedInvalid.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._generate_schema.GenerateSchema.CollectedInvalid", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "config_wrapper", "ns_resolver", "typevars_map"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "config_wrapper", "ns_resolver", "typevars_map"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "pydantic._internal._config.ConfigWrapper", {".class": "UnionType", "items": ["pydantic._internal._namespace_utils.NsResolver", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GenerateSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init_subclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class"], "fullname": "pydantic._internal._generate_schema.GenerateSchema.__init_subclass__", "name": "__init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pydantic._internal._generate_schema.GenerateSchema"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init_subclass__ of GenerateSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic._internal._generate_schema.GenerateSchema.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_add_js_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "metadata_schema", "js_function"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._add_js_function", "name": "_add_js_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "metadata_schema", "js_function"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_js_function of GenerateSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_annotated_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "annotated_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._annotated_schema", "name": "_annotated_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "annotated_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_annotated_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_alias_generator_to_computed_field_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["alias_generator", "computed_field_info", "computed_field_name"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_alias_generator_to_computed_field_info", "name": "_apply_alias_generator_to_computed_field_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["alias_generator", "computed_field_info", "computed_field_name"], "arg_types": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "pydantic.aliases.AliasGenerator"], "uses_pep604_syntax": true}, "pydantic.fields.ComputedFieldInfo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_alias_generator_to_computed_field_info of GenerateSchema", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_alias_generator_to_computed_field_info", "name": "_apply_alias_generator_to_computed_field_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["alias_generator", "computed_field_info", "computed_field_name"], "arg_types": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "pydantic.aliases.AliasGenerator"], "uses_pep604_syntax": true}, "pydantic.fields.ComputedFieldInfo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_alias_generator_to_computed_field_info of GenerateSchema", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_apply_alias_generator_to_field_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["alias_generator", "field_info", "field_name"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_alias_generator_to_field_info", "name": "_apply_alias_generator_to_field_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["alias_generator", "field_info", "field_name"], "arg_types": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "pydantic.aliases.AliasGenerator"], "uses_pep604_syntax": true}, "pydantic.fields.FieldInfo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_alias_generator_to_field_info of GenerateSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_alias_generator_to_field_info", "name": "_apply_alias_generator_to_field_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["alias_generator", "field_info", "field_name"], "arg_types": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "pydantic.aliases.AliasGenerator"], "uses_pep604_syntax": true}, "pydantic.fields.FieldInfo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_alias_generator_to_field_info of GenerateSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_apply_annotations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "source_type", "annotations", "transform_inner_schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_annotations", "name": "_apply_annotations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "source_type", "annotations", "transform_inner_schema"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_annotations of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_discriminator_to_union": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "discriminator"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_discriminator_to_union", "name": "_apply_discriminator_to_union", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "discriminator"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", "pydantic.types.Discriminator", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_discriminator_to_union of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_field_serializers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "serializers"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_field_serializers", "name": "_apply_field_serializers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "serializers"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_field_serializers of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_field_title_generator_to_field_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["config_wrapper", "field_info", "field_name"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_field_title_generator_to_field_info", "name": "_apply_field_title_generator_to_field_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["config_wrapper", "field_info", "field_name"], "arg_types": ["pydantic._internal._config.ConfigWrapper", {".class": "UnionType", "items": ["pydantic.fields.FieldInfo", "pydantic.fields.ComputedFieldInfo"], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_field_title_generator_to_field_info of GenerateSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_field_title_generator_to_field_info", "name": "_apply_field_title_generator_to_field_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["config_wrapper", "field_info", "field_name"], "arg_types": ["pydantic._internal._config.ConfigWrapper", {".class": "UnionType", "items": ["pydantic.fields.FieldInfo", "pydantic.fields.ComputedFieldInfo"], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_field_title_generator_to_field_info of GenerateSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_apply_model_serializers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "serializers"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_model_serializers", "name": "_apply_model_serializers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "serializers"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.ModelSerializerDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_model_serializers of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_single_annotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "metadata"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_single_annotation", "name": "_apply_single_annotation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "metadata"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_single_annotation of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_single_annotation_json_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "metadata"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_single_annotation_json_schema", "name": "_apply_single_annotation_json_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "metadata"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_single_annotation_json_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_arbitrary_type_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tp"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._arbitrary_type_schema", "name": "_arbitrary_type_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tp"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_arbitrary_type_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_arbitrary_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._arbitrary_types", "name": "_arbitrary_types", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_arbitrary_types of GenerateSchema", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._arbitrary_types", "name": "_arbitrary_types", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_arbitrary_types of GenerateSchema", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_call_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "function"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._call_schema", "name": "_call_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "function"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generate_schema.ValidateCallSupportedTypes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_call_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CallSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_common_field_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "field_info", "decorators"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._common_field_schema", "name": "_common_field_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "field_info", "decorators"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "builtins.str", "pydantic.fields.FieldInfo", "pydantic._internal._decorators.DecoratorInfos"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_common_field_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generate_schema._CommonField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_computed_field_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "d", "field_serializers"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._computed_field_schema", "name": "_computed_field_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "d", "field_serializers"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "Instance", "args": ["pydantic.fields.ComputedFieldInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_computed_field_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_config_wrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._config_wrapper", "name": "_config_wrapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_config_wrapper of GenerateSchema", "ret_type": "pydantic._internal._config.ConfigWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._config_wrapper", "name": "_config_wrapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_config_wrapper of GenerateSchema", "ret_type": "pydantic._internal._config.ConfigWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_config_wrapper_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._config_wrapper_stack", "name": "_config_wrapper_stack", "type": "pydantic._internal._config.ConfigWrapperStack"}}, "_dataclass_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dataclass", "origin"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._dataclass_schema", "name": "_dataclass_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dataclass", "origin"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeType", "item": "_typeshed.DataclassInstance"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "_typeshed.DataclassInstance"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dataclass_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_dict_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "keys_type", "values_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._dict_schema", "name": "_dict_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "keys_type", "values_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dict_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_enum_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "enum_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._enum_schema", "name": "_enum_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "enum_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeType", "item": "enum.Enum"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_enum_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fraction_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._fraction_schema", "name": "_fraction_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fraction_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_frozenset_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._frozenset_schema", "name": "_frozenset_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "items_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_frozenset_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_dc_field_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "field_info", "decorators"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._generate_dc_field_schema", "name": "_generate_dc_field_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "field_info", "decorators"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "builtins.str", "pydantic.fields.FieldInfo", "pydantic._internal._decorators.DecoratorInfos"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_dc_field_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DataclassField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_md_field_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "field_info", "decorators"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._generate_md_field_schema", "name": "_generate_md_field_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "field_info", "decorators"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "builtins.str", "pydantic.fields.FieldInfo", "pydantic._internal._decorators.DecoratorInfos"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_md_field_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_parameter_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "name", "annotation", "default", "mode"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._generate_parameter_schema", "name": "_generate_parameter_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "name", "annotation", "default", "mode"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "positional_only"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "positional_or_keyword"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keyword_only"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_parameter_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ArgumentsParameter"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_schema_from_property": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "source"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._generate_schema_from_property", "name": "_generate_schema_from_property", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "source"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_schema_from_property of GenerateSchema", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_schema_inner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._generate_schema_inner", "name": "_generate_schema_inner", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_schema_inner of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_td_field_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "name", "field_info", "decorators", "required"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._generate_td_field_schema", "name": "_generate_td_field_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "name", "field_info", "decorators", "required"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "builtins.str", "pydantic.fields.FieldInfo", "pydantic._internal._decorators.DecoratorInfos", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_td_field_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TypedDictField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_args_resolving_forward_refs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_args_resolving_forward_refs", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "required"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_args_resolving_forward_refs", "name": "_get_args_resolving_forward_refs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "required"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_args_resolving_forward_refs of GenerateSchema", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "required"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_args_resolving_forward_refs", "name": "_get_args_resolving_forward_refs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "required"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_args_resolving_forward_refs of GenerateSchema", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_args_resolving_forward_refs", "name": "_get_args_resolving_forward_refs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "required"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_args_resolving_forward_refs of GenerateSchema", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_args_resolving_forward_refs", "name": "_get_args_resolving_forward_refs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_args_resolving_forward_refs of GenerateSchema", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_args_resolving_forward_refs", "name": "_get_args_resolving_forward_refs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_args_resolving_forward_refs of GenerateSchema", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "required"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_args_resolving_forward_refs of GenerateSchema", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_args_resolving_forward_refs of GenerateSchema", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_get_first_arg_or_any": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_first_arg_or_any", "name": "_get_first_arg_or_any", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_first_arg_or_any of GenerateSchema", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_first_two_args_or_any": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_first_two_args_or_any", "name": "_get_first_two_args_or_any", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_first_two_args_or_any of GenerateSchema", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_prepare_pydantic_annotations_for_known_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "annotations"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_prepare_pydantic_annotations_for_known_type", "name": "_get_prepare_pydantic_annotations_for_known_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "annotations"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_prepare_pydantic_annotations_for_known_type of GenerateSchema", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_wrapped_inner_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "get_inner_schema", "annotation", "pydantic_js_annotation_functions"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_wrapped_inner_schema", "name": "_get_wrapped_inner_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "get_inner_schema", "annotation", "pydantic_js_annotation_functions"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "pydantic.annotated_handlers.GetCoreSchemaHandler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._schema_generation_shared.GetJsonSchemaFunction"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_wrapped_inner_schema of GenerateSchema", "ret_type": "pydantic._internal._schema_generation_shared.CallbackGetCoreSchemaHandler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_hashable_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._hashable_schema", "name": "_hashable_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_hashable_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ip_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tp"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._ip_schema", "name": "_ip_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tp"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ip_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iterable_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "type_"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._iterable_schema", "name": "_iterable_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "type_"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_iterable_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.GeneratorSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_list_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._list_schema", "name": "_list_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "items_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_list_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_literal_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "literal_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._literal_schema", "name": "_literal_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "literal_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_literal_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_match_generic_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "origin"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._match_generic_type", "name": "_match_generic_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "origin"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_match_generic_type of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_model_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._model_schema", "name": "_model_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeType", "item": "pydantic.main.BaseModel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_model_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_namedtuple_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "namedtuple_cls", "origin"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._namedtuple_schema", "name": "_namedtuple_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "namedtuple_cls", "origin"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_namedtuple_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ns_resolver": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._ns_resolver", "name": "_ns_resolver", "type": "pydantic._internal._namespace_utils.NsResolver"}}, "_pattern_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pattern_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._pattern_schema", "name": "_pattern_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pattern_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pattern_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_forward_ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._resolve_forward_ref", "name": "_resolve_forward_ref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_forward_ref of GenerateSchema", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_self_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._resolve_self_type", "name": "_resolve_self_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_self_type of GenerateSchema", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sequence_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._sequence_schema", "name": "_sequence_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "items_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sequence_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._set_schema", "name": "_set_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "items_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_subclass_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "type_"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._subclass_schema", "name": "_subclass_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "type_"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_subclass_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tuple_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tuple_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._tuple_schema", "name": "_tuple_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tuple_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tuple_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_type_alias_type_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._type_alias_type_schema", "name": "_type_alias_type_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "typing_extensions.TypeAliasType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_type_alias_type_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_type_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._type_schema", "name": "_type_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_type_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_typed_dict_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "typed_dict_cls", "origin"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._typed_dict_schema", "name": "_typed_dict_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "typed_dict_cls", "origin"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_typed_dict_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_types_namespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._types_namespace", "name": "_types_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_types_namespace of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.NamespacesTuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._types_namespace", "name": "_types_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_types_namespace of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.NamespacesTuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_typevars_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._typevars_map", "name": "_typevars_map", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_union_is_subclass_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "union_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._union_is_subclass_schema", "name": "_union_is_subclass_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "union_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_union_is_subclass_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_union_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "union_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._union_schema", "name": "_union_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "union_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_union_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unknown_type_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._unknown_type_schema", "name": "_unknown_type_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unknown_type_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unpack_refs_defs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._unpack_refs_defs", "name": "_unpack_refs_defs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unpack_refs_defs of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unsubstituted_typevar_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "typevar"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._unsubstituted_typevar_schema", "name": "_unsubstituted_typevar_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "typevar"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "typing.TypeVar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unsubstituted_typevar_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_zoneinfo_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._zoneinfo_schema", "name": "_zoneinfo_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_zoneinfo_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clean_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema.clean_schema", "name": "clean_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clean_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "collect_definitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema.collect_definitions", "name": "collect_definitions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collect_definitions of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "defs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema.defs", "name": "defs", "type": "pydantic._internal._generate_schema._Definitions"}}, "field_name_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema.field_name_stack", "name": "field_name_stack", "type": "pydantic._internal._generate_schema._FieldNameStack"}}, "generate_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "from_dunder_get_core_schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema.generate_schema", "name": "generate_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "from_dunder_get_core_schema"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema.match_type", "name": "match_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_type of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_type_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema.model_type_stack", "name": "model_type_stack", "type": "pydantic._internal._generate_schema._ModelTypeStack"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema.GenerateSchema.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._generate_schema.GenerateSchema", "values": [], "variance": 0}, "slots": ["_config_wrapper_stack", "_ns_resolver", "_typevars_map", "defs", "field_name_stack", "model_type_stack"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetCoreSchemaFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._generate_schema.GetCoreSchemaFunction", "line": 121, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "GetCoreSchemaHandler": {".class": "SymbolTableNode", "cross_ref": "pydantic.annotated_handlers.GetCoreSchemaHandler", "kind": "Gdef"}, "GetJsonSchemaFunction": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._schema_generation_shared.GetJsonSchemaFunction", "kind": "Gdef"}, "GetJsonSchemaHandler": {".class": "SymbolTableNode", "cross_ref": "pydantic.annotated_handlers.GetJsonSchemaHandler", "kind": "Gdef"}, "IP_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.IP_TYPES", "name": "IP_TYPES", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "IPv4Address": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv4Address", "kind": "Gdef"}, "IPv4Interface": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv4Interface", "kind": "Gdef"}, "IPv4Network": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv4Network", "kind": "Gdef"}, "IPv6Address": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv6Address", "kind": "Gdef"}, "IPv6Interface": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv6Interface", "kind": "Gdef"}, "IPv6Network": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv6Network", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "JsonDict": {".class": "SymbolTableNode", "cross_ref": "pydantic.config.JsonDict", "kind": "Gdef"}, "JsonEncoder": {".class": "SymbolTableNode", "cross_ref": "pydantic.config.JsonEncoder", "kind": "Gdef"}, "JsonEncoders": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._generate_schema.JsonEncoders", "line": 272, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonEncoder"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "JsonSchemaExtraCallable": {".class": "SymbolTableNode", "cross_ref": "pydantic.config.JsonSchemaExtraCallable", "kind": "Gdef"}, "JsonSchemaValue": {".class": "SymbolTableNode", "cross_ref": "pydantic.json_schema.JsonSchemaValue", "kind": "Gdef"}, "LIST_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.LIST_TYPES", "name": "LIST_TYPES", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "LambdaType": {".class": "SymbolTableNode", "cross_ref": "types.LambdaType", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "MAPPING_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.MAPPING_TYPES", "name": "MAPPING_TYPES", "type": {".class": "Instance", "args": ["abc.ABCMeta"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MethodType": {".class": "SymbolTableNode", "cross_ref": "types.MethodType", "kind": "Gdef"}, "MockCoreSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._mock_val_ser.MockCoreSchema", "kind": "Gdef"}, "ModelSerializerDecoratorInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "kind": "Gdef"}, "ModelValidatorDecoratorInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "kind": "Gdef"}, "ModifyCoreSchemaWrapHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._generate_schema.ModifyCoreSchemaWrapHandler", "line": 120, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "pydantic.annotated_handlers.GetCoreSchemaHandler"}}, "MultiHostUrl": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.MultiHostUrl", "kind": "Gdef"}, "NamespacesTuple": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._namespace_utils.NamespacesTuple", "kind": "Gdef"}, "NsResolver": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._namespace_utils.NsResolver", "kind": "Gdef"}, "PATH_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.PATH_TYPES", "name": "PATH_TYPES", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "Parameter": {".class": "SymbolTableNode", "cross_ref": "inspect.Parameter", "kind": "Gdef"}, "PlainValidator": {".class": "SymbolTableNode", "cross_ref": "pydantic.functional_validators.PlainValidator", "kind": "Gdef"}, "PydanticCustomError": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticCustomError", "kind": "Gdef"}, "PydanticDeprecatedSince20": {".class": "SymbolTableNode", "cross_ref": "pydantic.warnings.PydanticDeprecatedSince20", "kind": "Gdef"}, "PydanticRecursiveRef": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._forward_ref.PydanticRecursiveRef", "kind": "Gdef"}, "PydanticSchemaGenerationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticSchemaGenerationError", "kind": "Gdef"}, "PydanticSerializationUnexpectedValue": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticSerializationUnexpectedValue", "kind": "Gdef"}, "PydanticUndefined": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefined", "kind": "Gdef"}, "PydanticUndefinedAnnotation": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUndefinedAnnotation", "kind": "Gdef"}, "PydanticUserError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUserError", "kind": "Gdef"}, "RootValidatorDecoratorInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.RootValidatorDecoratorInfo", "kind": "Gdef"}, "SEQUENCE_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.SEQUENCE_TYPES", "name": "SEQUENCE_TYPES", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "SET_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.SET_TYPES", "name": "SET_TYPES", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "StandardDataclass": {".class": "SymbolTableNode", "cross_ref": "_typeshed.DataclassInstance", "kind": "Gdef"}, "TUPLE_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.TUPLE_TYPES", "name": "TUPLE_TYPES", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeAliasType": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAliasType", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypedDict", "kind": "Gdef"}, "UUID": {".class": "SymbolTableNode", "cross_ref": "uuid.UUID", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "Url": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.Url", "kind": "Gdef"}, "VALIDATE_CALL_SUPPORTED_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.VALIDATE_CALL_SUPPORTED_TYPES", "name": "VALIDATE_CALL_SUPPORTED_TYPES", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "ValidateCallSupportedTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._generate_schema.ValidateCallSupportedTypes", "line": 154, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["types.FunctionType", "types.FunctionType", "types.MethodType", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "functools.partial"}], "uses_pep604_syntax": false}}}, "ValidatorDecoratorInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.ValidatorDecoratorInfo", "kind": "Gdef"}, "WrapValidator": {".class": "SymbolTableNode", "cross_ref": "pydantic.functional_validators.WrapValidator", "kind": "Gdef"}, "_CommonField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._generate_schema._CommonField", "name": "_CommonField", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._generate_schema._CommonField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._generate_schema", "mro": ["pydantic._internal._generate_schema._CommonField", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["validation_alias", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["serialization_alias", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["serialization_exclude", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["frozen", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["metadata", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}]], "readonly_keys": [], "required_keys": ["frozen", "metadata", "schema", "serialization_alias", "serialization_exclude", "validation_alias"]}}}, "_Definitions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._generate_schema._Definitions", "name": "_Definitions", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._generate_schema._Definitions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._generate_schema", "mro": ["pydantic._internal._generate_schema._Definitions", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema._Definitions.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema._Definitions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _Definitions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "definitions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema._Definitions.definitions", "name": "definitions", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_schema_or_ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tp"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "pydantic._internal._generate_schema._Definitions.get_schema_or_ref", "name": "get_schema_or_ref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tp"], "arg_types": ["pydantic._internal._generate_schema._Definitions", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_schema_or_ref of _Definitions", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "NoneType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema._Definitions.get_schema_or_ref", "name": "get_schema_or_ref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tp"], "arg_types": ["pydantic._internal._generate_schema._Definitions", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_schema_or_ref of _Definitions", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "NoneType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "seen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema._Definitions.seen", "name": "seen", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema._Definitions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._generate_schema._Definitions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FieldNameStack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._generate_schema._FieldNameStack", "name": "_FieldNameStack", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._generate_schema._FieldNameStack", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._generate_schema", "mro": ["pydantic._internal._generate_schema._FieldNameStack", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema._FieldNameStack.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema._FieldNameStack"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _FieldNameStack", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic._internal._generate_schema._FieldNameStack.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema._FieldNameStack._stack", "name": "_stack", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema._FieldNameStack.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema._FieldNameStack"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of _FieldNameStack", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "push": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "pydantic._internal._generate_schema._FieldNameStack.push", "name": "push", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["pydantic._internal._generate_schema._FieldNameStack", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push of _FieldNameStack", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema._FieldNameStack.push", "name": "push", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["pydantic._internal._generate_schema._FieldNameStack", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push of _FieldNameStack", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema._FieldNameStack.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._generate_schema._FieldNameStack", "values": [], "variance": 0}, "slots": ["_stack"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ModelTypeStack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._generate_schema._ModelTypeStack", "name": "_ModelTypeStack", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "pydantic._internal._generate_schema._ModelTypeStack", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._generate_schema", "mro": ["pydantic._internal._generate_schema._ModelTypeStack", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema._ModelTypeStack.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema._ModelTypeStack"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ModelTypeStack", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic._internal._generate_schema._ModelTypeStack.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema._ModelTypeStack._stack", "name": "_stack", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema._ModelTypeStack.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema._ModelTypeStack"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of _ModelTypeStack", "ret_type": {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "push": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "type_obj"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "pydantic._internal._generate_schema._ModelTypeStack.push", "name": "push", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "type_obj"], "arg_types": ["pydantic._internal._generate_schema._ModelTypeStack", "builtins.type"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push of _ModelTypeStack", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema._ModelTypeStack.push", "name": "push", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "type_obj"], "arg_types": ["pydantic._internal._generate_schema._ModelTypeStack", "builtins.type"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push of _ModelTypeStack", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema._ModelTypeStack.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._generate_schema._ModelTypeStack", "values": [], "variance": 0}, "slots": ["_stack"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ParameterKind": {".class": "SymbolTableNode", "cross_ref": "inspect._ParameterKind", "kind": "Gdef"}, "_SUPPORTS_TYPEDDICT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema._SUPPORTS_TYPEDDICT", "name": "_SUPPORTS_TYPEDDICT", "type": "builtins.bool"}}, "_VALIDATOR_F_MATCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema._VALIDATOR_F_MATCH", "name": "_VALIDATOR_F_MATCH", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators.FieldValidatorModes"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "no-info"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "with-info"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generate_schema.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generate_schema.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generate_schema.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generate_schema.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generate_schema.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generate_schema.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_add_custom_serialization_from_json_encoders": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["json_encoders", "tp", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema._add_custom_serialization_from_json_encoders", "name": "_add_custom_serialization_from_json_encoders", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["json_encoders", "tp", "schema"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generate_schema.JsonEncoders"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_custom_serialization_from_json_encoders", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "_common_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["schema", "validation_alias", "serialization_alias", "serialization_exclude", "frozen", "metadata"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema._common_field", "name": "_common_field", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["schema", "validation_alias", "serialization_alias", "serialization_exclude", "frozen", "metadata"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_common_field", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generate_schema._CommonField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_core_utils": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils", "kind": "Gdef"}, "_decorators": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators", "kind": "Gdef"}, "_discriminated_union": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._discriminated_union", "kind": "Gdef"}, "_extract_get_pydantic_json_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tp", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema._extract_get_pydantic_json_schema", "name": "_extract_get_pydantic_json_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["tp", "schema"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_get_pydantic_json_schema", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._schema_generation_shared.GetJsonSchemaFunction"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_json_schema_info_from_field_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["info"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema._extract_json_schema_info_from_field_info", "name": "_extract_json_schema_info_from_field_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["info"], "arg_types": [{".class": "UnionType", "items": ["pydantic.fields.FieldInfo", "pydantic.fields.ComputedFieldInfo"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_json_schema_info_from_field_info", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonSchemaExtraCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_first_non_null": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "b"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema._get_first_non_null", "name": "_get_first_non_null", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_first_non_null", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_known_annotated_metadata": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._known_annotated_metadata", "kind": "Gdef"}, "_mode_to_validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema._mode_to_validator", "name": "_mode_to_validator", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators.FieldValidatorModes"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.functional_validators.BeforeValidator"}, {".class": "TypeType", "item": "pydantic.functional_validators.AfterValidator"}, {".class": "TypeType", "item": "pydantic.functional_validators.PlainValidator"}, {".class": "TypeType", "item": "pydantic.functional_validators.WrapValidator"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_typing_extra": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._typing_extra", "kind": "Gdef"}, "_validators_require_validate_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["validators"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema._validators_require_validate_default", "name": "_validators_require_validate_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["validators"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.ValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validators_require_validate_default", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_each_item_validators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["schema", "each_item_validators", "field_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.apply_each_item_validators", "name": "apply_each_item_validators", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["schema", "each_item_validators", "field_name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.ValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_each_item_validators", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_model_validators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["schema", "validators", "mode"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.apply_model_validators", "name": "apply_model_validators", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["schema", "validators", "mode"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "inner"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "outer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "all"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_model_validators", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_validators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["schema", "validators", "field_name"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.apply_validators", "name": "apply_validators", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["schema", "validators", "field_name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.RootValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.ValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_validators", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attrgetter": {".class": "SymbolTableNode", "cross_ref": "_operator.attrgetter", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "itertools.chain", "kind": "Gdef"}, "check_decorator_fields_exist": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["decorators", "fields"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.check_decorator_fields_exist", "name": "check_decorator_fields_exist", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["decorators", "fields"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generate_schema.AnyFieldDecorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_decorator_fields_exist", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_validator_fields_against_field_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["info", "field"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.check_validator_fields_against_field_name", "name": "check_validator_fields_against_field_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["info", "field"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generate_schema.FieldDecoratorInfo"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_validator_fields_against_field_name", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "collect_dataclass_fields": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._fields.collect_dataclass_fields", "kind": "Gdef"}, "collect_invalid_schemas": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.collect_invalid_schemas", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy.copy", "kind": "Gdef"}, "core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "deepcopy": {".class": "SymbolTableNode", "cross_ref": "copy.deepcopy", "kind": "Gdef"}, "define_expected_missing_refs": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.define_expected_missing_refs", "kind": "Gdef"}, "extract_docstrings_from_cls": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._docs_extraction.extract_docstrings_from_cls", "kind": "Gdef"}, "filter_field_decorator_info_by_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["validator_functions", "field"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.filter_field_decorator_info_by_field", "name": "filter_field_decorator_info_by_field", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["validator_functions", "field"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema.FieldDecoratorInfoType", "id": -1, "name": "FieldDecoratorInfoType", "namespace": "pydantic._internal._generate_schema.filter_field_decorator_info_by_field", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_field_decorator_info_by_field", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema.FieldDecoratorInfoType", "id": -1, "name": "FieldDecoratorInfoType", "namespace": "pydantic._internal._generate_schema.filter_field_decorator_info_by_field", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema.FieldDecoratorInfoType", "id": -1, "name": "FieldDecoratorInfoType", "namespace": "pydantic._internal._generate_schema.filter_field_decorator_info_by_field", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, "get_args": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_args", "kind": "Gdef"}, "get_attribute_from_bases": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.get_attribute_from_bases", "kind": "Gdef"}, "get_origin": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_origin", "kind": "Gdef"}, "get_ref": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.get_ref", "kind": "Gdef"}, "get_standard_typevars_map": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._generics.get_standard_typevars_map", "kind": "Gdef"}, "get_type_ref": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.get_type_ref", "kind": "Gdef"}, "has_instance_in_type": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._generics.has_instance_in_type", "kind": "Gdef"}, "import_cached_base_model": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._import_utils.import_cached_base_model", "kind": "Gdef"}, "import_cached_field_info": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._import_utils.import_cached_field_info", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "inspect_field_serializer": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.inspect_field_serializer", "kind": "Gdef"}, "inspect_model_serializer": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.inspect_model_serializer", "kind": "Gdef"}, "inspect_validator": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.inspect_validator", "kind": "Gdef"}, "is_function_with_inner_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.is_function_with_inner_schema", "kind": "Gdef"}, "is_list_like_schema_with_items_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.is_list_like_schema_with_items_schema", "kind": "Gdef"}, "is_typeddict": {".class": "SymbolTableNode", "cross_ref": "typing.is_typeddict", "kind": "Gdef"}, "lenient_issubclass": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.lenient_issubclass", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "pathlib": {".class": "SymbolTableNode", "cross_ref": "pathlib", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "recursively_defined_type_refs": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._generics.recursively_defined_type_refs", "kind": "Gdef"}, "replace_types": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._generics.replace_types", "kind": "Gdef"}, "resolve_original_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["schema", "definitions"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.resolve_original_schema", "name": "resolve_original_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["schema", "definitions"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_original_schema", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "signature": {".class": "SymbolTableNode", "cross_ref": "inspect.signature", "kind": "Gdef"}, "simplify_schema_references": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.simplify_schema_references", "kind": "Gdef"}, "smart_deepcopy": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.smart_deepcopy", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "takes_validated_data_argument": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._fields.takes_validated_data_argument", "kind": "Gdef"}, "to_jsonable_python": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.to_jsonable_python", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef"}, "update_core_metadata": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_metadata.update_core_metadata", "kind": "Gdef"}, "validate_core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.validate_core_schema", "kind": "Gdef"}, "version_short": {".class": "SymbolTableNode", "cross_ref": "pydantic.version.version_short", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "wrap_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["field_info", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic._internal._generate_schema.wrap_default", "name": "wrap_default", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["field_info", "schema"], "arg_types": ["pydantic.fields.FieldInfo", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap_default", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/_internal/_generate_schema.py"}