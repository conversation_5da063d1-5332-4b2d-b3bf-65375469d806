{"data_mtime": 1751259987, "dep_lines": [17, 5, 3, 5, 6, 7, 8, 9, 10, 11, 14, 27, 375, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 20, 10, 10, 5, 5, 10, 5, 5, 25, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._namespace_utils", "collections.abc", "__future__", "collections", "re", "sys", "types", "typing", "warnings", "functools", "typing_extensions", "pydantic", "zoneinfo", "builtins", "_typeshed", "abc", "importlib", "importlib.machinery", "pydantic._internal._model_construction", "pydantic.main"], "hash": "d7875da13c4a37b02608739d7b83a17f07d5886b", "id": "pydantic._internal._typing_extra", "ignore_all": true, "interface_hash": "7eed84cae67ce4c420b97e37a653abea82fa5568", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/_internal/_typing_extra.py", "plugin_data": null, "size": 33396, "suppressed": [], "version_id": "1.13.0"}