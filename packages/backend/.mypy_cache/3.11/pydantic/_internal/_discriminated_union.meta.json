{"data_mtime": 1751259987, "dep_lines": [8, 5, 7, 8, 15, 1, 3, 5, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 20, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic_core.core_schema", "pydantic.errors", "pydantic._internal", "pydantic.types", "__future__", "typing", "pydantic_core", "builtins", "_typeshed", "abc", "importlib", "importlib.machinery", "typing_extensions"], "hash": "e24a45828601032a4a38bda7adba96d1d7ab5fb7", "id": "pydantic._internal._discriminated_union", "ignore_all": true, "interface_hash": "b6423b0359ee1ee51358d5ff9bda68457a4cc88d", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/_internal/_discriminated_union.py", "plugin_data": null, "size": 26446, "suppressed": [], "version_id": "1.13.0"}