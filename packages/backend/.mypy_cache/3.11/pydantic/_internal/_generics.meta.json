{"data_mtime": 1751259987, "dep_lines": [15, 16, 17, 18, 15, 24, 1, 3, 4, 5, 6, 7, 8, 11, 13, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 25, 5, 10, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._typing_extra", "pydantic._internal._core_utils", "pydantic._internal._forward_ref", "pydantic._internal._utils", "pydantic._internal", "pydantic.main", "__future__", "sys", "types", "typing", "collections", "contextlib", "<PERSON><PERSON><PERSON>", "weakref", "typing_extensions", "builtins", "_collections_abc", "_typeshed", "abc", "importlib", "importlib.machinery", "pydantic._internal._model_construction"], "hash": "f133da1e5b547a76ca8f2b3d99eb662eb08af1d1", "id": "pydantic._internal._generics", "ignore_all": true, "interface_hash": "d946089d0cc5873465f5e054476ace35c784ef4e", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/_internal/_generics.py", "plugin_data": null, "size": 22746, "suppressed": [], "version_id": "1.13.0"}