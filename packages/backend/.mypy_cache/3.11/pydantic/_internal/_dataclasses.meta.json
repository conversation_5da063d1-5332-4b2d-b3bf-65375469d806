{"data_mtime": 1751259987, "dep_lines": [20, 22, 22, 23, 24, 25, 26, 27, 28, 29, 30, 11, 19, 21, 22, 35, 36, 3, 5, 6, 7, 8, 11, 17, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 20, 25, 25, 5, 10, 5, 10, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.plugin._schema_validator", "pydantic._internal._config", "pydantic._internal._decorators", "pydantic._internal._fields", "pydantic._internal._generate_schema", "pydantic._internal._generics", "pydantic._internal._mock_val_ser", "pydantic._internal._namespace_utils", "pydantic._internal._schema_generation_shared", "pydantic._internal._signature", "pydantic._internal._utils", "pydantic_core.core_schema", "pydantic.errors", "pydantic.warnings", "pydantic._internal", "pydantic.config", "pydantic.fields", "__future__", "dataclasses", "typing", "warnings", "functools", "pydantic_core", "typing_extensions", "_typeshed", "builtins", "abc", "importlib", "importlib.machinery", "pydantic._internal._repr", "pydantic.aliases", "pydantic.plugin", "pydantic.v1", "pydantic.v1.dataclasses", "pydantic_core._pydantic_core", "re"], "hash": "dd14e416632e2088422d2745624b44a357d4112c", "id": "pydantic._internal._dataclasses", "ignore_all": true, "interface_hash": "ec1bf5db4d866cda954702b08fc981ba6ecfec3e", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/_internal/_dataclasses.py", "plugin_data": null, "size": 9486, "suppressed": [], "version_id": "1.13.0"}