{"data_mtime": 1751259987, "dep_lines": [27, 32, 32, 33, 34, 35, 40, 12, 20, 28, 29, 31, 32, 9, 11, 13, 14, 15, 16, 19, 20, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 25, 10, 10, 5, 5, 5, 20, 5, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._serializers", "pydantic._internal._known_annotated_metadata", "pydantic._internal._typing_extra", "pydantic._internal._import_utils", "pydantic._internal._internal_dataclass", "pydantic._internal._schema_generation_shared", "pydantic._internal._generate_schema", "collections.abc", "pydantic_core.core_schema", "pydantic.errors", "pydantic.types", "pydantic.json_schema", "pydantic._internal", "__future__", "collections", "dataclasses", "os", "typing", "functools", "typing_extensions", "pydantic_core", "builtins", "_typeshed", "abc", "importlib", "importlib.machinery", "pydantic._internal._repr", "pydantic.annotated_handlers", "pydantic.fields"], "hash": "7e1636436ab9c8ac84afa2bf3a97b082a246615c", "id": "pydantic._internal._std_types_schema", "ignore_all": true, "interface_hash": "433ee503fbdaed6c7ca79f0135e3617a668af617", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/_internal/_std_types_schema.py", "plugin_data": null, "size": 16163, "suppressed": [], "version_id": "1.13.0"}