{"data_mtime": 1751259987, "dep_lines": [39, 39, 39, 39, 39, 39, 36, 39, 40, 41, 42, 43, 44, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 32, 34, 36, 37, 3038, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 20, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic._internal._fields", "pydantic._internal._internal_dataclass", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic._internal._validators", "pydantic_core.core_schema", "pydantic._internal", "pydantic._migration", "pydantic.annotated_handlers", "pydantic.errors", "pydantic.json_schema", "pydantic.warnings", "__future__", "base64", "dataclasses", "re", "datetime", "decimal", "enum", "pathlib", "types", "typing", "uuid", "annotated_types", "pydantic_core", "typing_extensions", "pydantic", "builtins", "_collections_abc", "_decimal", "_operator", "_typeshed", "abc", "importlib", "importlib.machinery", "os", "pydantic._internal._repr", "pydantic_core._pydantic_core"], "hash": "14e754491613eabb1cebe20be43a44488c1ccb70", "id": "pydantic.types", "ignore_all": true, "interface_hash": "873501bcea3e82bb6a05a725c7d8335a152125da", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/pydantic/types.py", "plugin_data": null, "size": 103958, "suppressed": [], "version_id": "1.13.0"}