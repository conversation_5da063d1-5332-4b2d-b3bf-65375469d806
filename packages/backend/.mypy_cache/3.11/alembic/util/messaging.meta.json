{"data_mtime": 1751259991, "dep_lines": [14, 16, 3, 14, 16, 1, 4, 5, 6, 7, 8, 12, 25, 26, 27, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 20, 20, 5, 5, 10, 10, 10, 5, 10, 10, 10, 10, 5, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.url", "alembic.util.sqla_compat", "collections.abc", "sqlalchemy.engine", "alembic.util", "__future__", "contextlib", "logging", "sys", "textwrap", "typing", "warnings", "fcntl", "termios", "struct", "builtins", "abc", "importlib", "importlib.machinery", "typing_extensions"], "hash": "d4b7c8dbc05709e8aece22c986a39214db9b92a4", "id": "alembic.util.messaging", "ignore_all": true, "interface_hash": "5ede3e02eef436cce9875a29a0957aae56409dec", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/util/messaging.py", "plugin_data": null, "size": 3250, "suppressed": [], "version_id": "1.13.0"}