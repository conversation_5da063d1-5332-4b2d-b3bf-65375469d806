{".class": "MypyFile", "_fullname": "alembic.util", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutogenerateDiffsDetected": {".class": "SymbolTableNode", "cross_ref": "alembic.util.exc.AutogenerateDiffsDetected", "kind": "Gdef"}, "CommandError": {".class": "SymbolTableNode", "cross_ref": "alembic.util.exc.CommandError", "kind": "Gdef"}, "Dispatcher": {".class": "SymbolTableNode", "cross_ref": "alembic.util.langhelpers.Dispatcher", "kind": "Gdef"}, "EMPTY_DICT": {".class": "SymbolTableNode", "cross_ref": "alembic.util.langhelpers.EMPTY_DICT", "kind": "Gdef"}, "ModuleClsProxy": {".class": "SymbolTableNode", "cross_ref": "alembic.util.langhelpers.ModuleClsProxy", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_with_legacy_names": {".class": "SymbolTableNode", "cross_ref": "alembic.util.langhelpers._with_legacy_names", "kind": "Gdef"}, "asbool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.asbool", "kind": "Gdef"}, "coerce_resource_to_filename": {".class": "SymbolTableNode", "cross_ref": "alembic.util.pyfiles.coerce_resource_to_filename", "kind": "Gdef"}, "dedupe_tuple": {".class": "SymbolTableNode", "cross_ref": "alembic.util.langhelpers.dedupe_tuple", "kind": "Gdef"}, "err": {".class": "SymbolTableNode", "cross_ref": "alembic.util.messaging.err", "kind": "Gdef"}, "format_as_comma": {".class": "SymbolTableNode", "cross_ref": "alembic.util.messaging.format_as_comma", "kind": "Gdef"}, "has_computed": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat.has_computed", "kind": "Gdef"}, "immutabledict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._py_collections.immutabledict", "kind": "Gdef"}, "load_python_file": {".class": "SymbolTableNode", "cross_ref": "alembic.util.pyfiles.load_python_file", "kind": "Gdef"}, "memoized_property": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.memoized_property", "kind": "Gdef"}, "msg": {".class": "SymbolTableNode", "cross_ref": "alembic.util.messaging.msg", "kind": "Gdef"}, "not_none": {".class": "SymbolTableNode", "cross_ref": "alembic.util.langhelpers.not_none", "kind": "Gdef"}, "obfuscate_url_pw": {".class": "SymbolTableNode", "cross_ref": "alembic.util.messaging.obfuscate_url_pw", "kind": "Gdef"}, "open_in_editor": {".class": "SymbolTableNode", "cross_ref": "alembic.util.editor.open_in_editor", "kind": "Gdef"}, "pyc_file_from_path": {".class": "SymbolTableNode", "cross_ref": "alembic.util.pyfiles.pyc_file_from_path", "kind": "Gdef"}, "rev_id": {".class": "SymbolTableNode", "cross_ref": "alembic.util.langhelpers.rev_id", "kind": "Gdef"}, "sqla_13": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat.sqla_13", "kind": "Gdef"}, "sqla_14": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat.sqla_14", "kind": "Gdef"}, "sqla_2": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat.sqla_2", "kind": "Gdef"}, "status": {".class": "SymbolTableNode", "cross_ref": "alembic.util.messaging.status", "kind": "Gdef"}, "template_to_file": {".class": "SymbolTableNode", "cross_ref": "alembic.util.pyfiles.template_to_file", "kind": "Gdef"}, "to_list": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.to_list", "kind": "Gdef"}, "to_tuple": {".class": "SymbolTableNode", "cross_ref": "alembic.util.langhelpers.to_tuple", "kind": "Gdef"}, "unique_list": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._py_collections.unique_list", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "cross_ref": "alembic.util.messaging.warn", "kind": "Gdef"}, "write_outstream": {".class": "SymbolTableNode", "cross_ref": "alembic.util.messaging.write_outstream", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/util/__init__.py"}