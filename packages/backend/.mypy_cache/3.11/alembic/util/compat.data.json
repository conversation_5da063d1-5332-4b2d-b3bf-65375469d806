{".class": "MypyFile", "_fullname": "alembic.util.compat", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ConfigParser": {".class": "SymbolTableNode", "cross_ref": "configparser.ConfigParser", "kind": "Gdef"}, "EncodedIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["io._WrappedBuffer"], "extra_attrs": null, "type_ref": "io.TextIOWrapper"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.util.compat.EncodedIO", "name": "EncodedIO", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.util.compat.EncodedIO", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "alembic.util.compat", "mro": ["alembic.util.compat.EncodedIO", "io.TextIOWrapper", "io.TextIOBase", "io.IOBase", "typing.TextIO", "typing.IO", "typing.Iterator", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable", "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.util.compat.EncodedIO.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.util.compat.EncodedIO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of EncodedIO", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.util.compat.EncodedIO.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.util.compat.EncodedIO", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EntryPoint": {".class": "SymbolTableNode", "cross_ref": "importlib.metadata.EntryPoint", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.compat.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.compat.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.compat.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.compat.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.compat.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.compat.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_metadata": {".class": "SymbolTableNode", "cross_ref": "importlib.metadata", "kind": "Gdef"}, "_resources": {".class": "SymbolTableNode", "cross_ref": "importlib.resources", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "formatannotation_fwdref": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["annotation", "base_module"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.util.compat.formatannotation_fwdref", "name": "formatannotation_fwdref", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["annotation", "base_module"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatannotation_fwdref", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "importlib_metadata": {".class": "SymbolTableNode", "cross_ref": "importlib.metadata", "kind": "Gdef"}, "importlib_metadata_get": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["group"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.util.compat.importlib_metadata_get", "name": "importlib_metadata_get", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["group"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "importlib_metadata_get", "ret_type": {".class": "Instance", "args": ["importlib.metadata.EntryPoint"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "importlib_resources": {".class": "SymbolTableNode", "cross_ref": "importlib.resources", "kind": "Gdef"}, "inspect_formatargspec": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.inspect_formatargspec", "kind": "Gdef"}, "inspect_getfullargspec": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.inspect_getfullargspec", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "is_posix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.compat.is_posix", "name": "is_posix", "type": "builtins.bool"}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "py310": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.compat.py310", "name": "py310", "type": "builtins.bool"}}, "py311": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.compat.py311", "name": "py311", "type": "builtins.bool"}}, "py39": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.compat.py39", "name": "py39", "type": "builtins.bool"}}, "read_config_parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["file_config", "file_argument"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.util.compat.read_config_parser", "name": "read_config_parser", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["file_config", "file_argument"], "arg_types": ["configparser.ConfigParser", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_config_parser", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/util/compat.py"}