{"data_mtime": 1751259991, "dep_lines": [18, 19, 6, 7, 18, 1, 3, 4, 5, 8, 9, 10, 11, 12, 1, 1, 16, 15], "dep_prios": [10, 5, 10, 10, 20, 5, 10, 5, 10, 10, 10, 10, 5, 5, 5, 30, 5, 5], "dependencies": ["alembic.util.compat", "alembic.util.exc", "importlib.machinery", "importlib.util", "alembic.util", "__future__", "atexit", "contextlib", "importlib", "os", "re", "tempfile", "types", "typing", "builtins", "abc"], "hash": "4e582605c2b966c654ade45ca80e2ca058cf66c3", "id": "alembic.util.pyfiles", "ignore_all": true, "interface_hash": "b9fd0845b6a9b6bd1117a2dbb2197ad6fff8995a", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/util/pyfiles.py", "plugin_data": null, "size": 3489, "suppressed": ["mako.template", "mako"], "version_id": "1.13.0"}