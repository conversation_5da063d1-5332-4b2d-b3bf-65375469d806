{"data_mtime": 1751259991, "dep_lines": [21, 18, 41, 44, 3, 5, 6, 7, 8, 9, 41, 1, 1, 1, 1, 1, 49, 50], "dep_prios": [5, 5, 10, 5, 5, 5, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 10, 5], "dependencies": ["sqlalchemy.util.compat", "sqlalchemy.util", "importlib.resources", "importlib.metadata", "__future__", "configparser", "io", "os", "sys", "typing", "importlib", "builtins", "_typeshed", "abc", "importlib.machinery", "types"], "hash": "16a55a300f493a1871e0a811ad9aecb63f6af4e7", "id": "alembic.util.compat", "ignore_all": true, "interface_hash": "1cb09b31637759fe5f7827b7a1369de94e630834", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/util/compat.py", "plugin_data": null, "size": 2594, "suppressed": ["importlib_resources", "importlib_metadata"], "version_id": "1.13.0"}