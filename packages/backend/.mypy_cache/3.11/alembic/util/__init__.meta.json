{"data_mtime": 1751259991, "dep_lines": [1, 2, 4, 17, 24, 28, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["alembic.util.editor", "alembic.util.exc", "alembic.util.langhelpers", "alembic.util.messaging", "alembic.util.pyfiles", "alembic.util.sqla_compat", "builtins", "abc", "importlib", "importlib.machinery", "typing"], "hash": "3d0e5d14560a47d9c719677149d824603e7b7180", "id": "alembic.util", "ignore_all": true, "interface_hash": "b656d4f74bbfc411fa2d43f93875918fcc761580", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/util/__init__.py", "plugin_data": null, "size": 1688, "suppressed": [], "version_id": "1.13.0"}