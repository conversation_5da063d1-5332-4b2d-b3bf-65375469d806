{"data_mtime": 1751259990, "dep_lines": [27, 31, 32, 33, 48, 50, 51, 53, 55, 84, 24, 25, 26, 27, 172, 4, 6, 7, 8, 22, 39, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 25, 25, 25, 25, 25, 5, 5, 10, 10, 20, 5, 5, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.url", "sqlalchemy.sql.visitors", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.engine.reflection", "sqlalchemy.sql.compiler", "sqlalchemy.sql.dml", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.naming", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.types", "sqlalchemy.engine", "sqlalchemy.util", "__future__", "contextlib", "re", "typing", "sqlalchemy", "typing_extensions", "builtins", "abc", "enum", "importlib", "importlib.machinery", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.util", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._selectable_constructors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "007b7caccb9f6996db082581f6601f38756c9501", "id": "alembic.util.sqla_compat", "ignore_all": true, "interface_hash": "b719080d3dcaae7225570dcfac79d52e5a52e1f2", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/util/sqla_compat.py", "plugin_data": null, "size": 19526, "suppressed": [], "version_id": "1.13.0"}