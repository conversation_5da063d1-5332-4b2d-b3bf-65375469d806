{".class": "MypyFile", "_fullname": "alembic.script.revision", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "CycleDetected": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.script.revision.RevisionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.script.revision.CycleDetected", "name": "CycleDetected", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.script.revision.CycleDetected", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.script.revision", "mro": ["alembic.script.revision.CycleDetected", "alembic.script.revision.RevisionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "revisions"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.CycleDetected.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "revisions"], "arg_types": ["alembic.script.revision.CycleDetected", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CycleDetected", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "alembic.script.revision.CycleDetected.kind", "name": "kind", "type": "builtins.str"}}, "revisions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.script.revision.CycleDetected.revisions", "name": "revisions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision.CycleDetected.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.script.revision.CycleDetected", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DependencyCycleDetected": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.script.revision.CycleDetected"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.script.revision.DependencyCycleDetected", "name": "DependencyCycleDetected", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.script.revision.DependencyCycleDetected", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.script.revision", "mro": ["alembic.script.revision.DependencyCycleDetected", "alembic.script.revision.CycleDetected", "alembic.script.revision.RevisionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "revisions"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.DependencyCycleDetected.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "revisions"], "arg_types": ["alembic.script.revision.DependencyCycleDetected", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DependencyCycleDetected", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.script.revision.DependencyCycleDetected.kind", "name": "kind", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision.DependencyCycleDetected.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.script.revision.DependencyCycleDetected", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DependencyLoopDetected": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.script.revision.DependencyCycleDetected", "alembic.script.revision.LoopDetected"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.script.revision.DependencyLoopDetected", "name": "DependencyLoopDetected", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.script.revision.DependencyLoopDetected", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.script.revision", "mro": ["alembic.script.revision.DependencyLoopDetected", "alembic.script.revision.DependencyCycleDetected", "alembic.script.revision.LoopDetected", "alembic.script.revision.CycleDetected", "alembic.script.revision.RevisionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "revision"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.DependencyLoopDetected.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "revision"], "arg_types": ["alembic.script.revision.DependencyLoopDetected", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DependencyLoopDetected", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.script.revision.DependencyLoopDetected.kind", "name": "kind", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision.DependencyLoopDetected.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.script.revision.DependencyLoopDetected", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Deque": {".class": "SymbolTableNode", "cross_ref": "typing.Deque", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FrozenSet": {".class": "SymbolTableNode", "cross_ref": "typing.FrozenSet", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "LoopDetected": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.script.revision.CycleDetected"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.script.revision.LoopDetected", "name": "LoopDetected", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.script.revision.LoopDetected", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.script.revision", "mro": ["alembic.script.revision.LoopDetected", "alembic.script.revision.CycleDetected", "alembic.script.revision.RevisionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "revision"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.LoopDetected.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "revision"], "arg_types": ["alembic.script.revision.LoopDetected", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LoopDetected", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.script.revision.LoopDetected.kind", "name": "kind", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision.LoopDetected.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.script.revision.LoopDetected", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultipleHeads": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.script.revision.RevisionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.script.revision.MultipleHeads", "name": "MultipleHeads", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.script.revision.MultipleHeads", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.script.revision", "mro": ["alembic.script.revision.MultipleHeads", "alembic.script.revision.RevisionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "heads", "argument"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.MultipleHeads.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "heads", "argument"], "arg_types": ["alembic.script.revision.MultipleHeads", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MultipleHeads", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "argument": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.script.revision.MultipleHeads.argument", "name": "argument", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.script.revision.MultipleHeads.heads", "name": "heads", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision.MultipleHeads.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.script.revision.MultipleHeads", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef"}, "RangeNotAncestorError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.script.revision.RevisionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.script.revision.RangeNotAncestorError", "name": "RangeNotAncestorError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.script.revision.RangeNotAncestorError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.script.revision", "mro": ["alembic.script.revision.RangeNotAncestorError", "alembic.script.revision.RevisionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lower", "upper"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RangeNotAncestorError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "lower", "upper"], "arg_types": ["alembic.script.revision.RangeNotAncestorError", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}, {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RangeNotAncestorError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lower": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.script.revision.RangeNotAncestorError.lower", "name": "lower", "type": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}}}, "upper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.script.revision.RangeNotAncestorError.upper", "name": "upper", "type": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision.RangeNotAncestorError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.script.revision.RangeNotAncestorError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResolutionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.script.revision.RevisionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.script.revision.ResolutionError", "name": "ResolutionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.script.revision.ResolutionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.script.revision", "mro": ["alembic.script.revision.ResolutionError", "alembic.script.revision.RevisionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "message", "argument"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.ResolutionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "message", "argument"], "arg_types": ["alembic.script.revision.ResolutionError", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResolutionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "argument": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.script.revision.ResolutionError.argument", "name": "argument", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision.ResolutionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.script.revision.ResolutionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Revision": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.script.revision.Revision", "name": "Revision", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.script.revision.Revision", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.script.revision", "mro": ["alembic.script.revision.Revision", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "revision", "down_revision", "dependencies", "branch_labels"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.Revision.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "revision", "down_revision", "dependencies", "branch_labels"], "arg_types": ["alembic.script.revision.Revision", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Revision", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.Revision.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Revision", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_all_down_revisions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.script.revision.Revision._all_down_revisions", "name": "_all_down_revisions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_all_down_revisions of Revision", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.Revision._all_down_revisions", "name": "_all_down_revisions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_all_down_revisions of Revision", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_all_nextrev": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "alembic.script.revision.Revision._all_nextrev", "name": "_all_nextrev", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "_is_real_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.script.revision.Revision._is_real_base", "name": "_is_real_base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_real_base of Revision", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.Revision._is_real_base", "name": "_is_real_base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_real_base of Revision", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_is_real_branch_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.script.revision.Revision._is_real_branch_point", "name": "_is_real_branch_point", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_real_branch_point of Revision", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.Revision._is_real_branch_point", "name": "_is_real_branch_point", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_real_branch_point of Revision", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_is_real_head": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.script.revision.Revision._is_real_head", "name": "_is_real_head", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_real_head of Revision", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.Revision._is_real_head", "name": "_is_real_head", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_real_head of Revision", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_normalized_down_revisions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.script.revision.Revision._normalized_down_revisions", "name": "_normalized_down_revisions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalized_down_revisions of Revision", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.Revision._normalized_down_revisions", "name": "_normalized_down_revisions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalized_down_revisions of Revision", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_normalized_resolved_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.script.revision.Revision._normalized_resolved_dependencies", "name": "_normalized_resolved_dependencies", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_orig_branch_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.script.revision.Revision._orig_branch_labels", "name": "_orig_branch_labels", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_resolved_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.script.revision.Revision._resolved_dependencies", "name": "_resolved_dependencies", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_versioned_down_revisions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.script.revision.Revision._versioned_down_revisions", "name": "_versioned_down_revisions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_versioned_down_revisions of Revision", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.Revision._versioned_down_revisions", "name": "_versioned_down_revisions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_versioned_down_revisions of Revision", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "add_nextrev": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "revision"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.Revision.add_nextrev", "name": "add_nextrev", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "revision"], "arg_types": ["alembic.script.revision.Revision", "alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_nextrev of Revision", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "branch_labels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "alembic.script.revision.Revision.branch_labels", "name": "branch_labels", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "alembic.script.revision.Revision.dependencies", "name": "dependencies", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevIdType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "down_revision": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "alembic.script.revision.Revision.down_revision", "name": "down_revision", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevIdType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "is_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.script.revision.Revision.is_base", "name": "is_base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_base of Revision", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.Revision.is_base", "name": "is_base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_base of Revision", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_branch_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.script.revision.Revision.is_branch_point", "name": "is_branch_point", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_branch_point of Revision", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.Revision.is_branch_point", "name": "is_branch_point", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_branch_point of Revision", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_head": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.script.revision.Revision.is_head", "name": "is_head", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_head of Revision", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.Revision.is_head", "name": "is_head", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_head of Revision", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_merge_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.script.revision.Revision.is_merge_point", "name": "is_merge_point", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_merge_point of Revision", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.Revision.is_merge_point", "name": "is_merge_point", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_merge_point of Revision", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nextrev": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "alembic.script.revision.Revision.nextrev", "name": "nextrev", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "revision": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "alembic.script.revision.Revision.revision", "name": "revision", "type": "builtins.str"}}, "verify_rev_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "revision"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.script.revision.Revision.verify_rev_id", "name": "verify_rev_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "revision"], "arg_types": [{".class": "TypeType", "item": "alembic.script.revision.Revision"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_rev_id of Revision", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.Revision.verify_rev_id", "name": "verify_rev_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "revision"], "arg_types": [{".class": "TypeType", "item": "alembic.script.revision.Revision"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_rev_id of Revision", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision.Revision.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.script.revision.Revision", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RevisionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.script.revision.RevisionError", "name": "RevisionError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.script.revision.RevisionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.script.revision", "mro": ["alembic.script.revision.RevisionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision.RevisionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.script.revision.RevisionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RevisionMap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.script.revision.RevisionMap", "name": "RevisionMap", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.script.revision.RevisionMap", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.script.revision", "mro": ["alembic.script.revision.RevisionMap", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "generator"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "generator"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RevisionMap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_branches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "revisions", "map_"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._add_branches", "name": "_add_branches", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "revisions", "map_"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionMapType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_branches of RevisionMap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_depends_on": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "revisions", "map_"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._add_depends_on", "name": "_add_depends_on", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "revisions", "map_"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionMapType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_depends_on of RevisionMap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_collect_downgrade_revisions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "upper", "lower", "inclusive", "implicit_base", "assert_relative_length"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._collect_downgrade_revisions", "name": "_collect_downgrade_revisions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "upper", "lower", "inclusive", "implicit_base", "assert_relative_length"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}, {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_collect_downgrade_revisions of RevisionMap", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrBase"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_collect_upgrade_revisions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "upper", "lower", "inclusive", "implicit_base", "assert_relative_length"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._collect_upgrade_revisions", "name": "_collect_upgrade_revisions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "upper", "lower", "inclusive", "implicit_base", "assert_relative_length"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}, {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_collect_upgrade_revisions of RevisionMap", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_detect_cycles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "rev_map", "heads", "bases", "_real_heads", "_real_bases"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._detect_cycles", "name": "_detect_cycles", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "rev_map", "heads", "bases", "_real_heads", "_real_bases"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._InterimRevisionMapType"}, {".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_detect_cycles of RevisionMap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_filter_into_branch_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "targets"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._filter_into_branch_heads", "name": "_filter_into_branch_heads", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "targets"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrBase"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_filter_into_branch_heads of RevisionMap", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrBase"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.script.revision.RevisionMap._generator", "name": "_generator", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_all_current": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "id_"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._get_all_current", "name": "_get_all_current", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "id_"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_all_current of RevisionMap", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrBase"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_ancestor_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "targets", "map_", "check", "include_dependencies"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._get_ancestor_nodes", "name": "_get_ancestor_nodes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "targets", "map_", "check", "include_dependencies"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrBase"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionMapType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_ancestor_nodes of RevisionMap", "ret_type": {".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_base_revisions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "identifier"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._get_base_revisions", "name": "_get_base_revisions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "identifier"], "arg_types": ["alembic.script.revision.RevisionMap", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_base_revisions of RevisionMap", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_descendant_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "targets", "map_", "check", "omit_immediate_dependencies", "include_dependencies"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._get_descendant_nodes", "name": "_get_descendant_nodes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "targets", "map_", "check", "omit_immediate_dependencies", "include_dependencies"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrBase"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionMapType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_descendant_nodes of RevisionMap", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iterate_related_revisions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "fn", "targets", "map_", "check"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._iterate_related_revisions", "name": "_iterate_related_revisions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "fn", "targets", "map_", "check"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["alembic.script.revision.Revision"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrBase"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionMapType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_iterate_related_revisions of RevisionMap", "ret_type": {".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_map_branch_labels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "revisions", "map_"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._map_branch_labels", "name": "_map_branch_labels", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "revisions", "map_"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionMapType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_map_branch_labels of RevisionMap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_normalize_depends_on": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "revisions", "map_"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._normalize_depends_on", "name": "_normalize_depends_on", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "revisions", "map_"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionMapType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_depends_on of RevisionMap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_downgrade_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "current_revisions", "target", "assert_relative_length"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._parse_downgrade_target", "name": "_parse_downgrade_target", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "current_revisions", "target", "assert_relative_length"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}, {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_downgrade_target of RevisionMap", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrBase"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_upgrade_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "current_revisions", "target", "assert_relative_length"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._parse_upgrade_target", "name": "_parse_upgrade_target", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "current_revisions", "target", "assert_relative_length"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}, {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_upgrade_target of RevisionMap", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrBase"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_real_bases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.script.revision.RevisionMap._real_bases", "name": "_real_bases", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.RevisionMap"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_real_bases of RevisionMap", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.RevisionMap._real_bases", "name": "_real_bases", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_real_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.script.revision.RevisionMap._real_heads", "name": "_real_heads", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.RevisionMap"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_real_heads of RevisionMap", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.RevisionMap._real_heads", "name": "_real_heads", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_resolve_branch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "branch_label"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._resolve_branch", "name": "_resolve_branch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "branch_label"], "arg_types": ["alembic.script.revision.RevisionMap", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_branch of RevisionMap", "ret_type": {".class": "UnionType", "items": ["alembic.script.revision.Revision", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_revision_number": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "id_"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._resolve_revision_number", "name": "_resolve_revision_number", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "id_"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_revision_number of RevisionMap", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_revision_for_ident": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "resolved_id", "check_branch"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._revision_for_ident", "name": "_revision_for_ident", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "resolved_id", "check_branch"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "UnionType", "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_revision_for_ident of RevisionMap", "ret_type": {".class": "UnionType", "items": ["alembic.script.revision.Revision", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_revision_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.script.revision.RevisionMap._revision_map", "name": "_revision_map", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.RevisionMap"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_revision_map of RevisionMap", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionMapType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.RevisionMap._revision_map", "name": "_revision_map", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["alembic.script.revision.Revision", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_shares_lineage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "target", "test_against_revs", "include_dependencies"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._shares_lineage", "name": "_shares_lineage", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "target", "test_against_revs", "include_dependencies"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrStr"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrStr"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_shares_lineage of RevisionMap", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_topological_sort": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "revisions", "heads"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._topological_sort", "name": "_topological_sort", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "revisions", "heads"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_topological_sort of RevisionMap", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_walk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "start", "steps", "branch_label", "no_overwalk"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap._walk", "name": "_walk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "start", "steps", "branch_label", "no_overwalk"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "UnionType", "items": ["builtins.str", "alembic.script.revision.Revision", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_walk of RevisionMap", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrBase"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_revision": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "revision", "_replace"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap.add_revision", "name": "add_revision", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "revision", "_replace"], "arg_types": ["alembic.script.revision.RevisionMap", "alembic.script.revision.Revision", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_revision of RevisionMap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.script.revision.RevisionMap.bases", "name": "bases", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.RevisionMap"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bases of RevisionMap", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.RevisionMap.bases", "name": "bases", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "filter_for_lineage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "targets", "check_against", "include_dependencies"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap.filter_for_lineage", "name": "filter_for_lineage", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "targets", "check_against", "include_dependencies"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._TR", "id": -1, "name": "_TR", "namespace": "alembic.script.revision.RevisionMap.filter_for_lineage", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrStr"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_for_lineage of RevisionMap", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._TR", "id": -1, "name": "_TR", "namespace": "alembic.script.revision.RevisionMap.filter_for_lineage", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrStr"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._TR", "id": -1, "name": "_TR", "namespace": "alembic.script.revision.RevisionMap.filter_for_lineage", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrStr"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, "get_current_head": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "branch_label"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap.get_current_head", "name": "get_current_head", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "branch_label"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_current_head of RevisionMap", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_revision": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "id_"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap.get_revision", "name": "get_revision", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "id_"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_revision of RevisionMap", "ret_type": {".class": "UnionType", "items": ["alembic.script.revision.Revision", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_revisions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "id_"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap.get_revisions", "name": "get_revisions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "id_"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_revisions of RevisionMap", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrBase"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.script.revision.RevisionMap.heads", "name": "heads", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.script.revision.RevisionMap"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "heads of RevisionMap", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.script.revision.RevisionMap.heads", "name": "heads", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "iterate_revisions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "upper", "lower", "implicit_base", "inclusive", "assert_relative_length", "select_for_downgrade"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.RevisionMap.iterate_revisions", "name": "iterate_revisions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "upper", "lower", "implicit_base", "inclusive", "assert_relative_length", "select_for_downgrade"], "arg_types": ["alembic.script.revision.RevisionMap", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}, {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iterate_revisions of RevisionMap", "ret_type": {".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision.RevisionMap.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.script.revision.RevisionMap", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_CollectRevisionsProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.script.revision._CollectRevisionsProtocol", "name": "_CollectRevisionsProtocol", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "alembic.script.revision._CollectRevisionsProtocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "alembic.script.revision", "mro": ["alembic.script.revision._CollectRevisionsProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "upper", "lower", "inclusive", "implicit_base", "assert_relative_length"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "alembic.script.revision._CollectRevisionsProtocol.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "upper", "lower", "inclusive", "implicit_base", "assert_relative_length"], "arg_types": ["alembic.script.revision._CollectRevisionsProtocol", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}, {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionIdentifierType"}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _CollectRevisionsProtocol", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrBase"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._CollectRevisionsProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.script.revision._CollectRevisionsProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetRevArg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "alembic.script.revision._GetRevArg", "line": 34, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}}}, "_InterimRevisionMapType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "alembic.script.revision._InterimRevisionMapType", "line": 42, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", "alembic.script.revision.Revision"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_RevIdType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "alembic.script.revision._RevIdType", "line": 33, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}}}, "_RevisionIdentifierType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "alembic.script.revision._RevisionIdentifierType", "line": 39, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_RevisionMapType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "alembic.script.revision._RevisionMapType", "line": 43, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["alembic.script.revision.Revision", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_RevisionOrBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "alembic.script.revision._RevisionOrBase", "line": 41, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["alembic.script.revision.Revision", {".class": "LiteralType", "fallback": "builtins.str", "value": "base"}], "uses_pep604_syntax": false}}}, "_RevisionOrStr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "alembic.script.revision._RevisionOrStr", "line": 40, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["alembic.script.revision.Revision", "builtins.str"], "uses_pep604_syntax": false}}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_TR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._TR", "name": "_TR", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevisionOrStr"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.script.revision.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.script.revision.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.script.revision.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.script.revision.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.script.revision.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.script.revision.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_relative_destination": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.script.revision._relative_destination", "name": "_relative_destination", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_revision_illegal_chars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.script.revision._revision_illegal_chars", "name": "_revision_illegal_chars", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "is_revision": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rev"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.script.revision.is_revision", "name": "is_revision", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rev"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_revision", "ret_type": "alembic.script.revision.Revision", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "not_none": {".class": "SymbolTableNode", "cross_ref": "alembic.util.langhelpers.not_none", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "sqlautil": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "tuple_rev_as_scalar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "alembic.script.revision.tuple_rev_as_scalar", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rev"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "alembic.script.revision.tuple_rev_as_scalar", "name": "tuple_rev_as_scalar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rev"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_rev_as_scalar", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rev"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "alembic.script.revision.tuple_rev_as_scalar", "name": "tuple_rev_as_scalar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rev"], "arg_types": [{".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_rev_as_scalar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.script.revision.tuple_rev_as_scalar", "name": "tuple_rev_as_scalar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rev"], "arg_types": [{".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_rev_as_scalar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rev"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "alembic.script.revision.tuple_rev_as_scalar", "name": "tuple_rev_as_scalar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rev"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_rev_as_scalar", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.script.revision.tuple_rev_as_scalar", "name": "tuple_rev_as_scalar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rev"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_rev_as_scalar", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["rev"], "arg_types": [{".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_rev_as_scalar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rev"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_rev_as_scalar", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.script.revision._T", "id": -1, "name": "_T", "namespace": "alembic.script.revision.tuple_rev_as_scalar#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "util": {".class": "SymbolTableNode", "cross_ref": "alembic.util", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/script/revision.py"}