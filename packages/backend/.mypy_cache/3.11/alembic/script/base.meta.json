{"data_mtime": 1751259991, "dep_lines": [22, 23, 25, 26, 22, 24, 25, 33, 1, 3, 4, 5, 6, 7, 8, 9, 10, 24, 40, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 43], "dep_prios": [10, 10, 10, 10, 20, 5, 20, 25, 5, 5, 10, 10, 10, 10, 10, 5, 5, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["alembic.script.revision", "alembic.script.write_hooks", "alembic.runtime.migration", "alembic.util.compat", "alembic.script", "alembic.util", "alembic.runtime", "alembic.config", "__future__", "contextlib", "datetime", "os", "re", "shutil", "sys", "types", "typing", "alembic", "zoneinfo", "builtins", "abc", "alembic.util.exc", "alembic.util.langhelpers", "importlib", "importlib.machinery", "posixpath", "sqlalchemy", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "c28485bf2eb5af91f9228b511b5f0a88e3116625", "id": "alembic.script.base", "ignore_all": true, "interface_hash": "67a6707a923317bee651cd3a8d0284b37a4964d3", "mtime": 1751257053, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/script/base.py", "plugin_data": null, "size": 37800, "suppressed": ["backports.zoneinfo"], "version_id": "1.13.0"}