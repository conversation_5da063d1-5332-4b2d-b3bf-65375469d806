{"data_mtime": 1751259991, "dep_lines": [25, 27, 1, 3, 4, 5, 25, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 10, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util", "alembic.util", "__future__", "collections", "re", "typing", "sqlalchemy", "alembic", "builtins", "_collections_abc", "_typeshed", "abc", "alembic.util.langhelpers", "alembic.util.messaging", "importlib", "importlib.machinery", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "952bd960aea4aab663a031b4664663a4317d2df7", "id": "alembic.script.revision", "ignore_all": true, "interface_hash": "379bc15c8d2e6ad21b91d8023771daa6cb56a5ed", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/script/revision.py", "plugin_data": null, "size": 62306, "suppressed": [], "version_id": "1.13.0"}