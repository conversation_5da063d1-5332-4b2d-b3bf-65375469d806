{"data_mtime": 1751259991, "dep_lines": [18, 17, 4, 6, 7, 8, 9, 17, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 10, 10, 5, 20, 5, 30, 30, 30], "dependencies": ["alembic.util.compat", "alembic.util", "__future__", "shlex", "subprocess", "sys", "typing", "alembic", "builtins", "abc", "importlib", "importlib.machinery"], "hash": "b2d503e872b25a0a53c0f8285f87d0ab7fc9084f", "id": "alembic.script.write_hooks", "ignore_all": true, "interface_hash": "77ca74e53398dd733b21a96064a25f66d2849036", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/script/write_hooks.py", "plugin_data": null, "size": 5036, "suppressed": [], "version_id": "1.13.0"}