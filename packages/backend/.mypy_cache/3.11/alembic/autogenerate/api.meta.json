{"data_mtime": 1751259991, "dep_lines": [16, 17, 19, 20, 29, 37, 41, 42, 44, 16, 18, 19, 26, 33, 1, 3, 4, 14, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 25, 25, 25, 25, 25, 20, 10, 20, 25, 25, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["alembic.autogenerate.compare", "alembic.autogenerate.render", "alembic.operations.ops", "alembic.util.sqla_compat", "sqlalchemy.sql.schema", "alembic.runtime.environment", "alembic.runtime.migration", "alembic.script.base", "alembic.script.revision", "alembic.autogenerate", "alembic.util", "alembic.operations", "sqlalchemy.engine", "alembic.config", "__future__", "contextlib", "typing", "sqlalchemy", "alembic", "builtins", "abc", "alembic.runtime", "alembic.script", "alembic.util.exc", "enum", "importlib", "importlib.machinery", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.reflection", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "66615089f8e42bd6bb61de53fd04f5f3b57b6b32", "id": "alembic.autogenerate.api", "ignore_all": true, "interface_hash": "82e207d6e36b36d677a323c6dcf9cb970bc39bea", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/autogenerate/api.py", "plugin_data": null, "size": 22173, "suppressed": [], "version_id": "1.13.0"}