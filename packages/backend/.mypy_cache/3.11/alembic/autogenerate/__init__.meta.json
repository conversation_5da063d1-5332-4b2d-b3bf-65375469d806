{"data_mtime": 1751259991, "dep_lines": [1, 6, 8, 10, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["alembic.autogenerate.api", "alembic.autogenerate.compare", "alembic.autogenerate.render", "alembic.autogenerate.rewriter", "builtins", "abc", "importlib", "importlib.machinery", "typing"], "hash": "ce56b974fa4b28e36b67c52bc7ca113107dd239e", "id": "alembic.autogenerate", "ignore_all": true, "interface_hash": "398e56805d6daa547865069960d4fa9bb1b75c0c", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/autogenerate/__init__.py", "plugin_data": null, "size": 543, "suppressed": [], "version_id": "1.13.0"}