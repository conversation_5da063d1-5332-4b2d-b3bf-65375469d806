{"data_mtime": 1751259991, "dep_lines": [21, 25, 26, 31, 34, 44, 45, 47, 18, 19, 20, 24, 25, 48, 4, 6, 7, 8, 18, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 17], "dep_prios": [5, 10, 10, 25, 25, 25, 25, 25, 10, 10, 10, 10, 20, 25, 5, 5, 10, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["sqlalchemy.sql.elements", "alembic.operations.ops", "alembic.util.sqla_compat", "sqlalchemy.sql.base", "sqlalchemy.sql.schema", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "alembic.autogenerate.api", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.types", "alembic.util", "alembic.operations", "alembic.config", "__future__", "io", "re", "typing", "sqlalchemy", "alembic", "builtins", "abc", "alembic.util.langhelpers", "enum", "importlib", "importlib.machinery", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "e4bc7dfbc41a9176869da1a735db00c34cd9136d", "id": "alembic.autogenerate.render", "ignore_all": true, "interface_hash": "a95e0ffa37213989e39029e0568fdae013f3467c", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/autogenerate/render.py", "plugin_data": null, "size": 35481, "suppressed": ["mako.pygen"], "version_id": "1.13.0"}