{"data_mtime": 1751259991, "dep_lines": [26, 27, 33, 35, 36, 41, 42, 47, 48, 21, 23, 25, 26, 30, 32, 35, 4, 6, 7, 8, 9, 21, 32, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 10, 25, 25, 25, 25, 10, 10, 10, 20, 5, 10, 20, 5, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.expression", "sqlalchemy.sql.schema", "alembic.ddl._autogen", "alembic.operations.ops", "alembic.util.sqla_compat", "sqlalchemy.engine.reflection", "sqlalchemy.sql.elements", "alembic.autogenerate.api", "alembic.ddl.impl", "sqlalchemy.event", "sqlalchemy.schema", "sqlalchemy.types", "sqlalchemy.sql", "sqlalchemy.util", "alembic.util", "alembic.operations", "__future__", "contextlib", "logging", "re", "typing", "sqlalchemy", "alembic", "builtins", "abc", "alembic.ddl", "alembic.util.langhelpers", "importlib", "importlib.machinery", "sqlalchemy.engine", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "de9a348c814a45d01c12f2dd4b0ebac1c8832426", "id": "alembic.autogenerate.compare", "ignore_all": true, "interface_hash": "29464c5229952a2e5bacd6f7964f8a560d336cdf", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/autogenerate/compare.py", "plugin_data": null, "size": 44944, "suppressed": [], "version_id": "1.13.0"}