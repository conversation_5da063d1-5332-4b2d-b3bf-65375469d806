{".class": "MypyFile", "_fullname": "alembic.autogenerate.api", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutogenContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.autogenerate.api.AutogenContext", "name": "AutogenContext", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.autogenerate.api.AutogenContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.autogenerate.api", "mro": ["alembic.autogenerate.api.AutogenContext", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "migration_context", "metadata", "opts", "autogenerate"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.api.AutogenContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "migration_context", "metadata", "opts", "autogenerate"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.runtime.migration.MigrationContext", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AutogenContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_has_batch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "alembic.autogenerate.api.AutogenContext._has_batch", "name": "_has_batch", "type": "builtins.bool"}}, "_name_filters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.api.AutogenContext._name_filters", "name": "_name_filters", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_object_filters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.api.AutogenContext._object_filters", "name": "_object_filters", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_within_batch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_generator", "is_decorated"], "fullname": "alembic.autogenerate.api.AutogenContext._within_batch", "name": "_within_batch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_within_batch of AutogenContext", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.autogenerate.api.AutogenContext._within_batch", "name": "_within_batch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_within_batch of AutogenContext", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "alembic.autogenerate.api.AutogenContext.connection", "name": "connection", "type": {".class": "UnionType", "items": ["sqlalchemy.engine.base.Connection", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "dialect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "alembic.autogenerate.api.AutogenContext.dialect", "name": "dialect", "type": {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "imports": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "alembic.autogenerate.api.AutogenContext.imports", "name": "imports", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "inspector": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.api.AutogenContext.inspector", "name": "inspector", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inspector of AutogenContext", "ret_type": "sqlalchemy.engine.reflection.Inspector", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.autogenerate.api.AutogenContext.inspector", "name": "inspector", "type": {".class": "Instance", "args": ["sqlalchemy.engine.reflection.Inspector"], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "alembic.autogenerate.api.AutogenContext.metadata", "name": "metadata", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "migration_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "alembic.autogenerate.api.AutogenContext.migration_context", "name": "migration_context", "type": "alembic.runtime.migration.MigrationContext"}}, "opts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "alembic.autogenerate.api.AutogenContext.opts", "name": "opts", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "run_filters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.autogenerate.api.AutogenContext.run_filters", "name": "run_filters", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "object_", "name", "type_", "reflected", "compare_to"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "sqlalchemy.sql.schema.SchemaItem", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintName"}, {".class": "TypeAliasType", "args": [], "type_ref": "alembic.runtime.environment.NameFilterType"}, "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.SchemaItem", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_name_filters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "type_", "parent_names"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.api.AutogenContext.run_name_filters", "name": "run_name_filters", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "type_", "parent_names"], "arg_types": ["alembic.autogenerate.api.AutogenContext", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "alembic.runtime.environment.NameFilterType"}, {".class": "TypeAliasType", "args": [], "type_ref": "alembic.runtime.environment.NameFilterParentNames"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_name_filters of AutogenContext", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_object_filters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "object_", "name", "type_", "reflected", "compare_to"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.api.AutogenContext.run_object_filters", "name": "run_object_filters", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "object_", "name", "type_", "reflected", "compare_to"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "sqlalchemy.sql.schema.SchemaItem", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintName"}, {".class": "TypeAliasType", "args": [], "type_ref": "alembic.runtime.environment.NameFilterType"}, "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.SchemaItem", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_object_filters of AutogenContext", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sorted_tables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.api.AutogenContext.sorted_tables", "name": "sorted_tables", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sorted_tables of AutogenContext", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.Table"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.autogenerate.api.AutogenContext.sorted_tables", "name": "sorted_tables", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["sqlalchemy.sql.schema.Table"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "table_key_to_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.api.AutogenContext.table_key_to_table", "name": "table_key_to_table", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "table_key_to_table of AutogenContext", "ret_type": {".class": "Instance", "args": ["builtins.str", "sqlalchemy.sql.schema.Table"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.autogenerate.api.AutogenContext.table_key_to_table", "name": "table_key_to_table", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "sqlalchemy.sql.schema.Table"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.autogenerate.api.AutogenContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.autogenerate.api.AutogenContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Config": {".class": "SymbolTableNode", "cross_ref": "alembic.config.Config", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DowngradeOps": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.DowngradeOps", "kind": "Gdef"}, "Inspector": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.Inspector", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.MetaData", "kind": "Gdef"}, "MigrationContext": {".class": "SymbolTableNode", "cross_ref": "alembic.runtime.migration.MigrationContext", "kind": "Gdef"}, "MigrationScript": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.MigrationScript", "kind": "Gdef"}, "NameFilterParentNames": {".class": "SymbolTableNode", "cross_ref": "alembic.runtime.environment.NameFilterParentNames", "kind": "Gdef"}, "NameFilterType": {".class": "SymbolTableNode", "cross_ref": "alembic.runtime.environment.NameFilterType", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ProcessRevisionDirectiveFn": {".class": "SymbolTableNode", "cross_ref": "alembic.runtime.environment.ProcessRevisionDirectiveFn", "kind": "Gdef"}, "RenderItemFn": {".class": "SymbolTableNode", "cross_ref": "alembic.runtime.environment.RenderItemFn", "kind": "Gdef"}, "RevisionContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.autogenerate.api.RevisionContext", "name": "RevisionContext", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.autogenerate.api.RevisionContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.autogenerate.api", "mro": ["alembic.autogenerate.api.RevisionContext", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "config", "script_directory", "command_args", "process_revision_directives"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.api.RevisionContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "config", "script_directory", "command_args", "process_revision_directives"], "arg_types": ["alembic.autogenerate.api.RevisionContext", "alembic.config.Config", "alembic.script.base.ScriptDirectory", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.runtime.environment.ProcessRevisionDirectiveFn"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RevisionContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_default_revision": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.api.RevisionContext._default_revision", "name": "_default_revision", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.autogenerate.api.RevisionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_default_revision of RevisionContext", "ret_type": "alembic.operations.ops.MigrationScript", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_last_autogen_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "alembic.autogenerate.api.RevisionContext._last_autogen_context", "name": "_last_autogen_context", "type": "alembic.autogenerate.api.AutogenContext"}}, "_run_environment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "rev", "migration_context", "autogenerate"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.api.RevisionContext._run_environment", "name": "_run_environment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "rev", "migration_context", "autogenerate"], "arg_types": ["alembic.autogenerate.api.RevisionContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, "alembic.runtime.migration.MigrationContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_run_environment of RevisionContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_to_script": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "migration_script"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.api.RevisionContext._to_script", "name": "_to_script", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "migration_script"], "arg_types": ["alembic.autogenerate.api.RevisionContext", "alembic.operations.ops.MigrationScript"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_to_script of RevisionContext", "ret_type": {".class": "UnionType", "items": ["alembic.script.base.Script", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "command_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.api.RevisionContext.command_args", "name": "command_args", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.api.RevisionContext.config", "name": "config", "type": "alembic.config.Config"}}, "generate_scripts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.api.RevisionContext.generate_scripts", "name": "generate_scripts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.autogenerate.api.RevisionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_scripts of RevisionContext", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["alembic.script.base.Script", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generated_revisions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.autogenerate.api.RevisionContext.generated_revisions", "name": "generated_revisions", "type": {".class": "Instance", "args": ["alembic.operations.ops.MigrationScript"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "process_revision_directives": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.autogenerate.api.RevisionContext.process_revision_directives", "name": "process_revision_directives", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.runtime.environment.ProcessRevisionDirectiveFn"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "run_autogenerate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "rev", "migration_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.api.RevisionContext.run_autogenerate", "name": "run_autogenerate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "rev", "migration_context"], "arg_types": ["alembic.autogenerate.api.RevisionContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, "alembic.runtime.migration.MigrationContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_autogenerate of RevisionContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_no_autogenerate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "rev", "migration_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.api.RevisionContext.run_no_autogenerate", "name": "run_no_autogenerate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "rev", "migration_context"], "arg_types": ["alembic.autogenerate.api.RevisionContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, "alembic.runtime.migration.MigrationContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_no_autogenerate of RevisionContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "script_directory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.api.RevisionContext.script_directory", "name": "script_directory", "type": "alembic.script.base.ScriptDirectory"}}, "template_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.api.RevisionContext.template_args", "name": "template_args", "type": {".class": "Instance", "args": ["builtins.str", "alembic.config.Config"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.autogenerate.api.RevisionContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.autogenerate.api.RevisionContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SchemaItem": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.SchemaItem", "kind": "Gdef"}, "Script": {".class": "SymbolTableNode", "cross_ref": "alembic.script.base.Script", "kind": "Gdef"}, "ScriptDirectory": {".class": "SymbolTableNode", "cross_ref": "alembic.script.base.ScriptDirectory", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UpgradeOps": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.UpgradeOps", "kind": "Gdef"}, "_GetRevArg": {".class": "SymbolTableNode", "cross_ref": "alembic.script.revision._GetRevArg", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.api.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.api.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.api.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.api.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.api.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.api.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "_render_migration_diffs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["context", "template_args"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.api._render_migration_diffs", "name": "_render_migration_diffs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["context", "template_args"], "arg_types": ["alembic.runtime.migration.MigrationContext", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_migration_diffs", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "compare": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.compare", "kind": "Gdef"}, "compare_metadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["context", "metadata"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.api.compare_metadata", "name": "compare_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["context", "metadata"], "arg_types": ["alembic.runtime.migration.MigrationContext", "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compare_metadata", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection.inspect", "kind": "Gdef"}, "ops": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops", "kind": "Gdef"}, "produce_migrations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["context", "metadata"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.api.produce_migrations", "name": "produce_migrations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["context", "metadata"], "arg_types": ["alembic.runtime.migration.MigrationContext", "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "produce_migrations", "ret_type": "alembic.operations.ops.MigrationScript", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.render", "kind": "Gdef"}, "render_python_code": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["up_or_down_op", "sqlalchemy_module_prefix", "alembic_module_prefix", "render_as_batch", "imports", "render_item", "migration_context", "user_module_prefix"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.api.render_python_code", "name": "render_python_code", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["up_or_down_op", "sqlalchemy_module_prefix", "alembic_module_prefix", "render_as_batch", "imports", "render_item", "migration_context", "user_module_prefix"], "arg_types": [{".class": "UnionType", "items": ["alembic.operations.ops.UpgradeOps", "alembic.operations.ops.DowngradeOps"], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.runtime.environment.RenderItemFn"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["alembic.runtime.migration.MigrationContext", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_python_code", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sqla_compat": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "alembic.util", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/autogenerate/api.py"}