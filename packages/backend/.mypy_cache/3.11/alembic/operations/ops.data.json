{".class": "MypyFile", "_fullname": "alembic.operations.ops", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AddColumnOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.AlterTableOp"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.AddColumnOp", "name": "AddColumnOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.AddColumnOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.AddColumnOp", "alembic.operations.ops.AlterTableOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 4], "arg_names": ["self", "table_name", "column", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.AddColumnOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 4], "arg_names": ["self", "table_name", "column", "schema", "kw"], "arg_types": ["alembic.operations.ops.AddColumnOp", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AddColumnOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["cls", "operations", "table_name", "column", "schema"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.AddColumnOp.add_column", "name": "add_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["cls", "operations", "table_name", "column", "schema"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AddColumnOp"}, "alembic.operations.base.Operations", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_column of AddColumnOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.AddColumnOp.add_column", "name": "add_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["cls", "operations", "table_name", "column", "schema"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AddColumnOp"}, "alembic.operations.base.Operations", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_column of AddColumnOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "batch_add_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["cls", "operations", "column", "insert_before", "insert_after"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.AddColumnOp.batch_add_column", "name": "batch_add_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["cls", "operations", "column", "insert_before", "insert_after"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AddColumnOp"}, "alembic.operations.base.BatchOperations", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_add_column of AddColumnOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.AddColumnOp.batch_add_column", "name": "batch_add_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["cls", "operations", "column", "insert_before", "insert_after"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AddColumnOp"}, "alembic.operations.base.BatchOperations", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_add_column of AddColumnOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.AddColumnOp.column", "name": "column", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "from_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "col"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.AddColumnOp.from_column", "name": "from_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "col"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AddColumnOp"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_column of AddColumnOp", "ret_type": "alembic.operations.ops.AddColumnOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.AddColumnOp.from_column", "name": "from_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "col"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AddColumnOp"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_column of AddColumnOp", "ret_type": "alembic.operations.ops.AddColumnOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_column_and_tablename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "schema", "tname", "col"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.AddColumnOp.from_column_and_tablename", "name": "from_column_and_tablename", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "schema", "tname", "col"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AddColumnOp"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_column_and_tablename of AddColumnOp", "ret_type": "alembic.operations.ops.AddColumnOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.AddColumnOp.from_column_and_tablename", "name": "from_column_and_tablename", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "schema", "tname", "col"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AddColumnOp"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_column_and_tablename of AddColumnOp", "ret_type": "alembic.operations.ops.AddColumnOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.AddColumnOp.kw", "name": "kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.AddColumnOp.reverse", "name": "reverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.AddColumnOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse of AddColumnOp", "ret_type": "alembic.operations.ops.DropColumnOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.AddColumnOp.to_column", "name": "to_column", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.AddColumnOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_column of AddColumnOp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_diff_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.AddColumnOp.to_diff_tuple", "name": "to_diff_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.AddColumnOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_diff_tuple of AddColumnOp", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.AddColumnOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.AddColumnOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AddConstraintOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["to_constraint", 1]], "alt_promote": null, "bases": ["alembic.operations.ops.MigrateOperation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.AddConstraintOp", "name": "AddConstraintOp", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract"], "fullname": "alembic.operations.ops.AddConstraintOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.AddConstraintOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "add_constraint_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.operations.ops.AddConstraintOp.add_constraint_ops", "name": "add_constraint_ops", "type": "alembic.util.langhelpers.Dispatcher"}}, "constraint_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.operations.ops.AddConstraintOp.constraint_type", "name": "constraint_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.AddConstraintOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "constraint_type of AddConstraintOp", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.AddConstraintOp.constraint_type", "name": "constraint_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.AddConstraintOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "constraint_type of AddConstraintOp", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.AddConstraintOp.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AddConstraintOp"}, "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of AddConstraintOp", "ret_type": "alembic.operations.ops.AddConstraintOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.AddConstraintOp.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AddConstraintOp"}, "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of AddConstraintOp", "ret_type": "alembic.operations.ops.AddConstraintOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "register_add_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "type_"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.AddConstraintOp.register_add_constraint", "name": "register_add_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "type_"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AddConstraintOp"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_add_constraint of AddConstraintOp", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops._AC", "id": -1, "name": "_AC", "namespace": "", "upper_bound": "alembic.operations.ops.AddConstraintOp", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops._AC", "id": -1, "name": "_AC", "namespace": "", "upper_bound": "alembic.operations.ops.AddConstraintOp", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops._AC", "id": -1, "name": "_AC", "namespace": "", "upper_bound": "alembic.operations.ops.AddConstraintOp", "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.AddConstraintOp.register_add_constraint", "name": "register_add_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "type_"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AddConstraintOp"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_add_constraint of AddConstraintOp", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops._AC", "id": -1, "name": "_AC", "namespace": "", "upper_bound": "alembic.operations.ops.AddConstraintOp", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops._AC", "id": -1, "name": "_AC", "namespace": "", "upper_bound": "alembic.operations.ops.AddConstraintOp", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops._AC", "id": -1, "name": "_AC", "namespace": "", "upper_bound": "alembic.operations.ops.AddConstraintOp", "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.AddConstraintOp.reverse", "name": "reverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.AddConstraintOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse of AddConstraintOp", "ret_type": "alembic.operations.ops.DropConstraintOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "dataclass_transform_spec": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "alembic.operations.ops.AddConstraintOp.to_constraint", "name": "to_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "arg_types": ["alembic.operations.ops.AddConstraintOp", {".class": "UnionType", "items": ["alembic.runtime.migration.MigrationContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_constraint of AddConstraintOp", "ret_type": "sqlalchemy.sql.schema.Constraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.AddConstraintOp.to_constraint", "name": "to_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "arg_types": ["alembic.operations.ops.AddConstraintOp", {".class": "UnionType", "items": ["alembic.runtime.migration.MigrationContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_constraint of AddConstraintOp", "ret_type": "sqlalchemy.sql.schema.Constraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "to_diff_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.AddConstraintOp.to_diff_tuple", "name": "to_diff_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.AddConstraintOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_diff_tuple of AddConstraintOp", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.schema.Constraint"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.AddConstraintOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.AddConstraintOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AlterColumnOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.AlterTableOp"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.AlterColumnOp", "name": "AlterColumnOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.AlterColumnOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.AlterColumnOp", "alembic.operations.ops.AlterTableOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "table_name", "column_name", "schema", "existing_type", "existing_server_default", "existing_nullable", "existing_comment", "modify_nullable", "modify_comment", "modify_server_default", "modify_name", "modify_type", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.AlterColumnOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "table_name", "column_name", "schema", "existing_type", "existing_server_default", "existing_nullable", "existing_comment", "modify_nullable", "modify_comment", "modify_server_default", "modify_name", "modify_type", "kw"], "arg_types": ["alembic.operations.ops.AlterColumnOp", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AlterColumnOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "alter_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "operations", "table_name", "column_name", "nullable", "comment", "server_default", "new_column_name", "type_", "existing_type", "existing_server_default", "existing_nullable", "existing_comment", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.AlterColumnOp.alter_column", "name": "alter_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "operations", "table_name", "column_name", "nullable", "comment", "server_default", "new_column_name", "type_", "existing_type", "existing_server_default", "existing_nullable", "existing_comment", "schema", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AlterColumnOp"}, "alembic.operations.base.Operations", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.bool", "sqlalchemy.sql.schema.Identity", "sqlalchemy.sql.schema.Computed", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alter_column of AlterColumnOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.AlterColumnOp.alter_column", "name": "alter_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "operations", "table_name", "column_name", "nullable", "comment", "server_default", "new_column_name", "type_", "existing_type", "existing_server_default", "existing_nullable", "existing_comment", "schema", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AlterColumnOp"}, "alembic.operations.base.Operations", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.bool", "sqlalchemy.sql.schema.Identity", "sqlalchemy.sql.schema.Computed", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alter_column of AlterColumnOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "batch_alter_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "operations", "column_name", "nullable", "comment", "server_default", "new_column_name", "type_", "existing_type", "existing_server_default", "existing_nullable", "existing_comment", "insert_before", "insert_after", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.AlterColumnOp.batch_alter_column", "name": "batch_alter_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "operations", "column_name", "nullable", "comment", "server_default", "new_column_name", "type_", "existing_type", "existing_server_default", "existing_nullable", "existing_comment", "insert_before", "insert_after", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AlterColumnOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.bool", "sqlalchemy.sql.schema.Identity", "sqlalchemy.sql.schema.Computed", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_alter_column of AlterColumnOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.AlterColumnOp.batch_alter_column", "name": "batch_alter_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "operations", "column_name", "nullable", "comment", "server_default", "new_column_name", "type_", "existing_type", "existing_server_default", "existing_nullable", "existing_comment", "insert_before", "insert_after", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.AlterColumnOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.bool", "sqlalchemy.sql.schema.Identity", "sqlalchemy.sql.schema.Computed", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_alter_column of AlterColumnOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "column_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.AlterColumnOp.column_name", "name": "column_name", "type": "builtins.str"}}, "existing_comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.AlterColumnOp.existing_comment", "name": "existing_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "existing_nullable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.AlterColumnOp.existing_nullable", "name": "existing_nullable", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "existing_server_default": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.AlterColumnOp.existing_server_default", "name": "existing_server_default", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "existing_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.AlterColumnOp.existing_type", "name": "existing_type", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "has_changes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.AlterColumnOp.has_changes", "name": "has_changes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.AlterColumnOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_changes of AlterColumnOp", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.AlterColumnOp.kw", "name": "kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "modify_comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.AlterColumnOp.modify_comment", "name": "modify_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "modify_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.AlterColumnOp.modify_name", "name": "modify_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "modify_nullable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.AlterColumnOp.modify_nullable", "name": "modify_nullable", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "modify_server_default": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.AlterColumnOp.modify_server_default", "name": "modify_server_default", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "modify_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.AlterColumnOp.modify_type", "name": "modify_type", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.AlterColumnOp.reverse", "name": "reverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.AlterColumnOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse of AlterColumnOp", "ret_type": "alembic.operations.ops.AlterColumnOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_diff_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.AlterColumnOp.to_diff_tuple", "name": "to_diff_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.AlterColumnOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_diff_tuple of AlterColumnOp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.AlterColumnOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.AlterColumnOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AlterTableOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.MigrateOperation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.AlterTableOp", "name": "AlterTableOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.AlterTableOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.AlterTableOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "table_name", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.AlterTableOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "table_name", "schema"], "arg_types": ["alembic.operations.ops.AlterTableOp", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AlterTableOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.AlterTableOp.schema", "name": "schema", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.AlterTableOp.table_name", "name": "table_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.AlterTableOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.AlterTableOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BatchOperations": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.base.BatchOperations", "kind": "Gdef"}, "BulkInsertOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.MigrateOperation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.BulkInsertOp", "name": "BulkInsertOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.BulkInsertOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.BulkInsertOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "table", "rows", "multiinsert"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.BulkInsertOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "table", "rows", "multiinsert"], "arg_types": ["alembic.operations.ops.BulkInsertOp", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", "sqlalchemy.sql.selectable.TableClause"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BulkInsertOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bulk_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["cls", "operations", "table", "rows", "multiinsert"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.BulkInsertOp.bulk_insert", "name": "bulk_insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["cls", "operations", "table", "rows", "multiinsert"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.BulkInsertOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", "sqlalchemy.sql.selectable.TableClause"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bulk_insert of BulkInsertOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.BulkInsertOp.bulk_insert", "name": "bulk_insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["cls", "operations", "table", "rows", "multiinsert"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.BulkInsertOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", "sqlalchemy.sql.selectable.TableClause"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bulk_insert of BulkInsertOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "multiinsert": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.BulkInsertOp.multiinsert", "name": "multiinsert", "type": "builtins.bool"}}, "rows": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.BulkInsertOp.rows", "name": "rows", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.BulkInsertOp.table", "name": "table", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", "sqlalchemy.sql.selectable.TableClause"], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.BulkInsertOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.BulkInsertOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CheckConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.CheckConstraint", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "Computed": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Computed", "kind": "Gdef"}, "Constraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Constraint", "kind": "Gdef"}, "CreateCheckConstraintOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.AddConstraintOp"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.CreateCheckConstraintOp", "name": "CreateCheckConstraintOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.CreateCheckConstraintOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.CreateCheckConstraintOp", "alembic.operations.ops.AddConstraintOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["self", "constraint_name", "table_name", "condition", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateCheckConstraintOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["self", "constraint_name", "table_name", "condition", "schema", "kw"], "arg_types": ["alembic.operations.ops.CreateCheckConstraintOp", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintNameDefined"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.elements.TextClause", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CreateCheckConstraintOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_create_check_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "operations", "constraint_name", "condition", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateCheckConstraintOp.batch_create_check_constraint", "name": "batch_create_check_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "operations", "constraint_name", "condition", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateCheckConstraintOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.elements.TextClause"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_create_check_constraint of CreateCheckConstraintOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateCheckConstraintOp.batch_create_check_constraint", "name": "batch_create_check_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "operations", "constraint_name", "condition", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateCheckConstraintOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.elements.TextClause"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_create_check_constraint of CreateCheckConstraintOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "condition": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateCheckConstraintOp.condition", "name": "condition", "type": {".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.elements.TextClause", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "uses_pep604_syntax": false}}}, "constraint_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateCheckConstraintOp.constraint_name", "name": "constraint_name", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintNameDefined"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "constraint_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.operations.ops.CreateCheckConstraintOp.constraint_type", "name": "constraint_type", "type": "builtins.str"}}, "create_check_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5, 4], "arg_names": ["cls", "operations", "constraint_name", "table_name", "condition", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateCheckConstraintOp.create_check_constraint", "name": "create_check_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5, 4], "arg_names": ["cls", "operations", "constraint_name", "table_name", "condition", "schema", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateCheckConstraintOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.elements.TextClause"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_check_constraint of CreateCheckConstraintOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateCheckConstraintOp.create_check_constraint", "name": "create_check_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5, 4], "arg_names": ["cls", "operations", "constraint_name", "table_name", "condition", "schema", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateCheckConstraintOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.elements.TextClause"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_check_constraint of CreateCheckConstraintOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateCheckConstraintOp.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateCheckConstraintOp"}, "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of CreateCheckConstraintOp", "ret_type": "alembic.operations.ops.CreateCheckConstraintOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateCheckConstraintOp.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateCheckConstraintOp"}, "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of CreateCheckConstraintOp", "ret_type": "alembic.operations.ops.CreateCheckConstraintOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateCheckConstraintOp.kw", "name": "kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateCheckConstraintOp.schema", "name": "schema", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateCheckConstraintOp.table_name", "name": "table_name", "type": "builtins.str"}}, "to_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateCheckConstraintOp.to_constraint", "name": "to_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "arg_types": ["alembic.operations.ops.CreateCheckConstraintOp", {".class": "UnionType", "items": ["alembic.runtime.migration.MigrationContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_constraint of CreateCheckConstraintOp", "ret_type": "sqlalchemy.sql.schema.CheckConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.CreateCheckConstraintOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.CreateCheckConstraintOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CreateForeignKeyOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.AddConstraintOp"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.CreateForeignKeyOp", "name": "CreateForeignKeyOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.CreateForeignKeyOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.CreateForeignKeyOp", "alembic.operations.ops.AddConstraintOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "constraint_name", "source_table", "referent_table", "local_cols", "remote_cols", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateForeignKeyOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "constraint_name", "source_table", "referent_table", "local_cols", "remote_cols", "kw"], "arg_types": ["alembic.operations.ops.CreateForeignKeyOp", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintNameDefined"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CreateForeignKeyOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_create_foreign_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "operations", "constraint_name", "referent_table", "local_cols", "remote_cols", "referent_schema", "onupdate", "ondelete", "deferrable", "initially", "match", "dialect_kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateForeignKeyOp.batch_create_foreign_key", "name": "batch_create_foreign_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "operations", "constraint_name", "referent_table", "local_cols", "remote_cols", "referent_schema", "onupdate", "ondelete", "deferrable", "initially", "match", "dialect_kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateForeignKeyOp"}, "alembic.operations.base.BatchOperations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_create_foreign_key of CreateForeignKeyOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateForeignKeyOp.batch_create_foreign_key", "name": "batch_create_foreign_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "operations", "constraint_name", "referent_table", "local_cols", "remote_cols", "referent_schema", "onupdate", "ondelete", "deferrable", "initially", "match", "dialect_kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateForeignKeyOp"}, "alembic.operations.base.BatchOperations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_create_foreign_key of CreateForeignKeyOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "constraint_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateForeignKeyOp.constraint_name", "name": "constraint_name", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintNameDefined"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "constraint_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.operations.ops.CreateForeignKeyOp.constraint_type", "name": "constraint_type", "type": "builtins.str"}}, "create_foreign_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "operations", "constraint_name", "source_table", "referent_table", "local_cols", "remote_cols", "onupdate", "ondelete", "deferrable", "initially", "match", "source_schema", "referent_schema", "dialect_kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateForeignKeyOp.create_foreign_key", "name": "create_foreign_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "operations", "constraint_name", "source_table", "referent_table", "local_cols", "remote_cols", "onupdate", "ondelete", "deferrable", "initially", "match", "source_schema", "referent_schema", "dialect_kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateForeignKeyOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_foreign_key of CreateForeignKeyOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateForeignKeyOp.create_foreign_key", "name": "create_foreign_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "operations", "constraint_name", "source_table", "referent_table", "local_cols", "remote_cols", "onupdate", "ondelete", "deferrable", "initially", "match", "source_schema", "referent_schema", "dialect_kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateForeignKeyOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_foreign_key of CreateForeignKeyOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateForeignKeyOp.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateForeignKeyOp"}, "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of CreateForeignKeyOp", "ret_type": "alembic.operations.ops.CreateForeignKeyOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateForeignKeyOp.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateForeignKeyOp"}, "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of CreateForeignKeyOp", "ret_type": "alembic.operations.ops.CreateForeignKeyOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateForeignKeyOp.kw", "name": "kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "local_cols": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateForeignKeyOp.local_cols", "name": "local_cols", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "referent_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateForeignKeyOp.referent_table", "name": "referent_table", "type": "builtins.str"}}, "remote_cols": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateForeignKeyOp.remote_cols", "name": "remote_cols", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "source_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateForeignKeyOp.source_table", "name": "source_table", "type": "builtins.str"}}, "to_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateForeignKeyOp.to_constraint", "name": "to_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "arg_types": ["alembic.operations.ops.CreateForeignKeyOp", {".class": "UnionType", "items": ["alembic.runtime.migration.MigrationContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_constraint of CreateForeignKeyOp", "ret_type": "sqlalchemy.sql.schema.ForeignKeyConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_diff_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateForeignKeyOp.to_diff_tuple", "name": "to_diff_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.CreateForeignKeyOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_diff_tuple of CreateForeignKeyOp", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.schema.ForeignKeyConstraint"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.CreateForeignKeyOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.CreateForeignKeyOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CreateIndexOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.MigrateOperation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.CreateIndexOp", "name": "CreateIndexOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.CreateIndexOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.CreateIndexOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 4], "arg_names": ["self", "index_name", "table_name", "columns", "schema", "unique", "if_not_exists", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateIndexOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 4], "arg_names": ["self", "index_name", "table_name", "columns", "schema", "unique", "if_not_exists", "kw"], "arg_types": ["alembic.operations.ops.CreateIndexOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.elements.TextClause", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CreateIndexOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_create_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "operations", "index_name", "columns", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateIndexOp.batch_create_index", "name": "batch_create_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "operations", "index_name", "columns", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateIndexOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_create_index of CreateIndexOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateIndexOp.batch_create_index", "name": "batch_create_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "operations", "index_name", "columns", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateIndexOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_create_index of CreateIndexOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateIndexOp.columns", "name": "columns", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.elements.TextClause", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "create_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5, 5, 5, 4], "arg_names": ["cls", "operations", "index_name", "table_name", "columns", "schema", "unique", "if_not_exists", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateIndexOp.create_index", "name": "create_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5, 5, 5, 4], "arg_names": ["cls", "operations", "index_name", "table_name", "columns", "schema", "unique", "if_not_exists", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateIndexOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.elements.TextClause", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.functions.Function"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_index of CreateIndexOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateIndexOp.create_index", "name": "create_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5, 5, 5, 4], "arg_names": ["cls", "operations", "index_name", "table_name", "columns", "schema", "unique", "if_not_exists", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateIndexOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.elements.TextClause", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.functions.Function"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_index of CreateIndexOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "index"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateIndexOp.from_index", "name": "from_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "index"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateIndexOp"}, "sqlalchemy.sql.schema.Index"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_index of CreateIndexOp", "ret_type": "alembic.operations.ops.CreateIndexOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateIndexOp.from_index", "name": "from_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "index"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateIndexOp"}, "sqlalchemy.sql.schema.Index"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_index of CreateIndexOp", "ret_type": "alembic.operations.ops.CreateIndexOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "if_not_exists": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateIndexOp.if_not_exists", "name": "if_not_exists", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "index_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateIndexOp.index_name", "name": "index_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateIndexOp.kw", "name": "kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateIndexOp.reverse", "name": "reverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.CreateIndexOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse of CreateIndexOp", "ret_type": "alembic.operations.ops.DropIndexOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateIndexOp.schema", "name": "schema", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateIndexOp.table_name", "name": "table_name", "type": "builtins.str"}}, "to_diff_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateIndexOp.to_diff_tuple", "name": "to_diff_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.CreateIndexOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_diff_tuple of CreateIndexOp", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.schema.Index"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateIndexOp.to_index", "name": "to_index", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "arg_types": ["alembic.operations.ops.CreateIndexOp", {".class": "UnionType", "items": ["alembic.runtime.migration.MigrationContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_index of CreateIndexOp", "ret_type": "sqlalchemy.sql.schema.Index", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unique": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateIndexOp.unique", "name": "unique", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.CreateIndexOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.CreateIndexOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CreatePrimaryKeyOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.AddConstraintOp"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.CreatePrimaryKeyOp", "name": "CreatePrimaryKeyOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.CreatePrimaryKeyOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.CreatePrimaryKeyOp", "alembic.operations.ops.AddConstraintOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["self", "constraint_name", "table_name", "columns", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreatePrimaryKeyOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["self", "constraint_name", "table_name", "columns", "schema", "kw"], "arg_types": ["alembic.operations.ops.CreatePrimaryKeyOp", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintNameDefined"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CreatePrimaryKeyOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_create_primary_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "operations", "constraint_name", "columns"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreatePrimaryKeyOp.batch_create_primary_key", "name": "batch_create_primary_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "operations", "constraint_name", "columns"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreatePrimaryKeyOp"}, "alembic.operations.base.BatchOperations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_create_primary_key of CreatePrimaryKeyOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreatePrimaryKeyOp.batch_create_primary_key", "name": "batch_create_primary_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "operations", "constraint_name", "columns"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreatePrimaryKeyOp"}, "alembic.operations.base.BatchOperations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_create_primary_key of CreatePrimaryKeyOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreatePrimaryKeyOp.columns", "name": "columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "constraint_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreatePrimaryKeyOp.constraint_name", "name": "constraint_name", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintNameDefined"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "constraint_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.operations.ops.CreatePrimaryKeyOp.constraint_type", "name": "constraint_type", "type": "builtins.str"}}, "create_primary_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5], "arg_names": ["cls", "operations", "constraint_name", "table_name", "columns", "schema"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreatePrimaryKeyOp.create_primary_key", "name": "create_primary_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5], "arg_names": ["cls", "operations", "constraint_name", "table_name", "columns", "schema"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreatePrimaryKeyOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_primary_key of CreatePrimaryKeyOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreatePrimaryKeyOp.create_primary_key", "name": "create_primary_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5], "arg_names": ["cls", "operations", "constraint_name", "table_name", "columns", "schema"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreatePrimaryKeyOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_primary_key of CreatePrimaryKeyOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreatePrimaryKeyOp.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreatePrimaryKeyOp"}, "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of CreatePrimaryKeyOp", "ret_type": "alembic.operations.ops.CreatePrimaryKeyOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreatePrimaryKeyOp.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreatePrimaryKeyOp"}, "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of CreatePrimaryKeyOp", "ret_type": "alembic.operations.ops.CreatePrimaryKeyOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreatePrimaryKeyOp.kw", "name": "kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreatePrimaryKeyOp.schema", "name": "schema", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreatePrimaryKeyOp.table_name", "name": "table_name", "type": "builtins.str"}}, "to_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreatePrimaryKeyOp.to_constraint", "name": "to_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "arg_types": ["alembic.operations.ops.CreatePrimaryKeyOp", {".class": "UnionType", "items": ["alembic.runtime.migration.MigrationContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_constraint of CreatePrimaryKeyOp", "ret_type": "sqlalchemy.sql.schema.PrimaryKeyConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.CreatePrimaryKeyOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.CreatePrimaryKeyOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CreateTableCommentOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.AlterTableOp"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.CreateTableCommentOp", "name": "CreateTableCommentOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.CreateTableCommentOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.CreateTableCommentOp", "alembic.operations.ops.AlterTableOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "table_name", "comment", "schema", "existing_comment"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateTableCommentOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "table_name", "comment", "schema", "existing_comment"], "arg_types": ["alembic.operations.ops.CreateTableCommentOp", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CreateTableCommentOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_create_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["cls", "operations", "comment", "existing_comment"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateTableCommentOp.batch_create_table_comment", "name": "batch_create_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["cls", "operations", "comment", "existing_comment"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateTableCommentOp"}, "alembic.operations.base.BatchOperations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_create_table_comment of CreateTableCommentOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateTableCommentOp.batch_create_table_comment", "name": "batch_create_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["cls", "operations", "comment", "existing_comment"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateTableCommentOp"}, "alembic.operations.base.BatchOperations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_create_table_comment of CreateTableCommentOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateTableCommentOp.comment", "name": "comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "create_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5], "arg_names": ["cls", "operations", "table_name", "comment", "existing_comment", "schema"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateTableCommentOp.create_table_comment", "name": "create_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5], "arg_names": ["cls", "operations", "table_name", "comment", "existing_comment", "schema"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateTableCommentOp"}, "alembic.operations.base.Operations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_table_comment of CreateTableCommentOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateTableCommentOp.create_table_comment", "name": "create_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5], "arg_names": ["cls", "operations", "table_name", "comment", "existing_comment", "schema"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateTableCommentOp"}, "alembic.operations.base.Operations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_table_comment of CreateTableCommentOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "existing_comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateTableCommentOp.existing_comment", "name": "existing_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateTableCommentOp.reverse", "name": "reverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.CreateTableCommentOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse of CreateTableCommentOp", "ret_type": {".class": "UnionType", "items": ["alembic.operations.ops.CreateTableCommentOp", "alembic.operations.ops.DropTableCommentOp"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_diff_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateTableCommentOp.to_diff_tuple", "name": "to_diff_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.CreateTableCommentOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_diff_tuple of CreateTableCommentOp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateTableCommentOp.to_table", "name": "to_table", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "arg_types": ["alembic.operations.ops.CreateTableCommentOp", {".class": "UnionType", "items": ["alembic.runtime.migration.MigrationContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_table of CreateTableCommentOp", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.CreateTableCommentOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.CreateTableCommentOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CreateTableOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.MigrateOperation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.CreateTableOp", "name": "CreateTableOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.CreateTableOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.CreateTableOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 4], "arg_names": ["self", "table_name", "columns", "schema", "if_not_exists", "_namespace_metadata", "_constraints_included", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateTableOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 4], "arg_names": ["self", "table_name", "columns", "schema", "if_not_exists", "_namespace_metadata", "_constraints_included", "kw"], "arg_types": ["alembic.operations.ops.CreateTableOp", "builtins.str", {".class": "Instance", "args": ["sqlalchemy.sql.schema.SchemaItem"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CreateTableOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_constraints_included": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateTableOp._constraints_included", "name": "_constraints_included", "type": "builtins.bool"}}, "_namespace_metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateTableOp._namespace_metadata", "name": "_namespace_metadata", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateTableOp.columns", "name": "columns", "type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.SchemaItem"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateTableOp.comment", "name": "comment", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "create_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 5, 4], "arg_names": ["cls", "operations", "table_name", "columns", "if_not_exists", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateTableOp.create_table", "name": "create_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 5, 4], "arg_names": ["cls", "operations", "table_name", "columns", "if_not_exists", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateTableOp"}, "alembic.operations.base.Operations", "builtins.str", "sqlalchemy.sql.schema.SchemaItem", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_table of CreateTableOp", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateTableOp.create_table", "name": "create_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 5, 4], "arg_names": ["cls", "operations", "table_name", "columns", "if_not_exists", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateTableOp"}, "alembic.operations.base.Operations", "builtins.str", "sqlalchemy.sql.schema.SchemaItem", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_table of CreateTableOp", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["cls", "table", "_namespace_metadata"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateTableOp.from_table", "name": "from_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["cls", "table", "_namespace_metadata"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateTableOp"}, "sqlalchemy.sql.schema.Table", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_table of CreateTableOp", "ret_type": "alembic.operations.ops.CreateTableOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateTableOp.from_table", "name": "from_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["cls", "table", "_namespace_metadata"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateTableOp"}, "sqlalchemy.sql.schema.Table", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_table of CreateTableOp", "ret_type": "alembic.operations.ops.CreateTableOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "if_not_exists": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateTableOp.if_not_exists", "name": "if_not_exists", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateTableOp.kw", "name": "kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "prefixes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateTableOp.prefixes", "name": "prefixes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateTableOp.reverse", "name": "reverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.CreateTableOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse of CreateTableOp", "ret_type": "alembic.operations.ops.DropTableOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateTableOp.schema", "name": "schema", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateTableOp.table_name", "name": "table_name", "type": "builtins.str"}}, "to_diff_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateTableOp.to_diff_tuple", "name": "to_diff_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.CreateTableOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_diff_tuple of CreateTableOp", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.schema.Table"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateTableOp.to_table", "name": "to_table", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "arg_types": ["alembic.operations.ops.CreateTableOp", {".class": "UnionType", "items": ["alembic.runtime.migration.MigrationContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_table of CreateTableOp", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.CreateTableOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.CreateTableOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CreateUniqueConstraintOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.AddConstraintOp"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.CreateUniqueConstraintOp", "name": "CreateUniqueConstraintOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.CreateUniqueConstraintOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.CreateUniqueConstraintOp", "alembic.operations.ops.AddConstraintOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["self", "constraint_name", "table_name", "columns", "schema", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateUniqueConstraintOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["self", "constraint_name", "table_name", "columns", "schema", "kw"], "arg_types": ["alembic.operations.ops.CreateUniqueConstraintOp", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintNameDefined"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CreateUniqueConstraintOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_create_unique_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "operations", "constraint_name", "columns", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateUniqueConstraintOp.batch_create_unique_constraint", "name": "batch_create_unique_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "operations", "constraint_name", "columns", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateUniqueConstraintOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_create_unique_constraint of CreateUniqueConstraintOp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateUniqueConstraintOp.batch_create_unique_constraint", "name": "batch_create_unique_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "operations", "constraint_name", "columns", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateUniqueConstraintOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_create_unique_constraint of CreateUniqueConstraintOp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateUniqueConstraintOp.columns", "name": "columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "constraint_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateUniqueConstraintOp.constraint_name", "name": "constraint_name", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintNameDefined"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "constraint_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.operations.ops.CreateUniqueConstraintOp.constraint_type", "name": "constraint_type", "type": "builtins.str"}}, "create_unique_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5, 4], "arg_names": ["cls", "operations", "constraint_name", "table_name", "columns", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateUniqueConstraintOp.create_unique_constraint", "name": "create_unique_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5, 4], "arg_names": ["cls", "operations", "constraint_name", "table_name", "columns", "schema", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateUniqueConstraintOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unique_constraint of CreateUniqueConstraintOp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateUniqueConstraintOp.create_unique_constraint", "name": "create_unique_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5, 4], "arg_names": ["cls", "operations", "constraint_name", "table_name", "columns", "schema", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateUniqueConstraintOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unique_constraint of CreateUniqueConstraintOp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.CreateUniqueConstraintOp.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateUniqueConstraintOp"}, "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of CreateUniqueConstraintOp", "ret_type": "alembic.operations.ops.CreateUniqueConstraintOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.CreateUniqueConstraintOp.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.CreateUniqueConstraintOp"}, "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of CreateUniqueConstraintOp", "ret_type": "alembic.operations.ops.CreateUniqueConstraintOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateUniqueConstraintOp.kw", "name": "kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateUniqueConstraintOp.schema", "name": "schema", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.CreateUniqueConstraintOp.table_name", "name": "table_name", "type": "builtins.str"}}, "to_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.CreateUniqueConstraintOp.to_constraint", "name": "to_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "arg_types": ["alembic.operations.ops.CreateUniqueConstraintOp", {".class": "UnionType", "items": ["alembic.runtime.migration.MigrationContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_constraint of CreateUniqueConstraintOp", "ret_type": "sqlalchemy.sql.schema.UniqueConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.CreateUniqueConstraintOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.CreateUniqueConstraintOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DowngradeOps": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.OpContainer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.DowngradeOps", "name": "DowngradeOps", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.DowngradeOps", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.DowngradeOps", "alembic.operations.ops.OpContainer", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "ops", "downgrade_token"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DowngradeOps.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "ops", "downgrade_token"], "arg_types": ["alembic.operations.ops.DowngradeOps", {".class": "Instance", "args": ["alembic.operations.ops.MigrateOperation"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DowngradeOps", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "downgrade_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DowngradeOps.downgrade_token", "name": "downgrade_token", "type": "builtins.str"}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DowngradeOps.reverse", "name": "reverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.DowngradeOps"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse of DowngradeOps", "ret_type": "alembic.operations.ops.UpgradeOps", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.DowngradeOps.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.DowngradeOps", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DropColumnOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.AlterTableOp"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.DropColumnOp", "name": "DropColumnOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.DropColumnOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.DropColumnOp", "alembic.operations.ops.AlterTableOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 4], "arg_names": ["self", "table_name", "column_name", "schema", "_reverse", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropColumnOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 4], "arg_names": ["self", "table_name", "column_name", "schema", "_reverse", "kw"], "arg_types": ["alembic.operations.ops.DropColumnOp", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["alembic.operations.ops.AddColumnOp", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DropColumnOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reverse": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropColumnOp._reverse", "name": "_reverse", "type": {".class": "UnionType", "items": ["alembic.operations.ops.AddColumnOp", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "batch_drop_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "operations", "column_name", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.DropColumnOp.batch_drop_column", "name": "batch_drop_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "operations", "column_name", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropColumnOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_drop_column of DropColumnOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.DropColumnOp.batch_drop_column", "name": "batch_drop_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "operations", "column_name", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropColumnOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_drop_column of DropColumnOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "column_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropColumnOp.column_name", "name": "column_name", "type": "builtins.str"}}, "drop_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["cls", "operations", "table_name", "column_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.DropColumnOp.drop_column", "name": "drop_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["cls", "operations", "table_name", "column_name", "schema", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropColumnOp"}, "alembic.operations.base.Operations", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_column of DropColumnOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.DropColumnOp.drop_column", "name": "drop_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["cls", "operations", "table_name", "column_name", "schema", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropColumnOp"}, "alembic.operations.base.Operations", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_column of DropColumnOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_column_and_tablename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "schema", "tname", "col"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.DropColumnOp.from_column_and_tablename", "name": "from_column_and_tablename", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "schema", "tname", "col"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropColumnOp"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_column_and_tablename of DropColumnOp", "ret_type": "alembic.operations.ops.DropColumnOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.DropColumnOp.from_column_and_tablename", "name": "from_column_and_tablename", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "schema", "tname", "col"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropColumnOp"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_column_and_tablename of DropColumnOp", "ret_type": "alembic.operations.ops.DropColumnOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropColumnOp.kw", "name": "kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropColumnOp.reverse", "name": "reverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.DropColumnOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse of DropColumnOp", "ret_type": "alembic.operations.ops.AddColumnOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropColumnOp.to_column", "name": "to_column", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "arg_types": ["alembic.operations.ops.DropColumnOp", {".class": "UnionType", "items": ["alembic.runtime.migration.MigrationContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_column of DropColumnOp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_diff_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropColumnOp.to_diff_tuple", "name": "to_diff_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.DropColumnOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_diff_tuple of DropColumnOp", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.DropColumnOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.DropColumnOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DropConstraintOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.MigrateOperation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.DropConstraintOp", "name": "DropConstraintOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.DropConstraintOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.DropConstraintOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 5, 5], "arg_names": ["self", "constraint_name", "table_name", "type_", "schema", "_reverse"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropConstraintOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5], "arg_names": ["self", "constraint_name", "table_name", "type_", "schema", "_reverse"], "arg_types": ["alembic.operations.ops.DropConstraintOp", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintNameDefined"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["alembic.operations.ops.AddConstraintOp", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DropConstraintOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reverse": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropConstraintOp._reverse", "name": "_reverse", "type": {".class": "UnionType", "items": ["alembic.operations.ops.AddConstraintOp", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "batch_drop_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "operations", "constraint_name", "type_"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.DropConstraintOp.batch_drop_constraint", "name": "batch_drop_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "operations", "constraint_name", "type_"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropConstraintOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_drop_constraint of DropConstraintOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.DropConstraintOp.batch_drop_constraint", "name": "batch_drop_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "operations", "constraint_name", "type_"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropConstraintOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_drop_constraint of DropConstraintOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "constraint_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropConstraintOp.constraint_name", "name": "constraint_name", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintNameDefined"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "constraint_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropConstraintOp.constraint_type", "name": "constraint_type", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "drop_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 5], "arg_names": ["cls", "operations", "constraint_name", "table_name", "type_", "schema"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.DropConstraintOp.drop_constraint", "name": "drop_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 5], "arg_names": ["cls", "operations", "constraint_name", "table_name", "type_", "schema"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropConstraintOp"}, "alembic.operations.base.Operations", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_constraint of DropConstraintOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.DropConstraintOp.drop_constraint", "name": "drop_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 5], "arg_names": ["cls", "operations", "constraint_name", "table_name", "type_", "schema"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropConstraintOp"}, "alembic.operations.base.Operations", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_constraint of DropConstraintOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.DropConstraintOp.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropConstraintOp"}, "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of DropConstraintOp", "ret_type": "alembic.operations.ops.DropConstraintOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.DropConstraintOp.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropConstraintOp"}, "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of DropConstraintOp", "ret_type": "alembic.operations.ops.DropConstraintOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropConstraintOp.reverse", "name": "reverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.DropConstraintOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse of DropConstraintOp", "ret_type": "alembic.operations.ops.AddConstraintOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropConstraintOp.schema", "name": "schema", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropConstraintOp.table_name", "name": "table_name", "type": "builtins.str"}}, "to_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropConstraintOp.to_constraint", "name": "to_constraint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.DropConstraintOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_constraint of DropConstraintOp", "ret_type": "sqlalchemy.sql.schema.Constraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_diff_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropConstraintOp.to_diff_tuple", "name": "to_diff_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.DropConstraintOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_diff_tuple of DropConstraintOp", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.schema.SchemaItem"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.DropConstraintOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.DropConstraintOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DropIndexOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.MigrateOperation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.DropIndexOp", "name": "DropIndexOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.DropIndexOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.DropIndexOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 4], "arg_names": ["self", "index_name", "table_name", "schema", "if_exists", "_reverse", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropIndexOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 4], "arg_names": ["self", "index_name", "table_name", "schema", "if_exists", "_reverse", "kw"], "arg_types": ["alembic.operations.ops.DropIndexOp", {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str", "sqlalchemy.sql.elements.conv"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["alembic.operations.ops.CreateIndexOp", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DropIndexOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reverse": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropIndexOp._reverse", "name": "_reverse", "type": {".class": "UnionType", "items": ["alembic.operations.ops.CreateIndexOp", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "batch_drop_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "operations", "index_name", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.DropIndexOp.batch_drop_index", "name": "batch_drop_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "operations", "index_name", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropIndexOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_drop_index of DropIndexOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.DropIndexOp.batch_drop_index", "name": "batch_drop_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "operations", "index_name", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropIndexOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_drop_index of DropIndexOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "drop_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 5, 5, 4], "arg_names": ["cls", "operations", "index_name", "table_name", "schema", "if_exists", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.DropIndexOp.drop_index", "name": "drop_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5, 4], "arg_names": ["cls", "operations", "index_name", "table_name", "schema", "if_exists", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropIndexOp"}, "alembic.operations.base.Operations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_index of DropIndexOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.DropIndexOp.drop_index", "name": "drop_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5, 4], "arg_names": ["cls", "operations", "index_name", "table_name", "schema", "if_exists", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropIndexOp"}, "alembic.operations.base.Operations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_index of DropIndexOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "index"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.DropIndexOp.from_index", "name": "from_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "index"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropIndexOp"}, "sqlalchemy.sql.schema.Index"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_index of DropIndexOp", "ret_type": "alembic.operations.ops.DropIndexOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.DropIndexOp.from_index", "name": "from_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "index"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropIndexOp"}, "sqlalchemy.sql.schema.Index"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_index of DropIndexOp", "ret_type": "alembic.operations.ops.DropIndexOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "if_exists": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropIndexOp.if_exists", "name": "if_exists", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "index_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropIndexOp.index_name", "name": "index_name", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str", "sqlalchemy.sql.elements.conv"], "uses_pep604_syntax": false}}}, "kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropIndexOp.kw", "name": "kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropIndexOp.reverse", "name": "reverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.DropIndexOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse of DropIndexOp", "ret_type": "alembic.operations.ops.CreateIndexOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropIndexOp.schema", "name": "schema", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropIndexOp.table_name", "name": "table_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "to_diff_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropIndexOp.to_diff_tuple", "name": "to_diff_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.DropIndexOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_diff_tuple of DropIndexOp", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.schema.Index"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropIndexOp.to_index", "name": "to_index", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "arg_types": ["alembic.operations.ops.DropIndexOp", {".class": "UnionType", "items": ["alembic.runtime.migration.MigrationContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_index of DropIndexOp", "ret_type": "sqlalchemy.sql.schema.Index", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.DropIndexOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.DropIndexOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DropTableCommentOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.AlterTableOp"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.DropTableCommentOp", "name": "DropTableCommentOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.DropTableCommentOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.DropTableCommentOp", "alembic.operations.ops.AlterTableOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "table_name", "schema", "existing_comment"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropTableCommentOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "table_name", "schema", "existing_comment"], "arg_types": ["alembic.operations.ops.DropTableCommentOp", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DropTableCommentOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_drop_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["cls", "operations", "existing_comment"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.DropTableCommentOp.batch_drop_table_comment", "name": "batch_drop_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["cls", "operations", "existing_comment"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropTableCommentOp"}, "alembic.operations.base.BatchOperations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_drop_table_comment of DropTableCommentOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.DropTableCommentOp.batch_drop_table_comment", "name": "batch_drop_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["cls", "operations", "existing_comment"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropTableCommentOp"}, "alembic.operations.base.BatchOperations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_drop_table_comment of DropTableCommentOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "drop_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["cls", "operations", "table_name", "existing_comment", "schema"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.DropTableCommentOp.drop_table_comment", "name": "drop_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["cls", "operations", "table_name", "existing_comment", "schema"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropTableCommentOp"}, "alembic.operations.base.Operations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_table_comment of DropTableCommentOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.DropTableCommentOp.drop_table_comment", "name": "drop_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["cls", "operations", "table_name", "existing_comment", "schema"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropTableCommentOp"}, "alembic.operations.base.Operations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_table_comment of DropTableCommentOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "existing_comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropTableCommentOp.existing_comment", "name": "existing_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropTableCommentOp.reverse", "name": "reverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.DropTableCommentOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse of DropTableCommentOp", "ret_type": "alembic.operations.ops.CreateTableCommentOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_diff_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropTableCommentOp.to_diff_tuple", "name": "to_diff_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.DropTableCommentOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_diff_tuple of DropTableCommentOp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropTableCommentOp.to_table", "name": "to_table", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "arg_types": ["alembic.operations.ops.DropTableCommentOp", {".class": "UnionType", "items": ["alembic.runtime.migration.MigrationContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_table of DropTableCommentOp", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.DropTableCommentOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.DropTableCommentOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DropTableOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.MigrateOperation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.DropTableOp", "name": "DropTableOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.DropTableOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.DropTableOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "table_name", "schema", "if_exists", "table_kw", "_reverse"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropTableOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "table_name", "schema", "if_exists", "table_kw", "_reverse"], "arg_types": ["alembic.operations.ops.DropTableOp", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["alembic.operations.ops.CreateTableOp", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DropTableOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reverse": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropTableOp._reverse", "name": "_reverse", "type": {".class": "UnionType", "items": ["alembic.operations.ops.CreateTableOp", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropTableOp.comment", "name": "comment", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "drop_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 4], "arg_names": ["cls", "operations", "table_name", "schema", "if_exists", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.DropTableOp.drop_table", "name": "drop_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 4], "arg_names": ["cls", "operations", "table_name", "schema", "if_exists", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropTableOp"}, "alembic.operations.base.Operations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_table of DropTableOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.DropTableOp.drop_table", "name": "drop_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 4], "arg_names": ["cls", "operations", "table_name", "schema", "if_exists", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropTableOp"}, "alembic.operations.base.Operations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_table of DropTableOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["cls", "table", "_namespace_metadata"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.DropTableOp.from_table", "name": "from_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["cls", "table", "_namespace_metadata"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropTableOp"}, "sqlalchemy.sql.schema.Table", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_table of DropTableOp", "ret_type": "alembic.operations.ops.DropTableOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.DropTableOp.from_table", "name": "from_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["cls", "table", "_namespace_metadata"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.DropTableOp"}, "sqlalchemy.sql.schema.Table", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_table of DropTableOp", "ret_type": "alembic.operations.ops.DropTableOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "if_exists": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropTableOp.if_exists", "name": "if_exists", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "prefixes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropTableOp.prefixes", "name": "prefixes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropTableOp.reverse", "name": "reverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.DropTableOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse of DropTableOp", "ret_type": "alembic.operations.ops.CreateTableOp", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropTableOp.schema", "name": "schema", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "table_kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropTableOp.table_kw", "name": "table_kw", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.DropTableOp.table_name", "name": "table_name", "type": "builtins.str"}}, "to_diff_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropTableOp.to_diff_tuple", "name": "to_diff_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.DropTableOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_diff_tuple of DropTableOp", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.schema.Table"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.DropTableOp.to_table", "name": "to_table", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "arg_types": ["alembic.operations.ops.DropTableOp", {".class": "UnionType", "items": ["alembic.runtime.migration.MigrationContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_table of DropTableOp", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.DropTableOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.DropTableOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef"}, "ExecuteSQLOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.MigrateOperation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.ExecuteSQLOp", "name": "ExecuteSQLOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.ExecuteSQLOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.ExecuteSQLOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "sqltext", "execution_options"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.ExecuteSQLOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "sqltext", "execution_options"], "arg_types": ["alembic.operations.ops.ExecuteSQLOp", {".class": "UnionType", "items": ["sqlalchemy.sql.base.Executable", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExecuteSQLOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["cls", "operations", "sqltext", "execution_options"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.ExecuteSQLOp.batch_execute", "name": "batch_execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["cls", "operations", "sqltext", "execution_options"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.ExecuteSQLOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["sqlalchemy.sql.base.Executable", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_execute of ExecuteSQLOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.ExecuteSQLOp.batch_execute", "name": "batch_execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["cls", "operations", "sqltext", "execution_options"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.ExecuteSQLOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["sqlalchemy.sql.base.Executable", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_execute of ExecuteSQLOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["cls", "operations", "sqltext", "execution_options"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.ExecuteSQLOp.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["cls", "operations", "sqltext", "execution_options"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.ExecuteSQLOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["sqlalchemy.sql.base.Executable", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of ExecuteSQLOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.ExecuteSQLOp.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["cls", "operations", "sqltext", "execution_options"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.ExecuteSQLOp"}, "alembic.operations.base.Operations", {".class": "UnionType", "items": ["sqlalchemy.sql.base.Executable", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of ExecuteSQLOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "execution_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.ExecuteSQLOp.execution_options", "name": "execution_options", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "sqltext": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.ExecuteSQLOp.sqltext", "name": "sqltext", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.base.Executable", "builtins.str"], "uses_pep604_syntax": false}}}, "to_diff_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.ExecuteSQLOp.to_diff_tuple", "name": "to_diff_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.ExecuteSQLOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_diff_tuple of ExecuteSQLOp", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["sqlalchemy.sql.base.Executable", "builtins.str"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.ExecuteSQLOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.ExecuteSQLOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ForeignKeyConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ForeignKeyConstraint", "kind": "Gdef"}, "FrozenSet": {".class": "SymbolTableNode", "cross_ref": "typing.FrozenSet", "kind": "Gdef"}, "Function": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.Function", "kind": "Gdef"}, "Identity": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Identity", "kind": "Gdef"}, "Index": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Index", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.MetaData", "kind": "Gdef"}, "MigrateOperation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.MigrateOperation", "name": "MigrateOperation", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.MigrateOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "_mutations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "alembic.operations.ops.MigrateOperation._mutations", "name": "_mutations", "type": {".class": "Instance", "args": ["alembic.autogenerate.rewriter.Rewriter"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.ops.MigrateOperation.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.MigrateOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of MigrateOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.MigrateOperation.info", "name": "info", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.MigrateOperation.reverse", "name": "reverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.MigrateOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse of MigrateOperation", "ret_type": "alembic.operations.ops.MigrateOperation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_diff_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.MigrateOperation.to_diff_tuple", "name": "to_diff_tuple", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.MigrateOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_diff_tuple of MigrateOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.MigrateOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.MigrateOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MigrationContext": {".class": "SymbolTableNode", "cross_ref": "alembic.runtime.migration.MigrationContext", "kind": "Gdef"}, "MigrationScript": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.MigrateOperation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.MigrationScript", "name": "MigrationScript", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.MigrationScript", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.MigrationScript", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "rev_id", "upgrade_ops", "downgrade_ops", "message", "imports", "head", "splice", "branch_label", "version_path", "depends_on"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.MigrationScript.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "rev_id", "upgrade_ops", "downgrade_ops", "message", "imports", "head", "splice", "branch_label", "version_path", "depends_on"], "arg_types": ["alembic.operations.ops.MigrationScript", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "alembic.operations.ops.UpgradeOps", "alembic.operations.ops.DowngradeOps", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevIdType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevIdType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MigrationScript", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_downgrade_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.operations.ops.MigrationScript._downgrade_ops", "name": "_downgrade_ops", "type": {".class": "Instance", "args": ["alembic.operations.ops.DowngradeOps"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_needs_render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.operations.ops.MigrationScript._needs_render", "name": "_needs_render", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_upgrade_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.operations.ops.MigrationScript._upgrade_ops", "name": "_upgrade_ops", "type": {".class": "Instance", "args": ["alembic.operations.ops.UpgradeOps"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "branch_label": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.MigrationScript.branch_label", "name": "branch_label", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevIdType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "depends_on": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.MigrationScript.depends_on", "name": "depends_on", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevIdType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "downgrade_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": ["is_property"], "fullname": "alembic.operations.ops.MigrationScript.downgrade_ops", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "alembic.operations.ops.MigrationScript.downgrade_ops", "name": "downgrade_ops", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.MigrationScript"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "downgrade_ops of MigrationScript", "ret_type": {".class": "UnionType", "items": ["alembic.operations.ops.DowngradeOps", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.MigrationScript.downgrade_ops", "name": "downgrade_ops", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.MigrationScript"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "downgrade_ops of MigrationScript", "ret_type": {".class": "UnionType", "items": ["alembic.operations.ops.DowngradeOps", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "downgrade_ops"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.ops.MigrationScript.downgrade_ops", "name": "downgrade_ops", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "downgrade_ops"], "arg_types": ["alembic.operations.ops.MigrationScript", {".class": "UnionType", "items": ["alembic.operations.ops.DowngradeOps", {".class": "Instance", "args": ["alembic.operations.ops.DowngradeOps"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "downgrade_ops of MigrationScript", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "downgrade_ops", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.MigrationScript"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "downgrade_ops of MigrationScript", "ret_type": {".class": "UnionType", "items": ["alembic.operations.ops.DowngradeOps", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "downgrade_ops_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.operations.ops.MigrationScript.downgrade_ops_list", "name": "downgrade_ops_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.MigrationScript"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "downgrade_ops_list of MigrationScript", "ret_type": {".class": "Instance", "args": ["alembic.operations.ops.DowngradeOps"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.MigrationScript.downgrade_ops_list", "name": "downgrade_ops_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.MigrationScript"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "downgrade_ops_list of MigrationScript", "ret_type": {".class": "Instance", "args": ["alembic.operations.ops.DowngradeOps"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.MigrationScript.head", "name": "head", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "imports": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.MigrationScript.imports", "name": "imports", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.MigrationScript.message", "name": "message", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "rev_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.MigrationScript.rev_id", "name": "rev_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "splice": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.MigrationScript.splice", "name": "splice", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "upgrade_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": ["is_property"], "fullname": "alembic.operations.ops.MigrationScript.upgrade_ops", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "alembic.operations.ops.MigrationScript.upgrade_ops", "name": "upgrade_ops", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.MigrationScript"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upgrade_ops of MigrationScript", "ret_type": {".class": "UnionType", "items": ["alembic.operations.ops.UpgradeOps", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.MigrationScript.upgrade_ops", "name": "upgrade_ops", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.MigrationScript"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upgrade_ops of MigrationScript", "ret_type": {".class": "UnionType", "items": ["alembic.operations.ops.UpgradeOps", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "upgrade_ops"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.ops.MigrationScript.upgrade_ops", "name": "upgrade_ops", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "upgrade_ops"], "arg_types": ["alembic.operations.ops.MigrationScript", {".class": "UnionType", "items": ["alembic.operations.ops.UpgradeOps", {".class": "Instance", "args": ["alembic.operations.ops.UpgradeOps"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upgrade_ops of MigrationScript", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "upgrade_ops", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.MigrationScript"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upgrade_ops of MigrationScript", "ret_type": {".class": "UnionType", "items": ["alembic.operations.ops.UpgradeOps", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "upgrade_ops_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.operations.ops.MigrationScript.upgrade_ops_list", "name": "upgrade_ops_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.MigrationScript"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upgrade_ops_list of MigrationScript", "ret_type": {".class": "Instance", "args": ["alembic.operations.ops.UpgradeOps"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.MigrationScript.upgrade_ops_list", "name": "upgrade_ops_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.MigrationScript"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upgrade_ops_list of MigrationScript", "ret_type": {".class": "Instance", "args": ["alembic.operations.ops.UpgradeOps"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "version_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.MigrationScript.version_path", "name": "version_path", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.MigrationScript.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.MigrationScript", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModifyTableOps": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.OpContainer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.ModifyTableOps", "name": "ModifyTableOps", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.ModifyTableOps", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.ModifyTableOps", "alembic.operations.ops.OpContainer", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "table_name", "ops", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.ModifyTableOps.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "table_name", "ops", "schema"], "arg_types": ["alembic.operations.ops.ModifyTableOps", "builtins.str", {".class": "Instance", "args": ["alembic.operations.ops.MigrateOperation"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ModifyTableOps", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.ModifyTableOps.reverse", "name": "reverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.ModifyTableOps"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse of ModifyTableOps", "ret_type": "alembic.operations.ops.ModifyTableOps", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.ModifyTableOps.schema", "name": "schema", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.ModifyTableOps.table_name", "name": "table_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.ModifyTableOps.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.ModifyTableOps", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "NULLTYPE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NULLTYPE", "kind": "Gdef"}, "OpContainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.MigrateOperation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.OpContainer", "name": "OpContainer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.OpContainer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.OpContainer", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "ops"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.OpContainer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "ops"], "arg_types": ["alembic.operations.ops.OpContainer", {".class": "Instance", "args": ["alembic.operations.ops.MigrateOperation"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OpContainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ops_as_diffs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "migrations"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.OpContainer._ops_as_diffs", "name": "_ops_as_diffs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "migrations"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.OpContainer"}, "alembic.operations.ops.OpContainer"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ops_as_diffs of OpContainer", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.OpContainer._ops_as_diffs", "name": "_ops_as_diffs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "migrations"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.OpContainer"}, "alembic.operations.ops.OpContainer"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ops_as_diffs of OpContainer", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "as_diffs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.OpContainer.as_diffs", "name": "as_diffs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.OpContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_diffs of OpContainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_empty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.OpContainer.is_empty", "name": "is_empty", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.OpContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_empty of OpContainer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.OpContainer.ops", "name": "ops", "type": {".class": "Instance", "args": ["alembic.operations.ops.MigrateOperation"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.OpContainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.OpContainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Operations": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.base.Operations", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PrimaryKeyConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.PrimaryKeyConstraint", "kind": "Gdef"}, "RenameTableOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.AlterTableOp"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.RenameTableOp", "name": "RenameTableOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.RenameTableOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.RenameTableOp", "alembic.operations.ops.AlterTableOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "old_table_name", "new_table_name", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.RenameTableOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "old_table_name", "new_table_name", "schema"], "arg_types": ["alembic.operations.ops.RenameTableOp", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RenameTableOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "new_table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.RenameTableOp.new_table_name", "name": "new_table_name", "type": "builtins.str"}}, "rename_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["cls", "operations", "old_table_name", "new_table_name", "schema"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.ops.RenameTableOp.rename_table", "name": "rename_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["cls", "operations", "old_table_name", "new_table_name", "schema"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.RenameTableOp"}, "alembic.operations.base.Operations", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rename_table of RenameTableOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.ops.RenameTableOp.rename_table", "name": "rename_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["cls", "operations", "old_table_name", "new_table_name", "schema"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.ops.RenameTableOp"}, "alembic.operations.base.Operations", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rename_table of RenameTableOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.RenameTableOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.RenameTableOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Rewriter": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.rewriter.Rewriter", "kind": "Gdef"}, "SchemaItem": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.SchemaItem", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "TableClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TableClause", "kind": "Gdef"}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UniqueConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.UniqueConstraint", "kind": "Gdef"}, "UpgradeOps": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.OpContainer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.ops.UpgradeOps", "name": "UpgradeOps", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.ops.UpgradeOps", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.ops", "mro": ["alembic.operations.ops.UpgradeOps", "alembic.operations.ops.OpContainer", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "ops", "upgrade_token"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.UpgradeOps.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "ops", "upgrade_token"], "arg_types": ["alembic.operations.ops.UpgradeOps", {".class": "Instance", "args": ["alembic.operations.ops.MigrateOperation"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UpgradeOps", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.UpgradeOps.reverse", "name": "reverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.ops.UpgradeOps"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse of UpgradeOps", "ret_type": "alembic.operations.ops.DowngradeOps", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reverse_into": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "downgrade_ops"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.ops.UpgradeOps.reverse_into", "name": "reverse_into", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "downgrade_ops"], "arg_types": ["alembic.operations.ops.UpgradeOps", "alembic.operations.ops.DowngradeOps"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse_into of UpgradeOps", "ret_type": "alembic.operations.ops.DowngradeOps", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upgrade_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.ops.UpgradeOps.upgrade_token", "name": "upgrade_token", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops.UpgradeOps.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.ops.UpgradeOps", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops._AC", "name": "_AC", "upper_bound": "alembic.operations.ops.AddConstraintOp", "values": [], "variance": 0}}, "_RevIdType": {".class": "SymbolTableNode", "cross_ref": "alembic.script.revision._RevIdType", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.ops._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.ops.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.ops.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.ops.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.ops.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.ops.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.ops.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "conv": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.conv", "kind": "Gdef"}, "quoted_name": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.quoted_name", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "schemaobj": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.schemaobj", "kind": "Gdef"}, "sqla_compat": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "alembic.util", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/operations/ops.py"}