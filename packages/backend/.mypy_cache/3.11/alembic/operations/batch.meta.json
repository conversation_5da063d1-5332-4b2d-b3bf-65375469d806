{"data_mtime": 1751259991, "dep_lines": [23, 25, 27, 28, 46, 48, 50, 52, 20, 22, 24, 27, 45, 4, 6, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 25, 25, 25, 25, 10, 10, 5, 20, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.schema", "sqlalchemy.util.topological", "alembic.util.exc", "alembic.util.sqla_compat", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "sqlalchemy.sql.type_api", "alembic.ddl.impl", "sqlalchemy.schema", "sqlalchemy.types", "sqlalchemy.util", "alembic.util", "sqlalchemy.engine", "__future__", "typing", "sqlalchemy", "builtins", "_collections_abc", "_typeshed", "abc", "alembic.ddl", "enum", "importlib", "importlib.machinery", "sqlalchemy.engine.base", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.result", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql", "sqlalchemy.sql._selectable_constructors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.dml", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "09868e057d4e08946743fb0ab772aa5c773f3479", "id": "alembic.operations.batch", "ignore_all": true, "interface_hash": "0e8b8f53a33ecdd55a7386ff89125ecf3ea40095", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/operations/batch.py", "plugin_data": null, "size": 26943, "suppressed": [], "version_id": "1.13.0"}