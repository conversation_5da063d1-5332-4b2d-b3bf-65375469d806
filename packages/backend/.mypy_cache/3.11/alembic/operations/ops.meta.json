{"data_mtime": 1751259991, "dep_lines": [24, 25, 28, 34, 38, 39, 51, 52, 54, 55, 56, 22, 24, 27, 33, 1, 3, 4, 5, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 25, 25, 25, 25, 25, 25, 25, 25, 5, 20, 10, 25, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["alembic.operations.schemaobj", "alembic.operations.base", "alembic.util.sqla_compat", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.type_api", "alembic.autogenerate.rewriter", "alembic.runtime.migration", "alembic.script.revision", "sqlalchemy.types", "alembic.operations", "alembic.util", "sqlalchemy.sql", "__future__", "abc", "re", "typing", "alembic", "builtins", "alembic.autogenerate", "alembic.operations.batch", "alembic.runtime", "alembic.util.langhelpers", "enum", "importlib", "importlib.machinery", "sqlalchemy", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util._collections", "sqlalchemy.util.langhelpers"], "hash": "f94c1c547ad84a8058f6f3dc00cb0d4460d81f40", "id": "alembic.operations.ops", "ignore_all": true, "interface_hash": "41c3c480be4c4c64c7643903737c852531b583d7", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/operations/ops.py", "plugin_data": null, "size": 94953, "suppressed": [], "version_id": "1.13.0"}