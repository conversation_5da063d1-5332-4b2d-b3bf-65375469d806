{"data_mtime": 1751259991, "dep_lines": [25, 27, 28, 30, 31, 43, 46, 47, 54, 70, 574, 27, 29, 41, 42, 51, 69, 575, 3, 5, 6, 7, 8, 29, 40, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 25, 25, 25, 25, 25, 20, 20, 10, 25, 25, 25, 25, 20, 5, 5, 10, 10, 5, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.elements", "alembic.operations.batch", "alembic.operations.schemaobj", "alembic.util.sqla_compat", "alembic.util.compat", "sqlalchemy.sql.expression", "sqlalchemy.sql.functions", "sqlalchemy.sql.schema", "alembic.operations.ops", "alembic.runtime.migration", "sqlalchemy.ext.asyncio", "alembic.operations", "alembic.util", "sqlalchemy.engine", "sqlalchemy.sql", "sqlalchemy.types", "alembic.ddl", "sqlalchemy.util", "__future__", "contextlib", "re", "textwrap", "typing", "alembic", "sqlalchemy", "builtins", "abc", "alembic.ddl.impl", "alembic.runtime", "alembic.util.langhelpers", "importlib", "importlib.machinery", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "654a724047b3166b13bac1cbe9fcc1e0bea473f5", "id": "alembic.operations.base", "ignore_all": true, "interface_hash": "ce99802a510cf9be5fb76345021a749bd870a89d", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/operations/base.py", "plugin_data": null, "size": 74474, "suppressed": [], "version_id": "1.13.0"}