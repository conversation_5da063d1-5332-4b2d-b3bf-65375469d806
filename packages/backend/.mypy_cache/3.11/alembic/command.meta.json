{"data_mtime": 1751259992, "dep_lines": [13, 18, 19, 11, 12, 14, 17, 3, 5, 6, 11, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 10, 10, 5, 25, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["alembic.runtime.environment", "alembic.script.base", "alembic.script.revision", "alembic.autogenerate", "alembic.util", "alembic.script", "alembic.config", "__future__", "os", "typing", "alembic", "builtins", "abc", "alembic.operations", "alembic.operations.ops", "alembic.runtime", "alembic.runtime.migration", "importlib", "importlib.machinery"], "hash": "a6bb9d839fcb8911d6bd189e07cdc6d66cc5c399", "id": "alembic.command", "ignore_all": true, "interface_hash": "2db0386a0f5d9e94ae9f278b86a7d5673af24ce8", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/command.py", "plugin_data": null, "size": 22169, "suppressed": [], "version_id": "1.13.0"}