{"data_mtime": 1751259991, "dep_lines": [24, 25, 27, 31, 33, 35, 36, 26, 34, 39, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.base", "sqlalchemy.engine.url", "sqlalchemy.sql.schema", "sqlalchemy.sql.type_api", "alembic.autogenerate.api", "alembic.operations.ops", "alembic.runtime.migration", "sqlalchemy.sql", "alembic.config", "alembic.script", "__future__", "typing", "builtins", "abc", "alembic.autogenerate", "alembic.op", "alembic.operations", "alembic.runtime", "alembic.script.base", "contextlib", "importlib", "importlib.machinery", "sqlalchemy", "sqlalchemy.engine", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "d81106f08e9e9eef4261da04ea3c9ced7986d22e", "id": "alembic.context", "ignore_all": true, "interface_hash": "547997def0c1da248e4c7be16fcd18b715095ac9", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/context.pyi", "plugin_data": null, "size": 31737, "suppressed": [], "version_id": "1.13.0"}