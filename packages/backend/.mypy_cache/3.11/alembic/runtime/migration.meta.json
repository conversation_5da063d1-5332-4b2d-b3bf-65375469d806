{"data_mtime": 1751259991, "dep_lines": [28, 29, 33, 34, 40, 42, 45, 47, 49, 27, 31, 32, 43, 46, 4, 6, 8, 9, 10, 25, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 25, 25, 25, 25, 25, 5, 10, 10, 25, 25, 5, 5, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.url", "sqlalchemy.engine.strategies", "alembic.util.sqla_compat", "alembic.util.compat", "sqlalchemy.engine.base", "sqlalchemy.engine.mock", "alembic.runtime.environment", "alembic.script.base", "alembic.script.revision", "sqlalchemy.engine", "alembic.ddl", "alembic.util", "sqlalchemy.sql", "alembic.config", "__future__", "contextlib", "logging", "sys", "typing", "sqlalchemy", "alembic", "builtins", "abc", "alembic.ddl.impl", "alembic.script", "alembic.util.langhelpers", "importlib", "importlib.machinery", "io", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.util", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "bba54e0ee84e39fbe882777ac38a2988bf1c6e90", "id": "alembic.runtime.migration", "ignore_all": true, "interface_hash": "e1c58a3a40dffe70e51991354f7af4b1e40eb20a", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/runtime/migration.py", "plugin_data": null, "size": 49857, "suppressed": [], "version_id": "1.13.0"}