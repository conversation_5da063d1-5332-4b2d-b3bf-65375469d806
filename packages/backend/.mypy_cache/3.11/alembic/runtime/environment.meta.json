{"data_mtime": 1751259992, "dep_lines": [19, 23, 27, 31, 35, 38, 41, 42, 25, 26, 30, 32, 39, 40, 1, 3, 21, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 25, 25, 25, 10, 5, 25, 25, 25, 25, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.schema", "alembic.runtime.migration", "alembic.script.revision", "sqlalchemy.engine.base", "sqlalchemy.sql.type_api", "alembic.autogenerate.api", "alembic.operations.ops", "alembic.script.base", "alembic.util", "alembic.operations", "sqlalchemy.engine", "sqlalchemy.sql", "alembic.config", "alembic.ddl", "__future__", "typing", "typing_extensions", "alembic", "builtins", "_typeshed", "abc", "alembic.autogenerate", "alembic.ddl.impl", "alembic.script", "alembic.util.langhelpers", "contextlib", "importlib", "importlib.machinery", "sqlalchemy", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.url", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "54768c094f1175f5388ffe1c49ce1ec7a8076bac", "id": "alembic.runtime.environment", "ignore_all": true, "interface_hash": "b6e2815be5da5ee275eb1d12c3c5d1579931a7c7", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/runtime/environment.py", "plugin_data": null, "size": 41497, "suppressed": [], "version_id": "1.13.0"}