{".class": "MypyFile", "_fullname": "alembic.ddl.sqlite", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BatchOperationsImpl": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.batch.BatchOperationsImpl", "kind": "Gdef"}, "Cast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Cast", "kind": "Gdef"}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "Constraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Constraint", "kind": "Gdef"}, "DDLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.DDLCompiler", "kind": "Gdef"}, "DefaultImpl": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.impl.DefaultImpl", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Inspector": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.Inspector", "kind": "Gdef"}, "JSON": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.JSON", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RenameTable": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.RenameTable", "kind": "Gdef"}, "SQLiteImpl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.ddl.impl.DefaultImpl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.sqlite.SQLiteImpl", "name": "SQLiteImpl", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.sqlite.SQLiteImpl", "has_param_spec_type": false, "metaclass_type": "alembic.ddl.impl.ImplMeta", "metadata": {}, "module_name": "alembic.ddl.sqlite", "mro": ["alembic.ddl.sqlite.SQLiteImpl", "alembic.ddl.impl.DefaultImpl", "builtins.object"], "names": {".class": "SymbolTable", "__dialect__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl.sqlite.SQLiteImpl.__dialect__", "name": "__dialect__", "type": "builtins.str"}}, "_guess_if_default_is_unparenthesized_sql_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.sqlite.SQLiteImpl._guess_if_default_is_unparenthesized_sql_expr", "name": "_guess_if_default_is_unparenthesized_sql_expr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "arg_types": ["alembic.ddl.sqlite.SQLiteImpl", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_guess_if_default_is_unparenthesized_sql_expr of SQLiteImpl", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "const"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.sqlite.SQLiteImpl.add_constraint", "name": "add_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "const"], "arg_types": ["alembic.ddl.sqlite.SQLiteImpl", "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_constraint of SQLiteImpl", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "autogen_column_reflect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "inspector", "table", "column_info"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.sqlite.SQLiteImpl.autogen_column_reflect", "name": "autogen_column_reflect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "inspector", "table", "column_info"], "arg_types": ["alembic.ddl.sqlite.SQLiteImpl", "sqlalchemy.engine.reflection.Inspector", "sqlalchemy.sql.schema.Table", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "autogen_column_reflect of SQLiteImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast_for_batch_migrate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "existing", "existing_transfer", "new_type"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.sqlite.SQLiteImpl.cast_for_batch_migrate", "name": "cast_for_batch_migrate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "existing", "existing_transfer", "new_type"], "arg_types": ["alembic.ddl.sqlite.SQLiteImpl", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.Cast"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cast_for_batch_migrate of SQLiteImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compare_server_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "inspector_column", "metadata_column", "rendered_metadata_default", "rendered_inspector_default"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.sqlite.SQLiteImpl.compare_server_default", "name": "compare_server_default", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "inspector_column", "metadata_column", "rendered_metadata_default", "rendered_inspector_default"], "arg_types": ["alembic.ddl.sqlite.SQLiteImpl", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compare_server_default of SQLiteImpl", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "correct_for_autogen_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "conn_unique_constraints", "conn_indexes", "metadata_unique_constraints", "metadata_indexes"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.sqlite.SQLiteImpl.correct_for_autogen_constraints", "name": "correct_for_autogen_constraints", "type": null}}, "drop_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "const"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.sqlite.SQLiteImpl.drop_constraint", "name": "drop_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "const"], "arg_types": ["alembic.ddl.sqlite.SQLiteImpl", "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_constraint of SQLiteImpl", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_ddl_sql_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "expr", "is_server_default", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.sqlite.SQLiteImpl.render_ddl_sql_expr", "name": "render_ddl_sql_expr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "expr", "is_server_default", "kw"], "arg_types": ["alembic.ddl.sqlite.SQLiteImpl", "sqlalchemy.sql.elements.ClauseElement", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_ddl_sql_expr of SQLiteImpl", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "requires_recreate_in_batch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "batch_op"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.sqlite.SQLiteImpl.requires_recreate_in_batch", "name": "requires_recreate_in_batch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "batch_op"], "arg_types": ["alembic.ddl.sqlite.SQLiteImpl", "alembic.operations.batch.BatchOperationsImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "requires_recreate_in_batch of SQLiteImpl", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transactional_ddl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl.sqlite.SQLiteImpl.transactional_ddl", "name": "transactional_ddl", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.sqlite.SQLiteImpl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.sqlite.SQLiteImpl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.sqlite.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.sqlite.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.sqlite.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.sqlite.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.sqlite.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.sqlite.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "alter_table": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.alter_table", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.cast", "kind": "Gdef"}, "compiles": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat.compiles", "kind": "Gdef"}, "format_table_name": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.format_table_name", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "schema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.schema", "kind": "Gdef"}, "sql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "alembic.util", "kind": "Gdef"}, "visit_rename_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.sqlite.visit_rename_table", "name": "visit_rename_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.RenameTable", "sqlalchemy.sql.compiler.DDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_rename_table", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.sqlite.visit_rename_table", "name": "visit_rename_table", "type": "alembic.util.sqla_compat._CompilerProtocol"}}}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/ddl/sqlite.py"}