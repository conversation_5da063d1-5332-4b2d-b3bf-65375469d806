{".class": "MypyFile", "_fullname": "alembic.ddl", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DefaultImpl": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.impl.DefaultImpl", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "mssql": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.mssql", "kind": "Gdef"}, "mysql": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.mysql", "kind": "Gdef"}, "oracle": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.oracle", "kind": "Gdef"}, "postgresql": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.postgresql", "kind": "Gdef"}, "sqlite": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.sqlite", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/ddl/__init__.py"}