{"data_mtime": 1751259991, "dep_lines": [57, 58, 59, 60, 23, 27, 29, 32, 41, 44, 45, 46, 47, 49, 65, 67, 70, 72, 22, 26, 43, 44, 45, 4, 6, 7, 8, 18, 43, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 25, 25, 25, 25, 5, 5, 10, 20, 20, 5, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.array", "sqlalchemy.dialects.postgresql.base", "sqlalchemy.dialects.postgresql.hstore", "sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "alembic.ddl.base", "alembic.ddl.impl", "alembic.autogenerate.render", "alembic.operations.ops", "alembic.operations.schemaobj", "alembic.operations.base", "alembic.util.sqla_compat", "sqlalchemy.sql.schema", "sqlalchemy.sql.type_api", "alembic.autogenerate.api", "alembic.runtime.migration", "sqlalchemy.types", "sqlalchemy.schema", "alembic.util", "alembic.autogenerate", "alembic.operations", "__future__", "logging", "re", "typing", "sqlalchemy", "alembic", "builtins", "abc", "alembic.ddl._autogen", "alembic.operations.batch", "alembic.runtime", "alembic.util.langhelpers", "enum", "importlib", "importlib.machinery", "sqlalchemy.dialects", "sqlalchemy.dialects.postgresql.ext", "sqlalchemy.engine", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.ddl", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "b31b327d9d31e7a5bd77afcac79403feb4fc3ad8", "id": "alembic.ddl.postgresql", "ignore_all": true, "interface_hash": "99d927e97ec238a2a158b2073e6b1ffcbdd58a80", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/ddl/postgresql.py", "plugin_data": null, "size": 29883, "suppressed": [], "version_id": "1.13.0"}