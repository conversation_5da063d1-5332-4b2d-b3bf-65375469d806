{"data_mtime": 1751259991, "dep_lines": [18, 21, 23, 26, 27, 28, 30, 33, 35, 15, 16, 22, 4, 6, 7, 13, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 25, 25, 25, 25, 10, 10, 10, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["alembic.ddl.base", "alembic.ddl.impl", "alembic.util.sqla_compat", "sqlalchemy.engine.reflection", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.schema", "sqlalchemy.sql.type_api", "alembic.operations.batch", "sqlalchemy.schema", "sqlalchemy.sql", "alembic.util", "__future__", "re", "typing", "sqlalchemy", "alembic", "builtins", "abc", "alembic.operations", "importlib", "importlib.machinery", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "fb8e1bf831e0760f266e39f5603bbec9dfc1fb4e", "id": "alembic.ddl.sqlite", "ignore_all": true, "interface_hash": "7bc03370a928ea02ecd4160316d653cbc10f3309", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/alembic/ddl/sqlite.py", "plugin_data": null, "size": 7570, "suppressed": [], "version_id": "1.13.0"}