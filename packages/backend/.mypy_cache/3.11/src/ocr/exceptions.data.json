{".class": "MypyFile", "_fullname": "src.ocr.exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BadRequestError": {".class": "SymbolTableNode", "cross_ref": "src.exceptions.BadRequestError", "kind": "Gdef"}, "ErrorCode": {".class": "SymbolTableNode", "cross_ref": "src.constants.ErrorCode", "kind": "Gdef"}, "ImageTooLarge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.BadRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.ocr.exceptions.ImageTooLarge", "name": "ImageTooLarge", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.ocr.exceptions.ImageTooLarge", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.ocr.exceptions", "mro": ["src.ocr.exceptions.ImageTooLarge", "src.exceptions.BadRequestError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.ocr.exceptions.ImageTooLarge.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.ocr.exceptions.ImageTooLarge", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImageTooLarge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.ocr.exceptions.ImageTooLarge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.ocr.exceptions.ImageTooLarge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InternalServerError": {".class": "SymbolTableNode", "cross_ref": "src.exceptions.InternalServerError", "kind": "Gdef"}, "InvalidAPIKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.BadRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.ocr.exceptions.InvalidAPIKey", "name": "InvalidAPIKey", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.ocr.exceptions.InvalidAPIKey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.ocr.exceptions", "mro": ["src.ocr.exceptions.InvalidAPIKey", "src.exceptions.BadRequestError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.ocr.exceptions.InvalidAPIKey.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.ocr.exceptions.InvalidAPIKey", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidAPIKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.ocr.exceptions.InvalidAPIKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.ocr.exceptions.InvalidAPIKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LLMAPIError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.InternalServerError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.ocr.exceptions.LLMAPIError", "name": "LLMAPIError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.ocr.exceptions.LLMAPIError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.ocr.exceptions", "mro": ["src.ocr.exceptions.LLMAPIError", "src.exceptions.InternalServerError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.ocr.exceptions.LLMAPIError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.ocr.exceptions.LLMAPIError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LLMAPIError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.ocr.exceptions.LLMAPIError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.ocr.exceptions.LLMAPIError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LLMRateLimit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.BadRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.ocr.exceptions.LLMRateLimit", "name": "LLMRateLimit", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.ocr.exceptions.LLMRateLimit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.ocr.exceptions", "mro": ["src.ocr.exceptions.LLMRateLimit", "src.exceptions.BadRequestError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.ocr.exceptions.LLMRateLimit.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.ocr.exceptions.LLMRateLimit", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LLMRateLimit", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.ocr.exceptions.LLMRateLimit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.ocr.exceptions.LLMRateLimit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotFoundError": {".class": "SymbolTableNode", "cross_ref": "src.exceptions.NotFoundError", "kind": "Gdef"}, "OCRJobNotFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.NotFoundError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.ocr.exceptions.OCRJobNotFound", "name": "OCRJobNotFound", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.ocr.exceptions.OCRJobNotFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.ocr.exceptions", "mro": ["src.ocr.exceptions.OCRJobNotFound", "src.exceptions.NotFoundError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.ocr.exceptions.OCRJobNotFound.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.ocr.exceptions.OCRJobNotFound", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OCRJobNotFound", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.ocr.exceptions.OCRJobNotFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.ocr.exceptions.OCRJobNotFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OCRProcessingFailed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.InternalServerError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.ocr.exceptions.OCRProcessingFailed", "name": "OCRProcessingFailed", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.ocr.exceptions.OCRProcessingFailed", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.ocr.exceptions", "mro": ["src.ocr.exceptions.OCRProcessingFailed", "src.exceptions.InternalServerError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.ocr.exceptions.OCRProcessingFailed.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.ocr.exceptions.OCRProcessingFailed", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OCRProcessingFailed", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.ocr.exceptions.OCRProcessingFailed.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.ocr.exceptions.OCRProcessingFailed", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedImageFormat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.BadRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.ocr.exceptions.UnsupportedImageFormat", "name": "UnsupportedImageFormat", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.ocr.exceptions.UnsupportedImageFormat", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.ocr.exceptions", "mro": ["src.ocr.exceptions.UnsupportedImageFormat", "src.exceptions.BadRequestError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.ocr.exceptions.UnsupportedImageFormat.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.ocr.exceptions.UnsupportedImageFormat", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnsupportedImageFormat", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.ocr.exceptions.UnsupportedImageFormat.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.ocr.exceptions.UnsupportedImageFormat", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ocr.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ocr.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ocr.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ocr.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ocr.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ocr.exceptions.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}}, "path": "src/ocr/exceptions.py"}