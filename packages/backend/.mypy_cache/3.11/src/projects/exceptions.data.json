{".class": "MypyFile", "_fullname": "src.projects.exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BadRequestError": {".class": "SymbolTableNode", "cross_ref": "src.exceptions.BadRequestError", "kind": "Gdef"}, "ConflictError": {".class": "SymbolTableNode", "cross_ref": "src.exceptions.ConflictError", "kind": "Gdef"}, "ErrorCode": {".class": "SymbolTableNode", "cross_ref": "src.constants.ErrorCode", "kind": "Gdef"}, "InvalidPageNumber": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.BadRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.projects.exceptions.InvalidPageNumber", "name": "InvalidPageNumber", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.projects.exceptions.InvalidPageNumber", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.projects.exceptions", "mro": ["src.projects.exceptions.InvalidPageNumber", "src.exceptions.BadRequestError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.projects.exceptions.InvalidPageNumber.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.projects.exceptions.InvalidPageNumber", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidPageNumber", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.projects.exceptions.InvalidPageNumber.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.projects.exceptions.InvalidPageNumber", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidTextRegionCoordinates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.BadRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.projects.exceptions.InvalidTextRegionCoordinates", "name": "InvalidTextRegionCoordinates", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.projects.exceptions.InvalidTextRegionCoordinates", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.projects.exceptions", "mro": ["src.projects.exceptions.InvalidTextRegionCoordinates", "src.exceptions.BadRequestError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.projects.exceptions.InvalidTextRegionCoordinates.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.projects.exceptions.InvalidTextRegionCoordinates", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidTextRegionCoordinates", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.projects.exceptions.InvalidTextRegionCoordinates.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.projects.exceptions.InvalidTextRegionCoordinates", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotFoundError": {".class": "SymbolTableNode", "cross_ref": "src.exceptions.NotFoundError", "kind": "Gdef"}, "ProjectAlreadyExists": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.ConflictError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.projects.exceptions.ProjectAlreadyExists", "name": "ProjectAlreadyExists", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.projects.exceptions.ProjectAlreadyExists", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.projects.exceptions", "mro": ["src.projects.exceptions.ProjectAlreadyExists", "src.exceptions.ConflictError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.projects.exceptions.ProjectAlreadyExists.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.projects.exceptions.ProjectAlreadyExists", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ProjectAlreadyExists", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.projects.exceptions.ProjectAlreadyExists.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.projects.exceptions.ProjectAlreadyExists", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProjectNotFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.NotFoundError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.projects.exceptions.ProjectNotFound", "name": "ProjectNotFound", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.projects.exceptions.ProjectNotFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.projects.exceptions", "mro": ["src.projects.exceptions.ProjectNotFound", "src.exceptions.NotFoundError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.projects.exceptions.ProjectNotFound.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.projects.exceptions.ProjectNotFound", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ProjectNotFound", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.projects.exceptions.ProjectNotFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.projects.exceptions.ProjectNotFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProjectPageNotFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.NotFoundError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.projects.exceptions.ProjectPageNotFound", "name": "ProjectPageNotFound", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.projects.exceptions.ProjectPageNotFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.projects.exceptions", "mro": ["src.projects.exceptions.ProjectPageNotFound", "src.exceptions.NotFoundError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.projects.exceptions.ProjectPageNotFound.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.projects.exceptions.ProjectPageNotFound", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ProjectPageNotFound", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.projects.exceptions.ProjectPageNotFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.projects.exceptions.ProjectPageNotFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextRegionNotFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.NotFoundError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.projects.exceptions.TextRegionNotFound", "name": "TextRegionNotFound", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.projects.exceptions.TextRegionNotFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.projects.exceptions", "mro": ["src.projects.exceptions.TextRegionNotFound", "src.exceptions.NotFoundError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.projects.exceptions.TextRegionNotFound.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.projects.exceptions.TextRegionNotFound", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TextRegionNotFound", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.projects.exceptions.TextRegionNotFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.projects.exceptions.TextRegionNotFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.projects.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.projects.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.projects.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.projects.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.projects.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.projects.exceptions.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}}, "path": "src/projects/exceptions.py"}