{"data_mtime": 1751259992, "dep_lines": [4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.constants", "src.exceptions", "builtins", "abc", "<PERSON><PERSON><PERSON>", "fastapi.exceptions", "importlib", "importlib.machinery", "starlette", "starlette.exceptions", "typing"], "hash": "d338a27cfb38729e393db891ef7081854755711b", "id": "src.projects.exceptions", "ignore_all": false, "interface_hash": "cf685e70ca8b7914239cb46af4fa54ca8fa10dba", "mtime": 1751253944, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "src/projects/exceptions.py", "plugin_data": null, "size": 1573, "suppressed": [], "version_id": "1.13.0"}