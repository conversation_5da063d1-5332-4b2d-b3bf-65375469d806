{".class": "MypyFile", "_fullname": "src.exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BadRequestError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.BaseHTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.exceptions.BadRequestError", "name": "BadRequestError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.exceptions.BadRequestError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.exceptions", "mro": ["src.exceptions.BadRequestError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "detail", "error_code"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.exceptions.BadRequestError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "detail", "error_code"], "arg_types": ["src.exceptions.BadRequestError", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BadRequestError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.exceptions.BadRequestError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.exceptions.BadRequestError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseHTTPException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["fastapi.exceptions.HTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.exceptions.BaseHTTPException", "name": "BaseHTTPException", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.exceptions.BaseHTTPException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.exceptions", "mro": ["src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "status_code", "detail", "error_code", "headers"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.exceptions.BaseHTTPException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "status_code", "detail", "error_code", "headers"], "arg_types": ["src.exceptions.BaseHTTPException", "builtins.int", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseHTTPException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "error_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.exceptions.BaseHTTPException.error_code", "name": "error_code", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.exceptions.BaseHTTPException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.exceptions.BaseHTTPException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConflictError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.BaseHTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.exceptions.ConflictError", "name": "ConflictError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.exceptions.ConflictError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.exceptions", "mro": ["src.exceptions.ConflictError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "detail", "error_code"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.exceptions.ConflictError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "detail", "error_code"], "arg_types": ["src.exceptions.ConflictError", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConflictError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.exceptions.ConflictError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.exceptions.ConflictError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ForbiddenError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.BaseHTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.exceptions.ForbiddenError", "name": "ForbiddenError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.exceptions.ForbiddenError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.exceptions", "mro": ["src.exceptions.ForbiddenError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "detail", "error_code"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.exceptions.ForbiddenError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "detail", "error_code"], "arg_types": ["src.exceptions.ForbiddenError", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ForbiddenError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.exceptions.ForbiddenError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.exceptions.ForbiddenError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPException": {".class": "SymbolTableNode", "cross_ref": "fastapi.exceptions.HTTPException", "kind": "Gdef"}, "InternalServerError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.BaseHTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.exceptions.InternalServerError", "name": "InternalServerError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.exceptions.InternalServerError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.exceptions", "mro": ["src.exceptions.InternalServerError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "detail", "error_code"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.exceptions.InternalServerError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "detail", "error_code"], "arg_types": ["src.exceptions.InternalServerError", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InternalServerError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.exceptions.InternalServerError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.exceptions.InternalServerError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotFoundError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.BaseHTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.exceptions.NotFoundError", "name": "NotFoundError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.exceptions.NotFoundError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.exceptions", "mro": ["src.exceptions.NotFoundError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "detail", "error_code"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.exceptions.NotFoundError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "detail", "error_code"], "arg_types": ["src.exceptions.NotFoundError", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NotFoundError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.exceptions.NotFoundError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.exceptions.NotFoundError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "UnauthorizedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.BaseHTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.exceptions.UnauthorizedError", "name": "UnauthorizedError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.exceptions.UnauthorizedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.exceptions", "mro": ["src.exceptions.UnauthorizedError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "detail", "error_code"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.exceptions.UnauthorizedError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "detail", "error_code"], "arg_types": ["src.exceptions.UnauthorizedError", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnauthorizedError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.exceptions.UnauthorizedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.exceptions.UnauthorizedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ValidationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.BaseHTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.exceptions.ValidationError", "name": "ValidationError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.exceptions.ValidationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.exceptions", "mro": ["src.exceptions.ValidationError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "detail", "error_code"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.exceptions.ValidationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "detail", "error_code"], "arg_types": ["src.exceptions.ValidationError", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValidationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.exceptions.ValidationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.exceptions.ValidationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.exceptions.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "status": {".class": "SymbolTableNode", "cross_ref": "starlette.status", "kind": "Gdef"}}, "path": "src/exceptions.py"}