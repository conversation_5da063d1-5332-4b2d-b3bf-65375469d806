{".class": "MypyFile", "_fullname": "src.translation.exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BadRequestError": {".class": "SymbolTableNode", "cross_ref": "src.exceptions.BadRequestError", "kind": "Gdef"}, "ErrorCode": {".class": "SymbolTableNode", "cross_ref": "src.constants.ErrorCode", "kind": "Gdef"}, "InternalServerError": {".class": "SymbolTableNode", "cross_ref": "src.exceptions.InternalServerError", "kind": "Gdef"}, "NotFoundError": {".class": "SymbolTableNode", "cross_ref": "src.exceptions.NotFoundError", "kind": "Gdef"}, "TranslationAlternativeNotFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.NotFoundError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.translation.exceptions.TranslationAlternativeNotFound", "name": "TranslationAlternativeNotFound", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.translation.exceptions.TranslationAlternativeNotFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.translation.exceptions", "mro": ["src.translation.exceptions.TranslationAlternativeNotFound", "src.exceptions.NotFoundError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.translation.exceptions.TranslationAlternativeNotFound.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.translation.exceptions.TranslationAlternativeNotFound", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TranslationAlternativeNotFound", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.translation.exceptions.TranslationAlternativeNotFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.translation.exceptions.TranslationAlternativeNotFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TranslationFailed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.InternalServerError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.translation.exceptions.TranslationFailed", "name": "TranslationFailed", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.translation.exceptions.TranslationFailed", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.translation.exceptions", "mro": ["src.translation.exceptions.TranslationFailed", "src.exceptions.InternalServerError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.translation.exceptions.TranslationFailed.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.translation.exceptions.TranslationFailed", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TranslationFailed", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.translation.exceptions.TranslationFailed.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.translation.exceptions.TranslationFailed", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TranslationJobNotFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.NotFoundError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.translation.exceptions.TranslationJobNotFound", "name": "TranslationJobNotFound", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.translation.exceptions.TranslationJobNotFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.translation.exceptions", "mro": ["src.translation.exceptions.TranslationJobNotFound", "src.exceptions.NotFoundError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.translation.exceptions.TranslationJobNotFound.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.translation.exceptions.TranslationJobNotFound", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TranslationJobNotFound", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.translation.exceptions.TranslationJobNotFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.translation.exceptions.TranslationJobNotFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TranslationTemplateNotFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.NotFoundError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.translation.exceptions.TranslationTemplateNotFound", "name": "TranslationTemplateNotFound", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.translation.exceptions.TranslationTemplateNotFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.translation.exceptions", "mro": ["src.translation.exceptions.TranslationTemplateNotFound", "src.exceptions.NotFoundError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.translation.exceptions.TranslationTemplateNotFound.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.translation.exceptions.TranslationTemplateNotFound", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TranslationTemplateNotFound", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.translation.exceptions.TranslationTemplateNotFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.translation.exceptions.TranslationTemplateNotFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedLanguagePair": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.exceptions.BadRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.translation.exceptions.UnsupportedLanguagePair", "name": "UnsupportedLanguagePair", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "src.translation.exceptions.UnsupportedLanguagePair", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.translation.exceptions", "mro": ["src.translation.exceptions.UnsupportedLanguagePair", "src.exceptions.BadRequestError", "src.exceptions.BaseHTTPException", "fastapi.exceptions.HTTPException", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "dataclass_transform_spec": null, "flags": [], "fullname": "src.translation.exceptions.UnsupportedLanguagePair.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "detail"], "arg_types": ["src.translation.exceptions.UnsupportedLanguagePair", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnsupportedLanguagePair", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.translation.exceptions.UnsupportedLanguagePair.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.translation.exceptions.UnsupportedLanguagePair", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.translation.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.translation.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.translation.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.translation.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.translation.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.translation.exceptions.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}}, "path": "src/translation/exceptions.py"}