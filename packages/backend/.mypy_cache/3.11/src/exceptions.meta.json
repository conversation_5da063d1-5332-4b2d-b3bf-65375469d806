{"data_mtime": 1751259992, "dep_lines": [4, 6, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["typing", "<PERSON><PERSON><PERSON>", "builtins", "abc", "fastapi.exceptions", "importlib", "importlib.machinery", "starlette", "starlette.exceptions", "starlette.status"], "hash": "638908142122c6625ce2aef7fbd3b33523691151", "id": "src.exceptions", "ignore_all": false, "interface_hash": "82622c7f02df8eab616933cd720ddccc4b0b9793", "mtime": 1751253829, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "src/exceptions.py", "plugin_data": null, "size": 2807, "suppressed": [], "version_id": "1.13.0"}