{"data_mtime": 1751259988, "dep_lines": [15, 16, 17, 1, 3, 4, 5, 6, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["anyio._core._testing", "anyio.abc", "anyio.lowlevel", "__future__", "warnings", "collections", "dataclasses", "types", "typing", "anyio", "builtins", "_collections_abc", "_typeshed", "abc", "anyio._core", "anyio._core._exceptions", "anyio._core._synchronization", "anyio._core._typedattr", "anyio.abc._resources", "anyio.abc._streams", "importlib", "importlib.machinery"], "hash": "f0899c30a8dcb1e031ce2dc0ca060f118c807317", "id": "anyio.streams.memory", "ignore_all": true, "interface_hash": "c280e8a5b75e0e1a31d10aaba7c54f9af1470f5d", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/anyio/streams/memory.py", "plugin_data": null, "size": 10560, "suppressed": [], "version_id": "1.13.0"}