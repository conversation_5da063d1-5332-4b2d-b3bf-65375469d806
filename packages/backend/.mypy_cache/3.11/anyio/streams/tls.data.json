{".class": "MypyFile", "_fullname": "anyio.streams.tls", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyByteStream": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._streams.AnyByteStream", "kind": "Gdef"}, "BrokenResourceError": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.BrokenResourceError", "kind": "Gdef"}, "ByteStream": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._streams.ByteStream", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "EndOfStream": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.EndOfStream", "kind": "Gdef"}, "Listener": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._streams.Listener", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "PosArgsT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarTupleExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.tls.PosArgsT", "name": "PosArgsT", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "variance": 0}}, "TLSAttribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["anyio._core._typedattr.TypedAttributeSet"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.streams.tls.TLSAttribute", "name": "TLSAttribute", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "anyio.streams.tls.TLSAttribute", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "anyio.streams.tls", "mro": ["anyio.streams.tls.TLSAttribute", "anyio._core._typedattr.TypedAttributeSet", "builtins.object"], "names": {".class": "SymbolTable", "alpn_protocol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.tls.TLSAttribute.alpn_protocol", "name": "alpn_protocol", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "channel_binding_tls_unique": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.tls.TLSAttribute.channel_binding_tls_unique", "name": "channel_binding_tls_unique", "type": "builtins.bytes"}}, "cipher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.tls.TLSAttribute.cipher", "name": "cipher", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "peer_certificate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.tls.TLSAttribute.peer_certificate", "name": "peer_certificate", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "anyio.streams.tls._PCTRTTT"}, {".class": "TypeAliasType", "args": [], "type_ref": "anyio.streams.tls._PCTRTT"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}}}, "peer_certificate_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.tls.TLSAttribute.peer_certificate_binary", "name": "peer_certificate_binary", "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "server_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.tls.TLSAttribute.server_side", "name": "server_side", "type": "builtins.bool"}}, "shared_ciphers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.tls.TLSAttribute.shared_ciphers", "name": "shared_ciphers", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "ssl_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.tls.TLSAttribute.ssl_object", "name": "ssl_object", "type": "ssl.SSLObject"}}, "standard_compatible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.tls.TLSAttribute.standard_compatible", "name": "standard_compatible", "type": "builtins.bool"}}, "tls_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.tls.TLSAttribute.tls_version", "name": "tls_version", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.tls.TLSAttribute.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.streams.tls.TLSAttribute", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TLSListener": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["anyio.streams.tls.TLSStream"], "extra_attrs": null, "type_ref": "anyio.abc._streams.Listener"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.streams.tls.TLSListener", "name": "TLSListener", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "anyio.streams.tls.TLSListener", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 285, "name": "listener", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "anyio.abc._streams.Listener"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 286, "name": "ssl_context", "type": "ssl.SSLContext"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 287, "name": "standard_compatible", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 288, "name": "handshake_timeout", "type": "builtins.float"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "anyio.streams.tls", "mro": ["anyio.streams.tls.TLSListener", "anyio.abc._streams.Listener", "anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "anyio.streams.tls.TLSListener.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "listener", "ssl_context", "standard_compatible", "handshake_timeout"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.tls.TLSListener.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "listener", "ssl_context", "standard_compatible", "handshake_timeout"], "arg_types": ["anyio.streams.tls.TLSListener", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "anyio.abc._streams.Listener"}, "ssl.SSLContext", "builtins.bool", "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TLSListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "anyio.streams.tls.TLSListener.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "listener"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ssl_context"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "standard_compatible"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "handshake_timeout"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["listener", "ssl_context", "standard_compatible", "handshake_timeout"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "anyio.streams.tls.TLSListener.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["listener", "ssl_context", "standard_compatible", "handshake_timeout"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "anyio.abc._streams.Listener"}, "ssl.SSLContext", "builtins.bool", "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TLSListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "anyio.streams.tls.TLSListener.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["listener", "ssl_context", "standard_compatible", "handshake_timeout"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "anyio.abc._streams.Listener"}, "ssl.SSLContext", "builtins.bool", "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TLSListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "aclose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio.streams.tls.TLSListener.aclose", "name": "aclose", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.streams.tls.TLSListener"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aclose of TLSListener", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extra_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "anyio.streams.tls.TLSListener.extra_attributes", "name": "extra_attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.streams.tls.TLSListener"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extra_attributes of TLSListener", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "anyio.streams.tls.TLSListener.extra_attributes", "name": "extra_attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.streams.tls.TLSListener"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extra_attributes of TLSListener", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "handle_handshake_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["exc", "stream"], "dataclass_transform_spec": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "anyio.streams.tls.TLSListener.handle_handshake_error", "name": "handle_handshake_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["exc", "stream"], "arg_types": ["builtins.BaseException", {".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._streams.AnyByteStream"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_handshake_error of TLSListener", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "anyio.streams.tls.TLSListener.handle_handshake_error", "name": "handle_handshake_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["exc", "stream"], "arg_types": ["builtins.BaseException", {".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._streams.AnyByteStream"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_handshake_error of TLSListener", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "handshake_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.tls.TLSListener.handshake_timeout", "name": "handshake_timeout", "type": "builtins.float"}}, "listener": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "anyio.streams.tls.TLSListener.listener", "name": "listener", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "anyio.abc._streams.Listener"}}}, "serve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "handler", "task_group"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio.streams.tls.TLSListener.serve", "name": "serve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "handler", "task_group"], "arg_types": ["anyio.streams.tls.TLSListener", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["anyio.streams.tls.TLSStream"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["anyio.abc._tasks.TaskGroup", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serve of TLSListener", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ssl_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "anyio.streams.tls.TLSListener.ssl_context", "name": "ssl_context", "type": "ssl.SSLContext"}}, "standard_compatible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.streams.tls.TLSListener.standard_compatible", "name": "standard_compatible", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.tls.TLSListener.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.streams.tls.TLSListener", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TLSStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["anyio.abc._streams.ByteStream"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.streams.tls.TLSStream", "name": "TLSStream", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "anyio.streams.tls.TLSStream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 73, "name": "transport_stream", "type": {".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._streams.AnyByteStream"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 74, "name": "standard_compatible", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 75, "name": "_ssl_object", "type": "ssl.SSLObject"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 76, "name": "_read_bio", "type": "ssl.MemoryBIO"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 77, "name": "_write_bio", "type": "ssl.MemoryBIO"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "anyio.streams.tls", "mro": ["anyio.streams.tls.TLSStream", "anyio.abc._streams.ByteStream", "anyio.abc._streams.ByteReceiveStream", "anyio.abc._streams.ByteSendStream", "anyio.abc._resources.AsyncResource", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "anyio.streams.tls.TLSStream.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "transport_stream", "standard_compatible", "_ssl_object", "_read_bio", "_write_bio"], "dataclass_transform_spec": null, "flags": [], "fullname": "anyio.streams.tls.TLSStream.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "transport_stream", "standard_compatible", "_ssl_object", "_read_bio", "_write_bio"], "arg_types": ["anyio.streams.tls.TLSStream", {".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._streams.AnyByteStream"}, "builtins.bool", "ssl.SSLObject", "ssl.MemoryBIO", "ssl.MemoryBIO"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TLSStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "anyio.streams.tls.TLSStream.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "transport_stream"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "standard_compatible"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "_ssl_object"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "_read_bio"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "_write_bio"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["transport_stream", "standard_compatible", "_ssl_object", "_read_bio", "_write_bio"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "anyio.streams.tls.TLSStream.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["transport_stream", "standard_compatible", "_ssl_object", "_read_bio", "_write_bio"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._streams.AnyByteStream"}, "builtins.bool", "ssl.SSLObject", "ssl.MemoryBIO", "ssl.MemoryBIO"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TLSStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "anyio.streams.tls.TLSStream.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["transport_stream", "standard_compatible", "_ssl_object", "_read_bio", "_write_bio"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._streams.AnyByteStream"}, "builtins.bool", "ssl.SSLObject", "ssl.MemoryBIO", "ssl.MemoryBIO"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TLSStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "_call_sslobject_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "func", "args"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio.streams.tls.TLSStream._call_sslobject_method", "name": "_call_sslobject_method", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "func", "args"], "arg_types": ["anyio.streams.tls.TLSStream", {".class": "CallableType", "arg_kinds": [2], "arg_names": [null], "arg_types": [{".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.tls.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "namespace": "anyio.streams.tls.TLSStream._call_sslobject_method", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.tls.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "anyio.streams.tls.TLSStream._call_sslobject_method", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.tls.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "namespace": "anyio.streams.tls.TLSStream._call_sslobject_method", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_call_sslobject_method of TLSStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.tls.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "anyio.streams.tls.TLSStream._call_sslobject_method", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.tls.PosArgsT", "id": -1, "min_len": 0, "name": "PosArgsT", "namespace": "anyio.streams.tls.TLSStream._call_sslobject_method", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.tls.T_Retval", "id": -2, "name": "T_<PERSON><PERSON><PERSON>", "namespace": "anyio.streams.tls.TLSStream._call_sslobject_method", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_read_bio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "anyio.streams.tls.TLSStream._read_bio", "name": "_read_bio", "type": "ssl.MemoryBIO"}}, "_ssl_object": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "anyio.streams.tls.TLSStream._ssl_object", "name": "_ssl_object", "type": "ssl.SSLObject"}}, "_write_bio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "anyio.streams.tls.TLSStream._write_bio", "name": "_write_bio", "type": "ssl.MemoryBIO"}}, "aclose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio.streams.tls.TLSStream.aclose", "name": "aclose", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.streams.tls.TLSStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aclose of TLSStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extra_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "anyio.streams.tls.TLSStream.extra_attributes", "name": "extra_attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.streams.tls.TLSStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extra_attributes of TLSStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "anyio.streams.tls.TLSStream.extra_attributes", "name": "extra_attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.streams.tls.TLSStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extra_attributes of TLSStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "max_bytes"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio.streams.tls.TLSStream.receive", "name": "receive", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "max_bytes"], "arg_types": ["anyio.streams.tls.TLSStream", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive of TLSStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio.streams.tls.TLSStream.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["anyio.streams.tls.TLSStream", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of TLSStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio.streams.tls.TLSStream.send_eof", "name": "send_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.streams.tls.TLSStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_eof of TLSStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "standard_compatible": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "anyio.streams.tls.TLSStream.standard_compatible", "name": "standard_compatible", "type": "builtins.bool"}}, "transport_stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "anyio.streams.tls.TLSStream.transport_stream", "name": "transport_stream", "type": {".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._streams.AnyByteStream"}}}, "unwrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_coroutine"], "fullname": "anyio.streams.tls.TLSStream.unwrap", "name": "unwrap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.streams.tls.TLSStream"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unwrap of TLSStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._streams.AnyByteStream"}, "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "wrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "transport_stream", "server_side", "hostname", "ssl_context", "standard_compatible"], "dataclass_transform_spec": null, "flags": ["is_class", "is_coroutine", "is_decorated"], "fullname": "anyio.streams.tls.TLSStream.wrap", "name": "wrap", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "transport_stream", "server_side", "hostname", "ssl_context", "standard_compatible"], "arg_types": [{".class": "TypeType", "item": "anyio.streams.tls.TLSStream"}, {".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._streams.AnyByteStream"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap of TLSStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.streams.tls.TLSStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "anyio.streams.tls.TLSStream.wrap", "name": "wrap", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "transport_stream", "server_side", "hostname", "ssl_context", "standard_compatible"], "arg_types": [{".class": "TypeType", "item": "anyio.streams.tls.TLSStream"}, {".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._streams.AnyByteStream"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap of TLSStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.streams.tls.TLSStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.tls.TLSStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.streams.tls.TLSStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "T_Retval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.streams.tls.T_Retval", "name": "T_<PERSON><PERSON><PERSON>", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TaskGroup": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._tasks.TaskGroup", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypeVarTuple": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVarTuple", "kind": "Gdef"}, "TypedAttributeSet": {".class": "SymbolTableNode", "cross_ref": "anyio._core._typedattr.TypedAttributeSet", "kind": "Gdef"}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing.Unpack", "kind": "Gdef"}, "_PCTRTT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio.streams.tls._PCTRTT", "line": 29, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_PCTRTTT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio.streams.tls._PCTRTTT", "line": 30, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.streams.tls._PCTRTT"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.streams.tls.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.streams.tls.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.streams.tls.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.streams.tls.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.streams.tls.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.streams.tls.__spec__", "name": "__spec__", "type": "importlib.machinery.ModuleSpec"}}, "aclose_forcefully": {".class": "SymbolTableNode", "cross_ref": "anyio._core._resources.aclose_forcefully", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "get_cancelled_exc_class": {".class": "SymbolTableNode", "cross_ref": "anyio._core._eventloop.get_cancelled_exc_class", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "to_thread": {".class": "SymbolTableNode", "cross_ref": "anyio.to_thread", "kind": "Gdef"}, "typed_attribute": {".class": "SymbolTableNode", "cross_ref": "anyio._core._typedattr.typed_attribute", "kind": "Gdef"}, "wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/anyio/streams/tls.py"}