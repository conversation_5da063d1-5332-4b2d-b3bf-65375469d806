{"data_mtime": 1751259988, "dep_lines": [27, 28, 29, 30, 31, 32, 9, 15, 16, 1, 3, 4, 5, 6, 7, 8, 10, 13, 15, 35, 43, 578, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 10, 5, 5, 10, 10, 10, 5, 5, 20, 25, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["anyio.streams.stapled", "anyio.streams.tls", "anyio._core._eventloop", "anyio._core._resources", "anyio._core._synchronization", "anyio._core._tasks", "collections.abc", "anyio.to_thread", "anyio.abc", "__future__", "errno", "os", "socket", "ssl", "stat", "sys", "ipaddress", "typing", "anyio", "_typeshed", "typing_extensions", "idna", "builtins", "_socket", "abc", "anyio._core._typedattr", "anyio.abc._resources", "anyio.abc._sockets", "anyio.abc._streams", "anyio.streams", "enum", "importlib", "importlib.machinery", "types"], "hash": "be9dba53fcd8e7e241be0141cf0eeef9b8af9301", "id": "anyio._core._sockets", "ignore_all": true, "interface_hash": "3770435246b026967ef479fcb9bbde4d8265038e", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/anyio/_core/_sockets.py", "plugin_data": null, "size": 27150, "suppressed": [], "version_id": "1.13.0"}