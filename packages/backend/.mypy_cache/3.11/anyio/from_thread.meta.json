{"data_mtime": 1751259988, "dep_lines": [23, 25, 26, 28, 4, 5, 23, 27, 1, 3, 6, 11, 12, 13, 14, 15, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["anyio._core._eventloop", "anyio._core._synchronization", "anyio._core._tasks", "anyio.abc._tasks", "collections.abc", "concurrent.futures", "anyio._core", "anyio.abc", "__future__", "sys", "contextlib", "dataclasses", "inspect", "threading", "types", "typing", "builtins", "_thread", "_typeshed", "abc", "concurrent", "concurrent.futures._base", "importlib", "importlib.machinery"], "hash": "348513ae481f3e6cba9e6fc67712c0cb0e4f92fe", "id": "anyio.from_thread", "ignore_all": true, "interface_hash": "22da97fe4ce675b6fcb6ccde3a3e0ff54e7ec681", "mtime": 1751256098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/projects/me/ho-trans/packages/backend/.venv/lib/python3.11/site-packages/anyio/from_thread.py", "plugin_data": null, "size": 17478, "suppressed": [], "version_id": "1.13.0"}